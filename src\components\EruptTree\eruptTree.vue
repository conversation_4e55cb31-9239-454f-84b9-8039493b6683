<template>
  <div class="h-full flex flex-col bg-white">
    <vxe-toolbar ref="toolbarRef" :refresh="{ queryMethod: search }">
      <template #buttons>
        <div
          class="vxe-buttons--wrapper"
          style="margin-left: 2%; display: flex; flex-direction: row"
        >
          <vxe-input v-model="searchVal" size="small" placeholder="搜索" clearable>
            <template #prefix>
              <i class="vxe-icon-search my-red" @click="onSearch"></i>
            </template>
          </vxe-input>
          <vxe-button
            circle
            icon="vxe-icon-add"
            v-if="powerRef.add"
            @click="insertEvent"
          ></vxe-button>
        </div>
      </template>
      <template #tools> </template>
    </vxe-toolbar>

    <vxe-table
      show-overflow
      ref="xTreeRef"
      border="inner"
      :row-config="{ isHover: true, useKey: true, isCurrent: false }"
      :toolbarConfig="toolbarConfig"
      :show-header="false"
      :data="tableData"
      :checkbox-config="{ labelField: 'title' }"
      :tree-config="{
        transform: false,
        line: true,
        iconOpen: 'vxe-icon-square-minus-fill',
        iconClose: 'vxe-icon-square-plus-fill',
        expandAll: true,
      }"
    >
      <vxe-column tree-node>
        <template #default="{ row }">
          <span>
            <template v-if="row.children && row.children.length">
              <i
                class="tree-node-icon"
                :class="hasRowExpand(row) ? 'vxe-icon-folder-open' : 'vxe-icon-folder'"
              ></i>
            </template>
            <template v-else>
              <i class="tree-node-icon vxe-icon-file-txt"></i>
            </template>
            <span :style="{ color: row.selected ? 'red' : 'black' }">{{ row.title }}</span>
          </span>
        </template>
      </vxe-column>
      <vxe-column title="操作" width="300">
        <template #default="{ row }">
          <vxe-button type="text" @click="getDetail(row.id)">详情</vxe-button>
          <vxe-button type="text" @click="addNext(row)">增加下级</vxe-button>
          <vxe-button type="text" @click="edit(row.id)" v-if="powerRef.edit">编辑</vxe-button>
          <vxe-button type="text" style="color: red" v-if="powerRef.delete" @click="deleted(row)"
            >删除</vxe-button
          >
        </template>
      </vxe-column>
    </vxe-table>
    <BasicDrawer
      @register="registerDrawer"
      :showFooter="showFooter"
      :title="modalTitle"
      :width="drawerWidth"
      @ok="handleSubmit"
      @close="handleCancel"
    >
      <BasicForm @register="registerForm"></BasicForm>
    </BasicDrawer>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    PropType,
    ref,
    onMounted,
    watchEffect,
    computed,
    unref,
    watch,
    h,
  } from 'vue';
  import { VXETable, VxeTableInstance } from 'vxe-table';
  import { useDrawer } from '/@/components/Drawer';
  import {
    batchRemove,
    buildApi,
    getOne,
    remove,
    save,
    treeQueryApi,
    update,
  } from '/@/api/erupt/erupt';
  import {
    getColFomatter,
    getComponent,
    getComponentProps,
  } from '/@/components/EruptTable/componets';
  import { useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';

  export default defineComponent({
    name: 'EruptTree',
    props: {
      className: {},
      overwriteAction: { add: {}, edit: {}, detail: {}, delete: {}, batchDelete: {} },
      formDynamicControl: { type: Object, default: {} },
    },
    components: { BasicForm, BasicDrawer, VXETable },
    setup(props, { attrs, emit }) {
      const { createMessage } = useMessage();

      interface RowVO {
        id: string;
        pid: string | null;
        title: string;
      }

      const { className, overwriteAction, formDynamicControl } = props;
      const tabs = ref([]);
      const powerRef = ref({});
      const tabValues = ref({});
      const searchVal = ref('');
      const uploadModalRef = ref();
      const columns = ref([]);
      const modalTitle = ref('');
      const showFooter = ref(true);
      const base = ref({ id: '', version: '' });
      const [registerDrawer, { openDrawer, closeDrawer, setDrawerProps }] = useDrawer();
      const addSchema = ref([]);
      const editSchema = ref([]);
      const lookSchema = ref([]);
      const xTreeRef = ref<VxeTableInstance<RowVO>>();
      const tableData = ref<RowVO[]>([]);
      const drawerWidth = ref('');
      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        schemas: editSchema,
        showActionButtonGroup: false,
      });
      const toolbarConfig = ref({
        buttons: [],
      });

      function refresh() {
        search();
      }
      function onSearch() {
        let elements = [];
        const $table = xTreeRef.value;
        deepTree(tableData.value, searchVal.value);
        $table.setCurrentRow(elements);
      }

      watch(
        () => searchVal.value,
        (v) => {
          deepTree(tableData.value, v);
        },
      );
      function deepTree(treeList, key) {
        for (let index = 0; index < treeList.length; index++) {
          const element = treeList[index];
          if (key && element.title.includes(key)) {
            element.selected = true;
          } else {
            element.selected = false;
          }
          if (element.children && element.children.length) {
            deepTree(element.children, key);
          }
        }
        console.log(treeList);
      }

      function handleCancel() {
        resetFields();
      }

      function search() {
        treeQueryApi(className).then((res) => {
          const $table = xTreeRef.value;
          tableData.value = res;
          setTimeout(() => {
            $table?.setAllTreeExpand(true);
          }, 30);
        });
      }

      function insertEvent() {
        if (overwriteAction && overwriteAction.add) {
          overwriteAction.add();
        } else {
          modalTitle.value = '新增';
          showFooter.value = true;
          openDrawer();
          base.value.id = '';
          base.value.version = '';
          updateSchema(addSchema.value);
          resetFields();
          for (let tabValuesKey in tabValues.value) {
            tabValues.value[tabValuesKey] = [];
          }
        }
      }

      async function addNext(row) {
        modalTitle.value = '增加下级';
        showFooter.value = true;
        openDrawer();
        const res = await getOne(className, row.id);
        updateSchema(addSchema.value);
        setFieldsValue({ parent: res });
      }
      const hasRowExpand = (row: RowVO) => {
        const $table = xTreeRef.value;
        console.log($table);
        if ($table) {
          return $table.isTreeExpandByRow(row);
        }
        return false;
      };

      async function handleSubmit() {
        debugger;
        try {
          let values = await validate();

          setDrawerProps({ confirmLoading: true });
          if (base.value.id) {
            const data = { ...values, ...base.value, ...tabValues.value };
            const { status, message } = await update(className, data, null);
            if (status == 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          } else {
            const data = { ...values, ...tabValues.value };
            const { status, message } = await save(className, data, null);
            if (status == 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          }
          // TODO custom api

          closeDrawer();
          emit('success');
        } finally {
          setDrawerProps({ confirmLoading: false });
        }
      }

      async function getDetail(id) {
        modalTitle.value = '详情';
        showFooter.value = false;
        openDrawer();
        await getOne(className, id).then((res) => {
          updateSchema(lookSchema.value);
          setFieldsValue(res);
          let tabV = {};
          for (let key in res) {
            tabs.value.forEach((item) => {
              if (item.key == key) {
                tabV[key] = res[key];
              }
            });
          }
          tabValues.value = tabV;
        });
      }

      async function edit(id) {
        modalTitle.value = '编辑';
        showFooter.value = true;
        openDrawer();
        const res = await getOne(className, id);
        base.value.id = res.id;
        base.value.version = res.version;
        updateSchema(editSchema.value);
        setFieldsValue(res);
        let tabV = {};
        for (let key in res) {
          tabs.value.forEach((item) => {
            if (item.key == key) {
              tabV[key] = res[key] ? res[key] : [];
            }
          });
        }
        tabValues.value = tabV;
      }

      async function deleted(row) {
        if (row.children && row.children.length > 0) {
          createMessage.error('存在叶节点的节点不允许直接删除！');
          return;
        }
        const { status, message } = await remove(className, row.id);
        if (status == 'SUCCESS') {
          createMessage.success('删除成功');
          refresh();
        } else {
          createMessage.error('删除失败:' + message);
        }
      }

      async function handleBatchRemove() {
        const selectedRows = tableRef.value.getCheckboxRecords();
        let ids = [];
        selectedRows.forEach((item) => {
          ids.push(item.id);
        });
        const { status, message } = await batchRemove(className, ids);
        if (status == 'SUCCESS') {
          createMessage.success('删除成功');
          refresh();
        } else {
          createMessage.error('删除失败' + message);
        }
      }

      onMounted(() => {
        search();
        buildApi(className).then((res) => {
          const {
            eruptModel: { eruptFieldModels, eruptJson },
            tabErupts,
            power,
          } = res;
          let cols = [{ type: 'checkbox' }];
          let qs = [];
          let edits = [];
          let adds = [];
          let details = [];
          let tabItems = [];
          let buttons = [];
          drawerWidth.value = eruptJson.drawerWidth;
          powerRef.value = power;
          let tabV = {};
          eruptFieldModels.forEach((item) => {
            const key = item.fieldName;
            const title = item.eruptFieldJson.edit.title;
            item.eruptFieldJson.views.forEach((v) => {
              if (v.show) {
                cols.push(getColFomatter(key, v, item.eruptFieldJson.edit));
              }
            });
            //  formState[key] = null
            if (item.eruptFieldJson.edit.show.edit_show && key !== 'id') {
              if (
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
                item.eruptFieldJson.edit.type !== 'TAB_TREE'
              ) {
                const e = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, className),
                      disabled: item.eruptFieldJson.edit.readOnly.edit,
                      onChange: (e) =>
                        formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {},
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };
                const a = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, className),
                      disabled: item.eruptFieldJson.edit.readOnly.add,
                      onChange: (e) =>
                        formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {},
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };
                const d = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, className),
                      disabled: true,
                      onChange: (e) =>
                        formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {},
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };
                edits.push(e);
                adds.push(a);
                details.push(d);
              } else {
                if (tabErupts) {
                  for (let tabEruptsKey in tabErupts) {
                    if (item.fieldName == tabEruptsKey) {
                      tabErupts[tabEruptsKey].title = item.eruptFieldJson.edit.title;
                      tabErupts[tabEruptsKey].key = tabEruptsKey;
                      tabErupts[tabEruptsKey].type = item.eruptFieldJson.edit.type;
                      tabItems.push(tabErupts[tabEruptsKey]);
                      tabV[tabEruptsKey] = [];
                    }
                  }
                }
              }
            }
          });
          tabs.value = tabItems;
          tabValues.value = tabV;
          console.log('tabs', tabs.value, tabValues.value);

          cols.push({
            width: 160,
            title: '操作',
            align: 'center',
            slots: { default: 'action' },
            fixed: 'right',
          });
          columns.value = cols;
          editSchema.value = edits;
          addSchema.value = adds;
          lookSchema.value = details;
          toolbarConfig.value.buttons = buttons;
        });
      });
      return {
        xTreeRef,
        tableData,
        columns,
        toolbarConfig,
        registerDrawer,
        openDrawer,
        closeDrawer,
        registerForm,
        modalTitle,
        showFooter,
        handleSubmit,
        setDrawerProps,
        getDetail,
        edit,
        deleted,
        searchVal,
        onSearch,
        handleCancel,
        uploadModalRef,
        tabs,
        tabValues,
        powerRef,
        search,
        insertEvent,
        hasRowExpand,
        addNext,
        drawerWidth,
      };
    },
  });
</script>
