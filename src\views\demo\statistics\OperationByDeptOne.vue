<template>
  <Card title="运营筹备部门完成率" :loading="loading">
    <template #extra>
      <div style="height: 32px" />
      <a-space direction="horizontal">
        <a-select
          v-model="line"
          style="width: 200px"
          placeholder="请选择线路"
          :options="lineOpt"
          @change="setLine"
          :loading="loading"
          :disabled="loading"
          allowClear
        ></a-select>
      </a-space>
    </template>
    <div v-show="isShow > 0" v-loading="loadings" ref="chartRef" :style="{ width, height }"></div>
    <div v-show="isShow <= 0" style="text-align: center; margin-top: 100px; height: 300px">
      <a-empty />
    </div>
  </Card>
</template>

<script setup lang="ts">
  import { Card } from 'ant-design-vue';
  import { Ref, ref, watch } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { useUserStore } from '/@/store/modules/user';
  import { getOperationByDept } from '/@/views/dashboard/workbench/components/api/home';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    loading: Boolean,
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '400px',
    },
    lineOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    deptOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });
  const line = ref('');
  const isShow = ref(0);
  const dept = ref('');
  const deptLabel = ref('');
  const loadings = ref(true);
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const userStore = useUserStore();
  const router = useRouter();

  const setLine = (val) => {
    loadings.value = true;
    line.value = val;
    getChartDate();
  };
  const setDept = (val) => {
    loadings.value = true;
    const selectedOption = props.deptOpt.find((option) => option.value === val);
    if (selectedOption) {
      deptLabel.value = selectedOption.label; // 假设你有一个变量 lineLabel 来保存 label
    }
    dept.value = val;
    getChartDate();
  };

  watch(
    () => props.loading,
    () => {
      if (props.loading) {
        return;
      }
      getChartDate();
    },
    { immediate: true },
  );

  const getChartDate = async () => {
    const data = {
      line: line.value,
      dept: dept.value,
    };
    try {
      const res = await getOperationByDept(data);
      updateChart(res);
      isShow.value = res.dept.length - 1;
      loadings.value = false;
    } catch (error) {
      console.log(error);
    }
  };

  function updateChart(value) {
    setOptions({
      xAxis: {
        type: 'category',
        data: value.dept,
        name: '责任单位',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#000',
          fontSize: 14,
        },
        axisLabel: {
          /*formatter: function (value) {
            return value.length > 3 ? value.slice(0, 3) + '...' : value;
          },*/
          formatter: function (value) {
            return value.length > 8 ? value.slice(0, 8) + '\n' : value;
          },
        },
      },
      yAxis: {
        name: '完成率', // 设置y轴名称
        nameLocation: 'end', // 名称显示的位置
        nameGap: 21, // 调整名称与轴线之间的距离
        nameTextStyle: {
          color: '#333',
          fontSize: 14,
          fontWeight: 'bold',
          backgroundColor: '#f8f8f8', // 添加背景色
          borderColor: '#d9d9d9', // 边框颜色
          borderWidth: 1, // 边框宽度
          borderRadius: 4, // 圆角边框
          padding: [4, 8], // 内边距
        },
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
        axisLabel: {
          formatter: function (value) {
            return value + '%';
          },
        },
      },
      grid: {
        left: '10%', // 左边距
        right: '12%', // 右边距
        top: '10%',
        bottom: '10%',
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          const index = params.dataIndex;
          const deptName = value.dept[index];
          const uncompletedRate = params.value; // 当前是未完成率
          const completedRate = 100 - uncompletedRate; // 计算完成率

          // 获取完成数量和总数量
          const completed = value.completed ? value.completed[index] : 0;
          const total = value.total ? value.total[index] : 0;
          const lineLabel = total - completed;
          return `
            ${deptName}<br/>
            完成率: ${uncompletedRate.toFixed(2)}%<br/>
            完成数量: ${lineLabel}<br/>
            总数量: ${total}
          `;
        },
      },
      series: [
        {
          name: '完成率',
          type: 'bar', // 显示柱状图
          data: value.data, // 根据选中的线路获取数据
          barWidth: '30%',
          itemStyle: {
            color: function (params) {
              // 根据索引返回不同的颜色
              const colorList = [
                '#ff6384',
                '#36a2eb',
                '#ffce56',
                '#4bc0c0',
                '#9966ff',
                '#ff9f40',
                '#ff6384',
                '#36a2eb',
                '#ffce56',
              ];
              return colorList[params.dataIndex];
            },
          },
          label: {
            show: true,
            position: 'top', // 或者其他位置如：'inside', 'right'
            formatter: function (params) {
              return params.value.toFixed(2) + '%';
            }, // 显示数值并保留两位小数
            color: '#000', // 文本颜色
            fontSize: 14, // 字体大小
          },
        },
      ],
    });
  }
</script>
