import { useUserStore } from '/@/store/modules/user';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  taskList = '/newline/operation/process/taskList/',
  taskInfoList = '/newline/operation/process/form/taskList/',
  delegate = '/newline/operation/process/delegate/',
  batchComplete = '/newline/operation/process/complete/batch/',
}

export const getTasksList = () => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.taskList + userName });
};

export const getFormTaskList = (page, form) => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.taskInfoList + userName, params: { ...page, ...form } });
};

export const delegate = (taskIds, no) => {
  return defHttp.post({ url: Api.delegate + `${no}`, params: taskIds });
};

export const batchComplete = (pids) => {
  return defHttp.post({ url: Api.batchComplete, params: pids });
};
