<template>
  <div style="padding: 10px">
    <Card title="会议管理视图" :loading="loading">
      <a-space direction="vertical" size="large" style="width: 100%">
        <a-card type="inner" style="width: 100%">
          <a-row :gutter="16">
            <a-col :span="12">
              <div ref="chartRef" v-loading="loadings" style="width: 100%; height: 200px"></div>
            </a-col>
            <a-col :span="12">
              <div
                style="
                  height: 100%;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div style="font-size: 20px; width: 600px; text-indent: 2em">
                  截至{{ text.months }}月底，{{
                    new Date().getFullYear().toString().substring(2)
                  }}年新线部已组织各专业单位召开<span style="color: red"
                    >会议共计{{ text.count }}场次，月均召开{{ text.avg }}场</span
                  >。各月会议场次见图。
                </div>
              </div>
            </a-col>
          </a-row>
        </a-card>
        <a-card type="inner" style="width: 100%">
          <a-row :gutter="16">
            <a-col :span="12">
              <div ref="chartRef1" v-loading="loadings" style="width: 100%; height: 600px"></div>
            </a-col>
            <a-col :span="12">
              <div
                style="
                  height: 100%;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div style="font-size: 20px; width: 600px">
                  <div style="text-indent: 2em">
                    本月，新线部组织召开会议共计<span style="color: red">{{ text1.count }}</span
                    >场次（上月{{ text1.lastCount }}场，{{ text1.rate }}），日均<span
                      style="color: red"
                      >{{ text1.avg }}</span
                    >次。
                  </div>
                  <div>各单位参与工作量：</div>
                  <div style="color: red">{{ text1.first }}</div>
                  <div style="color: red">{{ text1.second }}</div>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-card>
        <!--        <a-card type="inner" style="width: 100%">
          <a-row :gutter="16">
            <a-col :span="14">
              <div ref="chartRef2" v-loading="loadings" style="width: 100%; height: 600px"></div>
            </a-col>
            <a-col :span="10">
              <div
                style="
                  height: 100%;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                "
              >
                &lt;!&ndash; 直接展示后端返回的富文本内容 &ndash;&gt;
                <div v-html="meetingHtml" style="width: 100%"></div>
              </div>
            </a-col>
          </a-row>
        </a-card>-->
      </a-space>
    </Card>
  </div>
</template>

<script lang="ts" setup>
  import { Ref, ref, reactive, watch } from 'vue';
  import { Card } from 'ant-design-vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getMeetingDeptStats,
    getMeetingLineStats,
    getMeetingStats,
    meetingHtmlstc,
  } from '/@/views/demo/statistics/api/statistics';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    loading: Boolean,
  });

  const loadings = ref(true);
  const chartRef = ref(null);
  const chartRef1 = ref(null);
  const chartRef2 = ref(null);
  const { setOptions } = useECharts(chartRef);
  const { setOptions: setOptions1 } = useECharts(chartRef1);
  const { setOptions: setOptions2 } = useECharts(chartRef2);
  const userStore = useUserStore();
  const router = useRouter();

  // 存储富文本内容
  const meetingHtml = ref('');

  let text = reactive({
    months: '',
    count: 0,
    avg: 0,
  });

  const getChartDate = async () => {
    let res = null;
    try {
      res = await getMeetingStats();
    } catch (error) {
      console.log(error);
    }
    const value = res.chart;
    text.months = res.text.month;
    text.count = res.text.count;
    text.avg = res.text.avg;
    setOptions({
      title: {
        text: new Date().getFullYear() + '年新线会议场次月度统计',
        left: 'center',
        top: '0%', // 贴顶显示 (0%-3% 视需求调整)
        padding: [-2, 0, 0, 0], // 上,右,下,左 内边距
      },

      tooltip: {},
      xAxis: {
        type: 'category',
        data: value.months, // 假设接口返回的月份数据
        name: '月份', // 设置x轴名称
        nameLocation: 'end', // 名称显示的位置
        nameTextStyle: {
          color: '#000', // 名称的颜色
          fontSize: 14, // 名称的字体大小
        },
      },
      yAxis: {
        name: '场次', // 设置y轴名称
        nameLocation: 'end', // 名称显示的位置
        nameGap: 21, // 调整名称与轴线之间的距离
        nameTextStyle: {
          color: '#000', // 名称的颜色
          fontSize: 14, // 名称的字体大小
        },
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
      },
      grid: {
        left: '10%',
        right: '12%',
        top: '25%', // 增大顶部间距避免与标题重叠
        bottom: '10%',
      },
      series: [
        {
          name: '场次',
          type: 'bar', // 显示柱状图
          data: value.counts || [0, 0, 0, 0],
          barWidth: '40%',
          itemStyle: {
            color: function (params) {
              var colorList = ['#ff6347', '#4682b4', '#dda0dd', '#90ee90'];
              return colorList[params.dataIndex % colorList.length]; // 使用取模运算循环颜色
            },
          },
          label: {
            show: true,
            position: 'top', // 或者其他位置如：'inside', 'right'
            formatter: '{c}', // 显示数值
            color: '#000', // 文本颜色
            fontSize: 14, // 字体大小
          },
        },
      ],
    });
  };

  let text1 = reactive({
    count: 0,
    lastCount: 0,
    rate: '',
    avg: 0,
    first: '',
    second: '',
  });

  const getChartDate1 = async () => {
    let res = null;
    try {
      res = await getMeetingDeptStats();
    } catch (error) {
      console.log(error);
    }
    const value = res.chart;
    text1.count = res.text.count;
    text1.lastCount = res.text.lastCount;
    text1.rate = res.text.rate;
    text1.avg = res.text.avg;
    text1.first = res.text.first;
    text1.second = res.text.second;
    setOptions1({
      title: {
        text: '会议统计',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        name: '数量/场次',
        type: 'value',
        boundaryGap: [0, 0.01],
      },
      yAxis: {
        name: '单位',
        type: 'category',
        data: value.names,
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
      },
      series: [
        {
          name: '数量/场次',
          type: 'bar',
          data: value.counts || [0, 0, 0, 0],
          barMaxWidth: 50,
          label: {
            show: true,
            position: 'right',
            formatter: '{c}',
            color: '#000',
            fontSize: 14,
          },
          itemStyle: {
            color: function (params) {
              var colorList = ['#6a0dad', '#ff9a9e', '#8fd3f4', '#d4ecdd'];
              return colorList[((params.dataIndex - 1) % 3) + 1];
            },
          },
        },
      ],
    });
  };

  const getChartDate2 = async () => {
    let res = null;
    try {
      res = await getMeetingLineStats();
    } catch (error) {
      console.log(error);
    }
    const value = res.chart;
    setOptions2({
      title: {
        text: '设计介入会议线路分布',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        name: '线路',
        type: 'category',
        data: value.names,
        nameLocation: 'end', // 名称显示的位置
        nameTextStyle: {
          color: '#000', // 名称的颜色
          fontSize: 14, // 名称的字体大小
        },
        axisLabel: {
          rotate: 45,
          align: 'right',
        },
      },
      yAxis: {
        name: '数量',
        type: 'value',
        boundaryGap: [0, 0.01],
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          barWidth: '80%',
          data: value.counts || [0, 0, 0, 0],
          label: {
            show: true,
            position: 'top', // 或者其他位置如：'inside', 'right'
            formatter: '{c}', // 显示数值
            color: '#000', // 文本颜色
            fontSize: 14, // 字体大小
          },
          itemStyle: {
            color: function (params) {
              var colorList = ['#5793f3', '#d14a61', '#675bba', '#e8684a'];
              return colorList[((params.dataIndex - 1) % 3) + 1];
            },
          },
        },
      ],
    });
  };

  // 获取富文本内容
  const getMeetingHtmlContent = async () => {
    try {
      const res = await meetingHtmlstc();
      meetingHtml.value = res.html;
    } catch (error) {
      console.error('获取会议视图内容失败:', error);
      meetingHtml.value = '<div>内容加载失败，请稍后再试</div>';
    }
  };

  watch(
    () => props.loading,
    () => {
      if (props.loading) {
        return;
      }
      getChartDate();
      getChartDate1();
      getChartDate2();
      getMeetingHtmlContent(); // 获取富文本内容
      loadings.value = false;
    },
    { immediate: true },
  );
</script>
