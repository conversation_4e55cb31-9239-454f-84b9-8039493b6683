<template>
  <Card title="汇编落实增长率" :loading="loading" :bodyStyle="{ padding: '24px' }">
    <!-- 柱状图容器 -->
    <div
      v-loading="chartLoading"
      ref="chartRef"
      :style="{
        width: '100%',
        height: '420px',
        marginBottom: '24px',
      }"
    ></div>

    <!-- 数据表格 -->
    <div
      v-loading="tableLoading"
      :style="{
        borderLeft: '1px solid #f0f0f0',
        borderRight: '1px solid #f0f0f0',
        borderRadius: '4px',
      }"
    >
      <a-table
        :columns="columns"
        :data-source="sortedTableData"
        :pagination="false"
        bordered
        rowKey="dept"
        :scroll="{ x: true }"
      >
        <template #footer>
          <div :style="{ fontWeight: 500 }">
            合计增长率：{{ formatGrowthRate(totalGrowthRate) }}
          </div>
        </template>
      </a-table>
    </div>
  </Card>
</template>

<script setup lang="ts">
  import { Card } from 'ant-design-vue';
  import { onMounted, Ref, ref, computed } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { getAssemblyOne } from '/@/views/demo/statistics/api/statistics';

  interface GrowthData {
    dept: string;
    currentMonth: number;
    lastMonth: number;
    allTotal: number; // 所有总数（包括未完成）
    completedTotal: number; // 完成总数（已完成状态）
    monthStartCumulative: number; // 月初累计完成数
    lastPeriodRate: number;
    currentPeriodRate: number;
    growthRate: number;
  }

  interface DepartmentSortInfo {
    sort2: number;
    sort: number;
  }

  // 响应式状态
  const loading = ref(true);
  const chartLoading = ref(true);
  const tableLoading = ref(true);
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const rawData = ref<GrowthData[]>([]);
  const totalGrowthRate = ref(0);

  // 模拟部门排序数据
  const deptSortMap = ref<Record<string, DepartmentSortInfo>>({});

  // 表格列定义 - 更新为新的结构
  const columns = [
    {
      title: '部门',
      dataIndex: 'dept',
      key: 'dept',
      width: 150,
      align: 'center' as const,
    },
    {
      title: '上期完成数',
      dataIndex: 'lastMonth',
      key: 'lastMonth',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '上期完成率',
      dataIndex: 'lastPeriodRate',
      key: 'lastPeriodRate',
      width: 120,
      align: 'center' as const,
      customRender: ({ text }: { text: number }) => formatPercentage(text),
    },
    {
      title: '本期完成数',
      dataIndex: 'currentMonth',
      key: 'currentMonth',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '本期完成率',
      dataIndex: 'currentPeriodRate',
      key: 'currentPeriodRate',
      width: 120,
      align: 'center' as const,
      customRender: ({ text }: { text: number }) => formatPercentage(text),
    },
    {
      title: '所有总数',
      dataIndex: 'allTotal',
      key: 'allTotal',
      width: 150,
      align: 'center' as const,
    },
    {
      title: '完成总数',
      dataIndex: 'completedTotal',
      key: 'completedTotal',
      width: 150,
      align: 'center' as const,
    },
    {
      title: '增长率',
      dataIndex: 'growthRate',
      key: 'growthRate',
      width: 150,
      align: 'center' as const,
      customRender: ({ text }: { text: number }) => formatGrowthRate(text),
    },
  ];

  // 计算排序后的表格数据
  const sortedTableData = computed(() => {
    return [...rawData.value].sort((a, b) => {
      const sortA = deptSortMap.value[a.dept] || { sort2: Infinity, sort: Infinity };
      const sortB = deptSortMap.value[b.dept] || { sort2: Infinity, sort: Infinity };

      if (sortA.sort2 !== sortB.sort2) {
        return sortA.sort2 - sortB.sort2;
      }
      return sortA.sort - sortB.sort;
    });
  });

  // 获取数据
  const fetchData = async () => {
    try {
      loading.value = true;
      chartLoading.value = true;
      tableLoading.value = true;

      const res = await getAssemblyOne();
      if (res?.data) {
        rawData.value = res.data as GrowthData[];
        totalGrowthRate.value = res.totalGrowthRate as number;
        updateChart();
      }
    } finally {
      loading.value = false;
      chartLoading.value = false;
      tableLoading.value = false;
    }
  };

  // 更新柱状图
  function updateChart() {
    const chartData = sortedTableData.value.map((item) => ({
      name: item.dept,
      value: item.growthRate,
      itemStyle: {
        color: item.growthRate >= 0 ? '#36a35e' : '#e4503e',
      },
    }));

    setOptions({
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0];
          const item = sortedTableData.value.find((d) => d.dept === data.name);
          return `
          <div style="padding: 8px 12px">
            <div>${data.name}</div>
            <div style="margin-top: 6px">
              本期完成数：${item?.currentMonth || 0}
            </div>
            <div>
              上期完成数：${item?.lastMonth || 0}
            </div>
            <div>
              总数：${item?.total || 0}
            </div>
            <div style="margin-top: 6px">
              增长率：${formatGrowthRate(data.value)}
            </div>
          </div>
        `;
        },
      },
      xAxis: {
        type: 'category',
        data: chartData.map((d) => d.name),
        axisLabel: {
          rotate: 30,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value: number) => `${value > 0 ? '+' : ''}${value}%`,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666',
            width: 1,
          },
        },
      },
      grid: {
        left: '3%',
        right: '3%',
        bottom: '12%',
        containLabel: true,
      },
      series: [
        {
          type: 'bar',
          data: chartData,
          barWidth: '60%',
          label: {
            show: true,
            position: 'top',
            formatter: (params: any) => formatGrowthRate(params.value),
            color: '#666',
          },
        },
      ],
    });
  }

  // 格式化增长率显示
  function formatGrowthRate(rate: number): string {
    if (isNaN(rate)) return '-';
    const fixedRate = Math.abs(rate) < 0.01 ? 0 : rate;
    return `${fixedRate >= 0 ? '+' : ''}${fixedRate.toFixed(2)}%`;
  }

  // 格式化百分比显示
  function formatPercentage(value: number): string {
    if (isNaN(value)) return '-';
    return `${value.toFixed(2)}%`;
  }

  onMounted(fetchData);
</script>

<style scoped>
  /* 优化表格样式 */
  :deep(.ant-table) {
    border-radius: 14px;
    overflow: hidden;
  }

  :deep(.ant-table-footer) {
    background-color: #fafafa;
    font-weight: 500;
    padding: 12px !important;
  }
</style>
