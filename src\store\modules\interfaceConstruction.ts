import { defineStore } from 'pinia';
import { lineOptionsApi } from '/@/api/demo/interfaceLine';
import { store } from '/@/store';

export const userInterfaceStore = defineStore({
  id: 'InterfaceConstruction',
  actions: {
    async getLineOption(id) {
      const res = await lineOptionsApi(id);
      return res;
    },
  },
});

export function userInterfaceStoreWithOut() {
  return userInterfaceStore(store);
}
