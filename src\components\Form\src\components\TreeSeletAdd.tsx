import { defineComponent, onMounted, reactive, ref, watch } from 'vue';
import { Tree } from 'ant-design-vue';
import { refBuildTreeApi } from '/@/api/erupt/erupt';
import { VxeGridInstance } from '/@/components/VxeTable';
export default defineComponent({
  name: 'eruptTable',
  props: {
    value: { type: Array },
    className: {},
    tabName: { type: String },
    readOnly: { type: Boolean },
  },
  emits: ['update:value', 'register', 'change'],
  setup(props, { emit, attrs }) {
    const { className, tabName, readOnly } = props;
    const treeData = ref([]);
    const checkedKeys = ref([]);
    const keys = ref([]);
    const treeRef = ref();
    const expandedKeys = ref([]);
    const disabled = ref(false);
    const isObjRef = ref(true);
    onMounted(() => {
      refBuildTreeApi(className, tabName).then((res) => {
        disabled.value = readOnly;
        treeData.value = res;
        let nodes = [];
        if (props.value && props.value.length > 0) {
          props.value.forEach((item) => {
            const keyStr = item + '';
            //keys.value.push(keyStr)
            console.log('getChecked', deepTree(treeData.value, keyStr));
            nodes.push(deepTree(treeData.value, keyStr));
          });

          /*for (const key of value) {
            console.log('getchecked',getChecked(key,treeData.value))
            nodes.push(getChecked(key,treeData.value))
          }*/
        }
        checkedKeys.value = nodes;
      });
    });

    watch(
      () => props.value,
      (value) => {
        let ks = [];
        let nodes = [];
        let isObj = false;
        if (value) {
          setTimeout(() => {
            value.forEach((item) => {
              let keyStr;
              if (item instanceof Object) {
                isObj = true;
                keyStr = item.id + '';
              } else {
                isObj = false;
                const node = deepTree(treeData.value, item);
                let obj = { id: node.id, version: node.version };
                obj[node.labelField] = node.title;
                nodes.push(obj);
                keyStr = item + '';
              }

              ks.push(keyStr);
            });
            keys.value = ks;
            expandedKeys.value = ks;
            if (value && value.length > 0 && !isObj) {
              emit('update:value', nodes);
              //isObjRef.value = true
              console.log('keys.length', ks.length, 'node.length', nodes.length);
              return emit('change', nodes);
            }
          }, 300);
        } else {
          keys.value = [];
          expandedKeys.value = [];
        }
      },
      { immediate: true },
    );

    watch(
      () => props.readOnly,
      (v) => {
        disabled.value = v;
      },
    );
    /*
        watch(
          () => isObjRef.value,
          (v) => {

            if (!v) {
              emit('update:value', checkedKeys.value);
              //isObjRef.value = true
              return emit('change', checkedKeys.value);
            }
          })*/
    /* watch(
       () => checkedKeys.value,
       (v) => {
         let ids = []
         if (v && v.length > 0) {
           v.forEach(item => {
               let obj = {id: item.id, version: item.version}
               obj[item.labelField] = item.title
               ids.push(obj)
           })
           emit('update:value', ids);
           return emit('change', ids);
         } else {
           emit('update:value', []);
           return emit('change', []);
         }
       },
     );*/
    const onCheck = (keys, e) => {
      console.log(e);
      checkedKeys.value = e.checkedNodes;
      let ids = [];
      if (e.checkedNodes && e.checkedNodes.length > 0) {
        e.checkedNodes.forEach((item) => {
          let obj = { id: item.id, version: item.version };
          obj[item.labelField] = item.title;
          ids.push(obj);
        });
        emit('update:value', ids);
        return emit('change', ids);
      } else {
        emit('update:value', []);
        return emit('change', []);
      }
    };

    function deepTree(treeList, key) {
      for (let index = 0; index < treeList.length; index++) {
        const element = treeList[index];
        if (element.key == key) {
          return element;
        }
        if (element.children && element.children.length) {
          const ele = deepTree(element.children, key);
          if (ele) {
            return ele;
          }
        }
      }
    }

    return {
      treeData,
      checkedKeys,
      onCheck,
      keys,
      treeRef,
      expandedKeys,
      isObjRef,
      disabled,
    };
  },
  render() {
    return (
      <Tree
        ref="treeRef"
        disabled={this.disabled}
        autoExpandParent={true}
        v-model:checkedKeys={this.keys}
        v-model:expandedKeys={this.expandedKeys}
        treeData={this.treeData}
        checkable
        onCheck={(keys, e) => {
          this.onCheck(keys, e);
        }}
        onExpand={(expandedKeys, { expanded, node }) => {
          debugger;
          if (!expanded) {
            const expKeys = JSON.parse(JSON.stringify(expandedKeys));
            let keys = [];
            if (node.children) node.children.forEach((child) => keys.push(child.id));
            for (let key of keys) {
              const index = expKeys.findIndex((item) => item == key);
              if (index > 0) {
                expKeys.splice(index, 1);
              }
            }
            this.expandedKeys = expKeys;
          } else {
            this.expandedKeys = expandedKeys;
          }
        }}
      ></Tree>
    );
  },
});
