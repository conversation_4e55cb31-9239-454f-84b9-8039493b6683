import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetOptions = '/newline/line/getSiteByLine/',
  GetDeptBySpecical = '/newline/question/findDeptBySpecial/',
  GetNextNode = '/newline/question/getNextNode',
}
export const GetOptions = async (id) => {
  return await defHttp.get({ url: Api.GetOptions + id });
};
export const GetDeptBySpecical = async (code) => {
  return await defHttp.get({ url: Api.GetDeptBySpecical + code });
};

export const getNextNode = async (id) => {
  return await defHttp.post({ url: Api.GetNextNode + `/${id}` });
};
