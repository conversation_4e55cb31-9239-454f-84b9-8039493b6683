<template>
  <div ref="wrapRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
  import { defineComponent, ref, nextTick, unref, onMounted } from 'vue';

  import { useScript } from '/@/hooks/web/useScript';

  const BAI_DU_MAP_URL =
    'https://api.map.baidu.com/api?type=subway&v=1.0&ak=WESjNcUgSneFfxbvQKpckuvHPA91CIar"';
  export default defineComponent({
    name: 'BaiduMap',
    props: {
      width: {
        type: String,
        default: '100%',
      },
      height: {
        type: String,
        default: 'calc(100vh - 78px)',
      },
    },
    setup() {
      const wrapRef = ref<HTMLDivElement | null>(null);
      const { toPromise } = useScript({ src: BAI_DU_MAP_URL });

      async function initMap() {
        await toPromise();
        await nextTick();
        const wrapEl = unref(wrapRef);
        if (!wrapEl) return;
        const BMapSub = (window as any).BMapSub;

        let list = BMapSub.SubwayCitiesList;
        let subwaycity = null;
        for (var i = 0; i < list.length; i++) {
          if (list[i].name === '南京') {
            subwaycity = list[i];
            break;
          }
        }

        const subway = new BMapSub.Subway(wrapEl, subwaycity.citycode);
        subway.setZoom(0.5);

        const zoomControl = new BMapSub.ZoomControl({
          anchor: BMAPSUB_ANCHOR_BOTTOM_RIGHT,
          offset: new BMapSub.Size(10, 100),
        });

        subway.addControl(zoomControl);

        subway.addEventListener('tap', function (e) {
          //alert('您点击了"' + e.station.name + '"站');
          var detail = new BMapSub.DetailInfo(subway);
          detail.search(e.station.name);
        });
      }

      onMounted(() => {
        initMap();
      });

      return { wrapRef };
    },
  });
</script>
