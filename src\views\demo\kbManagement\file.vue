<template>
  <PageWrapper dense contentFullHeight fixedHeight contentClass="flex">
    <FolderTree class="w-1/4 xl:w-1/5" @select="handleSelect"> </FolderTree>
    <div class="w-3/4 xl:w-4/5 flex flex-col">
      <BasicTable @register="registerTable" class="w-full" :searchInfo="searchInfo">
        <template #toolbar>
          <a-button type="primary" @click="handleCreate">
            <template #icon><PlusOutlined /></template>
            新增文件
          </a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  label: '预览',
                  onClick: handlePreview.bind(null, record),
                },
                {
                  label: '下载',
                  onClick: handleDownload.bind(null, record),
                },
                {
                  label: '删除',
                  color: 'error',
                  popConfirm: {
                    title: '是否确认删除',
                    placement: 'left',
                    confirm: handleDelete.bind(null, record),
                  },
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>

    <a-modal
      v-model:visible="uploadModalVisible"
      title="上传文件"
      @ok="handleUploadConfirm"
      @cancel="handleUploadCancel"
      :maskClosable="false"
      :keyboard="false"
      :width="800"
      :centered="true"
      :destroyOnClose="true"
    >
      <div class="upload-modal-content">
        <BigFileUpload
          ref="uploadRef"
          beforeUploadUrl="/system/upload/before"
          uploadChunkUrl="/system/upload/chunk"
          uploadProgressUrl="/system/upload/progress"
          uploadMergeUrl="/system/upload/merge"
        />
      </div>
    </a-modal>
    <BasicModal
      @register="registerModal"
      title="预览文件"
      :width="1000"
      :height="800"
      :fullscreen="true"
      :showFooter="false"
      :showCancelBtn="false"
      :showOkBtn="false"
      :closeFunc="async () => true"
    >
      <div style="height: 100vh">
        <iframe :src="previewUrl" width="100%" height="100%" frameborder="0"></iframe>
      </div>
    </BasicModal>
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, reactive, ref, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import FolderTree from './FolderTree.vue';
  import { columns, searchFormSchema } from './folder.data';
  import { useGo } from '/@/hooks/web/usePage';
  import BigFileUpload from '/@/components/Upload/src/BigFileUpload.vue';
  import {
    getDocumentationList,
    addDocumentationFile,
    deleteDocumentationFile,
  } from './documentation';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useUserStore } from '/@/store/modules/user';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Base64 } from 'js-base64';

  export default defineComponent({
    name: 'kdfile',
    components: {
      BasicTable,
      PageWrapper,
      FolderTree,
      TableAction,
      BigFileUpload,
      PlusOutlined,
      BasicModal,
    },
    setup() {
      const go = useGo();
      const { createMessage } = useMessage();
      const searchInfo = reactive<Recordable>({});
      const uploadModalVisible = ref<boolean>(false);
      const selectedKeys = ref<string[]>(['1']);
      const uploadRef = ref();
      const [registerModal, { openModal }] = useModal();
      const previewUrl = ref<string>('');
      const userStore = useUserStore();
      const globSetting = useGlobSetting();
      const key = ref(0);

      const [registerTable, { setTableData }] = useTable({
        rowKey: 'id',
        columns,
        formConfig: {
          labelWidth: 120,
          schemas: searchFormSchema,
          autoSubmitOnEnter: true,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
        },
        searchInfo,
        title: '文件列表',
        pagination: {
          pageSize: 20,
          defaultPageSize: 20,
        },
        api: async (params) => {
          if (searchInfo.folderId) {
            const res = await getDocumentationList({
              directoryId: Number(searchInfo.folderId),
              fileName: params.fileName || '',
            });
            return res;
          }
          return [];
        },
      });

      function handleCreate() {
        if (!searchInfo.folderId) {
          createMessage.warning('请先选择左侧文件目录');
          return;
        }
        uploadModalVisible.value = true;
      }

      async function handleUploadConfirm() {
        if (uploadRef.value.fileList) {
          try {
            //alert(JSON.stringify(uploadRef.value));
            const fileInfo = uploadRef.value.fileList[0];
            if (fileInfo) {
              //alert(JSON.stringify(fileInfo));
              const params = {
                lineId: fileInfo.md5,
                type: '',
                des: fileInfo.name,
                enclosure: fileInfo.url,
                directoryId: Number(searchInfo.folderId),
              };
              await addDocumentationFile(params);
              createMessage.success('文件保存成功');
              // 刷新文件列表
              await handleSelect(searchInfo.folderId);
              uploadModalVisible.value = false;
            }
          } catch (error: any) {
            console.error('文件上传或保存失败:', error);
            createMessage.error('文件上传或保存失败: ' + (error.message || '未知错误'));
          }
        }
      }

      function handleUploadCancel() {
        uploadModalVisible.value = false;
      }

      function handleDownload(record: Recordable) {
        console.log('下载文件', record);
        const downloadUrl = globSetting.apiUrl + '/documentation/download/' + record.lineId; // 设置 UR
        window.open(downloadUrl, '_blank');
      }

      function handlePreview(record: Recordable) {
        const downloadUrl =
          globSetting.apiUrl +
          '/documentation/download/' +
          record.lineId +
          '?fullfilename=' +
          record.fileName; // 设置 UR

        const watermarkTxt =
          userStore.userInfo?.username + ' ' + userStore.userInfo?.realName || '';
        const encodedUrl = encodeURIComponent(Base64.encode(downloadUrl));

        previewUrl.value = ''; // 清空旧缓存
        previewUrl.value =
          globSetting.previewUrl +
          '/onlinePreview?url=' +
          encodedUrl +
          '&watermarkTxt=' +
          watermarkTxt; // 设置预览 URL
        key.value += 1; // 重新加载 iframe
        //alert(previewUrl.value);
        openModal(true);
      }

      async function handleDelete(record: Recordable) {
        try {
          const params = {
            id: Number(record.id),
          };
          await deleteDocumentationFile(params);
          createMessage.success('文件删除成功');
          // 刷新文件列表
          await handleSelect(searchInfo.folderId);
        } catch (error: any) {
          console.error('删除文件失败:', error);
          createMessage.error('删除文件失败: ' + (error.message || '未知错误'));
        }
      }

      async function handleSelect(folderId: string) {
        if (folderId) {
          searchInfo.folderId = folderId;
          try {
            const res = await getDocumentationList({
              directoryId: Number(folderId),
              fileName: '',
            });
            setTableData(res);
          } catch (error: any) {
            console.error('获取文件列表失败:', error);
            createMessage.error('获取文件列表失败: ' + (error.message || '未知错误'));
          }
        }
      }

      return {
        registerTable,
        handleCreate,
        handleDownload,
        handleDelete,
        handlePreview,
        handleSelect,
        searchInfo,
        uploadModalVisible,
        handleUploadConfirm,
        handleUploadCancel,
        selectedKeys,
        uploadRef,
        registerModal,
        previewUrl,
      };
    },
  });
</script>

<style scoped>
  .upload-modal-content {
    padding: 24px;
  }

  :deep(.ant-upload-drag) {
    border-radius: 8px;
  }

  :deep(.ant-upload-drag:hover) {
    border-color: #40a9ff;
  }

  :deep(.ant-upload-drag-icon) {
    color: #40a9ff;
    font-size: 48px;
    margin-bottom: 16px;
  }

  :deep(.ant-upload-text) {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 8px;
  }

  :deep(.ant-upload-hint) {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
  }
</style>
