<template>
  <div>
    <erupt-table
      title="接口施工"
      ref="eruptTable"
      class-name="InterfaceConstruction"
      :form-dynamic-control="dynamicController"
      :overwrite-action="overwriteAction"
      :edit-label-width="200"
      :query-sort="querySort"
    >
      <template #state="{ row }">
        <div v-if="row.state == '施工准备中'" style="align-self: center">
          <a-tag color="red" style="width: 80px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
        <div v-if="row.state == '施工管理中'" style="align-self: center">
          <a-tag color="green" style="width: 80px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
        <div v-if="row.state == '施工完成'" style="align-self: center">
          <a-tag color="yellow" style="width: 80px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
        <div v-if="row.state == '有超时项'" style="align-self: center">
          <a-tag color="#8b949e" style="width: 80px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
      </template>
    </erupt-table>
    <BasicModal @register="register" :showFooter="false" width="80%" title="详情">
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="详细信息">
          <table class="desc-table">
            <tbody>
              <tr class="table-header">
                <td>线路</td>
                <td>开始时间</td>
                <td>结束时间</td>
                <td>影响既有线范围</td>
                <td>实施单位</td>
              </tr>
              <tr>
                <td>{{ formatLineName(descData['lineIds']) }}</td>
                <td>{{
                  descData['startTime'] ? new Date(descData['startTime']).toLocaleDateString() : ''
                }}</td>
                <td>{{
                  descData['endTime'] ? new Date(descData['endTime']).toLocaleDateString() : ''
                }}</td>
                <td>{{ descData['bidSection'] }}</td>
                <td>{{ formatDeptName(descData['distributionDepts']) }}</td>
              </tr>
            </tbody>
          </table>
          <table class="desc-table">
            <tbody>
              <tr class="table-header">
                <td>施工内容简述</td>
              </tr>
              <tr>
                <td colspan="4">{{ descData['des'] }}</td>
              </tr>
            </tbody>
          </table>
          <FileList title="" :readonly="true" :dataSource="descData['fileList1']"> </FileList>
          <FileList title="" :readonly="true" :dataSource="descData['fileList2']"> </FileList>
        </a-collapse-panel>
      </a-collapse>
    </BasicModal>
    <BasicModal
      @register="registerEdit"
      :showFooter="false"
      width="80%"
      :title="type"
      :defaultFullscreen="true"
      @ok="handleSubmitEdit"
      @cancel="handleCancelEdit"
    >
      <BasicForm @register="registerFormEdit" style="height: 350px"> </BasicForm>
    </BasicModal>
  </div>
</template>

<style scoped lang="less"></style>
<script lang="ts">
  import eruptTable from '/@/components/EruptTable/eruptTable';
  import { defineComponent, onMounted, ref } from 'vue';
  import { NColorPicker } from 'naive-ui';
  import { buildApi, getOne, save, update } from '/@/api/erupt/erupt';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useForm } from '/@/components/Form';
  import { userInterfaceStoreWithOut } from '/@/store/modules/interfaceConstruction';
  import {
    getComponent,
    getComponentProps,
    getSearchComponent,
  } from '/@/components/EruptTable/componets';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Detail } from '/@/views/demo/system/InterfaceConstruction/api';
  import FileList from '/@/components/Form/src/components/FileList.vue';
  import { Modal } from 'ant-design-vue';
  import { useUserStore } from '/@/store/modules/user';
  import { SaveFile } from '/@/components/Form/src/api';

  export default defineComponent({
    name: 'InterfaceConstruction',
    components: { BasicModal, BasicForm, eruptTable, NColorPicker, FileList },
    setup: function () {
      const editSchemasWithoutFile = ref([]);
      const editSchemas = ref([]);
      const deptStr = ref('');
      const { createMessage } = useMessage();
      const [register, { openModal, closeModal }] = useModal();
      const [registerEdit, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
      const [
        registerFormEdit,
        { resetFields, setFieldsValue, updateSchema, validate, getFieldsValue },
      ] = useForm({
        labelWidth: 100,
        wrapperCol: { span: 24 },
        schemas: editSchemas,
        showActionButtonGroup: false,
      });
      const distributionDeptsElement = ref<HTMLElement | null>(null);
      const uploadDeptElement = ref<HTMLElement | null>(null);
      let observer: MutationObserver | null = null;
      const userStore = useUserStore();
      const type = ref('新增');
      const activeKey = ref('1');
      const base = ref({});
      const descData = ref({});
      const lineOptions = ref([]);
      const areaOptions = ref([]);
      const deptsOptions = ref([]);
      const eruptFieldModels = ref([]);
      const baseUrl = import.meta.env.VITE_GLOB_ERUPT_ATTACHMENT;

      const [registerForm] = useForm({
        labelWidth: 100,
        schemas: editSchemas,
        showActionButtonGroup: false,
      });

      onMounted(() => {
        buildApi('InterfaceConstruction').then((res) => {
          debugger;
          const {
            eruptModel: { eruptFieldModels },
          } = res;
          let edits = [];
          eruptFieldModels.forEach((item) => {
            const key = item.fieldName;
            const title = item.eruptFieldJson.edit.title;

            if (item.eruptFieldJson.edit.show.edit_show && key !== 'id') {
              if (
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
                item.eruptFieldJson.edit.type !== 'TAB_TREE'
              ) {
                const e = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, 'InterfaceConstruction'),
                      disabled: item.eruptFieldJson.edit.readOnly.edit,
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };

                edits.push(e);
              }
            }
          });
          editSchemasWithoutFile.value = edits;
          edits.push(
            {
              field: 'fileList1',
              component: 'EruptFileList',
              componentProps: {
                dataSource: [
                  {
                    field: 'expertReviewOpinion',
                    dataIndex: 0,
                    name: '工作联系单',
                    url: '',
                    des: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '新线管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'assetDisposalList',
                    dataIndex: 1,
                    name: '施工协调会议记录',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '新线管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'workContactSheet',
                    dataIndex: 2,
                    name: '出入口临时封闭通知',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '新线管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'constructionPlanApplicationForm',
                    dataIndex: 3,
                    name: '现场检查记录',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '新线管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工过程管理',
                  },
                  {
                    field: 'externalWorkPermit',
                    dataIndex: 4,
                    name: '工作联系单',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '新线管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工过程管理',
                  },
                  {
                    field: 'hiddenDangerRectificationList',
                    dataIndex: 5,
                    name: '系统功能恢复确认单',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '新线管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '临时验收接管',
                  },
                  {
                    field: 'workContactSheet2',
                    dataIndex: 6,
                    name: '设施设备交接协议',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '新线管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '临时验收接管',
                  },
                  {
                    field: 'systemFunctionRecoveryConfirmation',
                    dataIndex: 7,
                    name: '会议记录（如有）',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '新线管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '临时验收接管',
                  },
                  {
                    field: 'designConstructionPlan',
                    dataIndex: 8,
                    name: '资产处置清单',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '资产管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'onSiteInspectionRecords',
                    dataIndex: 9,
                    name: '安全隐患整改通知单',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '新线管理部',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工过程管理',
                  },
                ],
              },
              colProps: {
                span: '24',
              },
            },
            {
              field: 'fileList2',
              component: 'EruptFileList',
              componentProps: {
                dataSource: [
                  {
                    field: 'feedbackFromUnits',
                    dataIndex: 0,
                    name: '设计、施工方案',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: deptStr.value,
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'constructionCoordinationMeetingRecords',
                    dataIndex: 1,
                    name: '安全保证金交款单',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'trafficBureauNotification',
                    dataIndex: 2,
                    name: '外单位作业安全、消防、治安内安全协议',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'safetyDepositReceipt',
                    dataIndex: 3,
                    name: '施工负责人证',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'externalUnitSafetyAgreement',
                    dataIndex: 4,
                    name: '施工方案申报表',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'constructionSupervisorCertificate',
                    dataIndex: 5,
                    name: '外部施工作业许可单',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工前期管理',
                  },
                  {
                    field: 'onSiteInspectionRecords1',
                    dataIndex: 6,
                    name: '安全隐患整改通知单',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '施工过程管理',
                  },
                  {
                    field: 'others',
                    dataIndex: 7,
                    name: '其他',
                    url: '',
                    editable: false,
                    uploader: '',
                    uploadTime: '',
                    uploadDept: '',
                    deadlineTime: null,
                    state: '未确认',
                    parentId: '',
                    stage: '其他',
                  },
                ],
              },
              colProps: {
                span: '24',
              },
            },
          );

          edits.forEach((edit) => {
            if (edit.field == 'id') {
            }
            if (
              edit.field == 'lineIds' ||
              edit.field == 'bidSection' ||
              edit.field == 'startTime' ||
              edit.field == 'endTime' ||
              edit.field == 'des'
            ) {
              const props = { ...edit.componentProps() };
              edit.componentProps = ({ formModel }) => {
                return {
                  ...props,
                  disabled:
                    userStore.userInfo.dept != '新线管理部' &&
                    userStore.userInfo.dept != '资产管理部',
                };
              };
            }
            if (edit.field == 'distributionDepts') {
              const props = { ...edit.componentProps() };
              edit.componentProps = ({ formModel }) => {
                return {
                  ...props,
                  disabled:
                    userStore.userInfo.dept != '新线管理部' &&
                    userStore.userInfo.dept != '资产管理部',
                  onChange: async (e) => {
                    debugger;
                    if (e == null) {
                      setFieldsValue({
                        fileList1: [
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: 0,
                            field: 'expertReviewOpinion',
                            editable: false,
                            name: '工作联系单',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '新线管理部',
                            stage: '施工前期管理',
                            state: '未确认',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'assetDisposalList',
                            editable: false,
                            name: '施工协调会议记录',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '新线管理部',
                            stage: '施工前期管理',
                            state: '未确认',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'workContactSheet',
                            editable: false,
                            name: '出入口临时封闭通知',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '新线管理部',
                            stage: '施工前期管理',
                            state: '未确认',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'constructionPlanApplicationForm',
                            editable: false,
                            name: '现场检查记录',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '新线管理部',
                            stage: '施工过程管理',
                            state: '未确认',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'externalWorkPermit',
                            editable: false,
                            name: '工作联系单',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '新线管理部',
                            stage: '施工过程管理',
                            state: '未确认',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'hiddenDangerRectificationList',
                            editable: false,
                            name: '系统功能恢复确认单',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '新线管理部',
                            state: '未确认',
                            stage: '临时验收接管',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'workContactSheet2',
                            editable: false,
                            name: '设施设备交接协议',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '新线管理部',
                            stage: '临时验收接管',
                            state: '未确认',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'systemFunctionRecoveryConfirmation',
                            editable: false,
                            name: '会议记录（如有）',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '新线管理部',
                            stage: '临时验收接管',
                            state: '未确认',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'designConstructionPlan',
                            editable: false,
                            name: '资产处置清单',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '资产管理部	',
                            stage: '施工前期管理',
                            state: '未确认',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'onSiteInspectionRecords',
                            editable: false,
                            name: '安全隐患整改通知单',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '新线管理部',
                            stage: '施工过程管理',
                            state: '未确认',
                          },
                        ],
                        fileList2: [
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'feedbackFromUnits',
                            editable: false,
                            name: '设计、施工方案',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '',
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'constructionCoordinationMeetingRecords',
                            editable: false,
                            name: '安全保证金交款单',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '',
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'trafficBureauNotification',
                            editable: false,
                            name: '外单位作业安全、消防、治安内安全协议',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '',
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'safetyDepositReceipt',
                            editable: false,
                            name: '施工负责人证',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '',
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'externalUnitSafetyAgreement',
                            editable: false,
                            name: '施工方案申报表',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '',
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'constructionSupervisorCertificate',
                            editable: false,
                            name: '外部施工作业许可单',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '',
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'onSiteInspectionRecords1',
                            editable: false,
                            name: '安全隐患整改通知单',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '',
                            state: '未确认',
                            parentId: '',
                            stage: '施工过程管理',
                          },
                          {
                            id: null,
                            createDateTime: null,
                            creator: null,
                            createUser: null,
                            updateDateTime: null,
                            updateUser: null,
                            updator: null,
                            version: null,
                            field: 'others',
                            editable: false,
                            name: '其他',
                            des: '',
                            url: '',
                            uploader: '',
                            uploadTime: '',
                            uploadDept: '',
                            state: '未确认',
                            parentId: '',
                            stage: '其他',
                          },
                        ],
                      });
                      return; // 提前返回
                    }
                    const upLoaddept = props.options.find((item) => item.value == e).label;
                    deptStr.value = upLoaddept;
                    const { fileList1, fileList2 } = getFieldsValue();
                    if (fileList2) {
                      fileList2.forEach((item) => {
                        item.uploadDept = upLoaddept;
                      });
                      setFieldsValue({
                        fileList2,
                      });
                    } else {
                      setFieldsValue({
                        fileList2: [
                          {
                            field: 'feedbackFromUnits',
                            dataIndex: 0,
                            name: '设计、施工方案',
                            url: '',
                            editable: false,
                            uploader: '',
                            uploadTime: '',
                            uploadDept: deptStr.value,
                            deadlineTime: null,
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            field: 'constructionCoordinationMeetingRecords',
                            dataIndex: 1,
                            name: '安全保证金交款单',
                            url: '',
                            editable: false,
                            uploader: '',
                            uploadTime: '',
                            uploadDept: deptStr.value,
                            deadlineTime: null,
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            field: 'trafficBureauNotification',
                            dataIndex: 2,
                            name: '外单位作业安全、消防、治安内安全协议',
                            url: '',
                            editable: false,
                            uploader: '',
                            uploadTime: '',
                            uploadDept: deptStr.value,
                            deadlineTime: null,
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            field: 'safetyDepositReceipt',
                            dataIndex: 3,
                            name: '施工负责人证',
                            url: '',
                            editable: false,
                            uploader: '',
                            uploadTime: '',
                            uploadDept: deptStr.value,
                            deadlineTime: null,
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            field: 'externalUnitSafetyAgreement',
                            dataIndex: 4,
                            name: '施工方案申报表',
                            url: '',
                            editable: false,
                            uploader: '',
                            uploadTime: '',
                            uploadDept: deptStr.value,
                            deadlineTime: null,
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            field: 'constructionSupervisorCertificate',
                            dataIndex: 5,
                            name: '外部施工作业许可单',
                            url: '',
                            editable: false,
                            uploader: '',
                            uploadTime: '',
                            uploadDept: deptStr.value,
                            deadlineTime: null,
                            state: '未确认',
                            parentId: '',
                            stage: '施工前期管理',
                          },
                          {
                            field: 'onSiteInspectionRecords1',
                            dataIndex: 9,
                            name: '安全隐患整改通知单',
                            url: '',
                            editable: false,
                            uploader: '',
                            uploadTime: '',
                            uploadDept: deptStr.value,
                            deadlineTime: null,
                            state: '未确认',
                            parentId: '',
                            stage: '施工过程管理',
                          },
                          {
                            field: 'others',
                            dataIndex: 6,
                            name: '其他',
                            url: '',
                            editable: false,
                            uploader: '',
                            uploadTime: '',
                            uploadDept: deptStr.value,
                            deadlineTime: null,
                            state: '未确认',
                            parentId: '',
                            stage: '其他',
                          },
                        ],
                      });
                    }
                  },
                };
              };
            }
          });
          editSchemas.value = edits;
        });
      });
      const eruptTable = ref();
      const userInterfaceStore = userInterfaceStoreWithOut();
      //三层组件动态change
      const dynamicController = {
        lineids: (form, e) => {
          console.log('form', form);
          let options = [];
          userInterfaceStore.getLineOption(e).then((res) => {
            res.forEach((item) => {
              console.log('item' + item.name + item.id);
              options.push({ label: item.name, value: item.id });
            });
            {
              setTimeout(() => {
                eruptTable.value.updateForm({
                  field: 'bid_section',
                  componentProps: () => {
                    return {
                      options,
                      disabled: eruptTable.value.modalTitle == '详情',
                    };
                  },
                });
              }, 200);
            }
          });
        },

        tags: (form) => {
          console.log('form' + form);
        },
      };

      const resetAllFeilds = () => {
        resetFields();
        setFieldsValue({
          id: 1,
          createDateTime: null,
          creator: null,
          createUser: null,
          updateDateTime: null,
          updateUser: null,
          updator: null,
          version: 1,
          lineIds: null,
          bidSection: null,
          distributionDepts: null,
          endTime: null,
          startTime: null,
          des: null,
          fileList1: [
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: 0,
              field: 'expertReviewOpinion',
              editable: false,
              name: '工作联系单',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '新线管理部',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'assetDisposalList',
              editable: false,
              name: '施工协调会议记录',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '新线管理部',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'workContactSheet',
              editable: false,
              name: '出入口临时封闭通知',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '新线管理部',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'constructionPlanApplicationForm',
              editable: false,
              name: '现场检查记录',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '新线管理部',
              stage: '施工过程管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'externalWorkPermit',
              editable: false,
              name: '工作联系单',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '新线管理部',
              stage: '施工过程管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'hiddenDangerRectificationList',
              editable: false,
              name: '系统功能恢复确认单',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '新线管理部',
              stage: '临时验收接管',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'workContactSheet2',
              editable: false,
              name: '设施设备交接协议',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '新线管理部',
              stage: '临时验收接管',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'systemFunctionRecoveryConfirmation',
              editable: false,
              name: '会议记录（如有）',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '新线管理部',
              stage: '临时验收接管',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'designConstructionPlan',
              editable: false,
              name: '资产处置清单',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '资产管理部	',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'onSiteInspectionRecords',
              editable: false,
              name: '安全隐患整改通知单',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '新线管理部',
              stage: '施工过程管理',
            },
          ],
          fileList2: [
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'feedbackFromUnits',
              editable: false,
              name: '设计、施工方案',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'constructionCoordinationMeetingRecords',
              editable: false,
              name: '安全保证金交款单',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'trafficBureauNotification',
              editable: false,
              name: '外单位作业安全、消防、治安内安全协议',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'safetyDepositReceipt',
              editable: false,
              name: '施工负责人证',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'externalUnitSafetyAgreement',
              editable: false,
              name: '施工方案申报表',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'constructionSupervisorCertificate',
              editable: false,
              name: '外部施工作业许可单',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '',
              stage: '施工前期管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'onSiteInspectionRecords1',
              editable: false,
              name: '安全隐患整改通知单',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '',
              stage: '施工过程管理',
            },
            {
              id: null,
              createDateTime: null,
              creator: null,
              createUser: null,
              updateDateTime: null,
              updateUser: null,
              updator: null,
              version: null,
              field: 'others',
              editable: false,
              name: '其他',
              des: '',
              url: '',
              uploader: '',
              uploadTime: '',
              state: '未确认',
              parentId: '',
              uploadDept: '',
              stage: '其他',
            },
          ],
          state: null,
          receiveDepts: null,
          sendTime: null,
        });
      };
      const formatLineName = (lineIds) => {
        const matchedLine = lineOptions.value.find((line) => line.value === lineIds);
        return matchedLine ? matchedLine.label : '-';
      };

      // 格式化区域名称
      const formatAreaName = (bidSection) => {
        const matchedArea = areaOptions.value.find((area) => area.value === bidSection);
        return matchedArea ? matchedArea.label : '-';
      };

      const formatDeptName = (distributionDepts) => {
        const matchedDept = deptsOptions.value.find(
          (distributionDept) => distributionDept.value === distributionDepts,
        );
        return matchedDept ? matchedDept.label : '-';
      };

      const formatFileLinks = (filePaths) => {
        if (!filePaths) return ''; // 如果文件路径为空，返回空字符串

        return filePaths.url
          .split('|')
          .map((filePath) => {
            const fileName = filePath.substring(filePath.lastIndexOf('/') + 1); // 提取文件名
            const fileUrl = `${baseUrl}${filePath}`; // 拼接完整的URL
            return `<a href="${fileUrl}" download="${fileName}" target="_blank">${fileName}</a>`;
          })
          .join(' </br> '); // 用</br>连接多个文件链接
      };

      const overwriteAction = {
        add: async () => {
          resetAllFeilds();
          type.value = '新增';
          resetFields();
          setTimeout(() => {
            // 获取初始化的id值
            const { id } = getFieldsValue();

            // 更新文件列表的parentId
            const { fileList1, fileList2 } = getFieldsValue();
            const updatedFileList1 = fileList1?.map((item) => ({ ...item, parentId: id }));
            const updatedFileList2 = fileList2?.map((item) => ({ ...item, parentId: id }));

            setFieldsValue({
              fileList1: updatedFileList1,
              fileList2: updatedFileList2,
            });
          }, 200);
          openModal1();
        },
        edit: async (row) => {
          const res = await Detail(row.id);
          debugger;
          type.value = '编辑';
          base.value = res;
          setTimeout(() => {
            resetFields();
            openModal1();
            setTimeout(() => {
              const {
                id,
                lineIds,
                receiveDepts,
                sendTime,
                updateDateTime,
                updateUser,
                updator,
                version,
                bidSection,
                createDateTime,
                createUser,
                creator,
                des,
                distributionDepts,
                endTime,
                startTime,
                state,
                fileList1,
                fileList2,
              } = res;
              setFieldsValue({
                id,
                lineIds,
                receiveDepts,
                sendTime,
                state,
                updateDateTime,
                updateUser,
                updator,
                version,
                bidSection,
                createUser,
                createDateTime,
                creator,
                des,
                distributionDepts,
                endTime,
                startTime,
              });
              // 更新文件列表的parentId
              const updatedFileList1 = fileList1?.map((item) => ({ ...item, parentId: id }));
              const updatedFileList2 = fileList2?.map((item) => ({ ...item, parentId: id }));
              setTimeout(() => {
                setFieldsValue({
                  fileList1: updatedFileList1 || [],
                  fileList2: updatedFileList2 || [],
                });
              });
            }, 200);
          }, 200);
        },
        detail: async (row) => {
          const res = await Detail(row.id); // 获取详情数据
          const {
            eruptModel: { eruptFieldModels: fieldModels },
          } = await buildApi('InterfaceConstruction');

          eruptFieldModels.value = fieldModels;

          // 提取线路和区域的选择项
          const lineField = fieldModels.find((item) => item.fieldName === 'lineIds');
          const areaField = fieldModels.find((item) => item.fieldName === 'bidSection');
          const deptsField = fieldModels.find((item) => item.fieldName === 'distributionDepts');

          lineOptions.value = lineField?.componentValue || [];
          areaOptions.value = areaField?.componentValue || [];
          deptsOptions.value = deptsField?.componentValue || [];

          descData.value = res;

          setTimeout(() => {
            openModal();
          }, 200);
        },
      };

      const removeEmptyValues = (obj) => {
        for (const key in obj) {
          if (obj[key] === null || obj[key] === undefined) {
            delete obj[key];
          } else if (typeof obj[key] === 'object' && Object.keys(obj[key]).length === 0) {
            delete obj[key];
          }
        }
        return obj;
      };

      return {
        type,
        editSchemasWithoutFile,
        register,
        registerFormEdit,
        registerForm,
        resetFields,
        setFieldsValue,
        updateSchema,
        validate,
        getFieldsValue,
        registerEdit,
        openModal,
        openModal1,
        closeModal,
        closeModal1,
        formatLineName,
        formatDeptName,
        formatAreaName,
        formatFileLinks,
        eruptFieldModels,
        descData,
        activeKey,
        overwriteAction,
        dynamicController,
        eruptTable,
        querySort: (querys) => {
          for (let i = 0; i < querys.length; i++) {
            querys[i].span = 6;
            if (i >= 4) {
              querys[i].folding = true;
            }
          }
          console.log('afterSort', querys);
          return querys;
        },
        handleSubmitEdit: async () => {
          Modal.confirm({
            title: '请确认施工范围是否正确',
            onOk: async () => {
              debugger;
              let values = await validate();
              let { fileList1, fileList2 } = values;
              if (!fileList1) {
                fileList1 = [
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: 0,
                    field: 'expertReviewOpinion',
                    editable: false,
                    name: '工作联系单',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '新线管理部',
                    stage: '施工前期管理',
                  },
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: null,
                    field: 'assetDisposalList',
                    editable: false,
                    name: '施工协调会议记录',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '新线管理部',
                    stage: '施工前期管理',
                  },
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: null,
                    field: 'workContactSheet',
                    editable: false,
                    name: '出入口临时封闭通知',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '新线管理部',
                    stage: '施工前期管理',
                  },
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: null,
                    field: 'constructionPlanApplicationForm',
                    editable: false,
                    name: '现场检查记录',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '新线管理部',
                    stage: '施工过程管理',
                  },
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: null,
                    field: 'externalWorkPermit',
                    editable: false,
                    name: '工作联系单',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '新线管理部',
                    stage: '施工过程管理',
                  },
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: null,
                    field: 'hiddenDangerRectificationList',
                    editable: false,
                    name: '系统功能恢复确认单',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '新线管理部',
                    stage: '临时验收接管',
                  },
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: null,
                    field: 'workContactSheet2',
                    editable: false,
                    name: '设施设备交接协议',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '新线管理部',
                    stage: '临时验收接管',
                  },
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: null,
                    field: 'systemFunctionRecoveryConfirmation',
                    editable: false,
                    name: '会议记录（如有）',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '新线管理部',
                    stage: '临时验收接管',
                  },
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: null,
                    field: 'designConstructionPlan',
                    editable: false,
                    name: '资产处置清单',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '资产管理部	',
                    stage: '施工前期管理',
                  },
                  {
                    id: null,
                    createDateTime: null,
                    creator: null,
                    createUser: null,
                    updateDateTime: null,
                    updateUser: null,
                    updator: null,
                    version: null,
                    field: 'onSiteInspectionRecords',
                    editable: false,
                    name: '安全隐患整改通知单',
                    des: '',
                    url: '',
                    uploader: '',
                    uploadTime: '',
                    state: '未确认',
                    parentId: '',
                    uploadDept: '新线管理部',
                    stage: '施工过程管理',
                  },
                ];
              }

              if (type.value == '新增') {
                if (fileList1) {
                  for (const item of fileList1) {
                    // 处理item不存在的情况,item存进数据库bi
                    if (!item.id && item.id !== 0) {
                      const res = await SaveFile(item);
                      Object.assign(item, res); // 合并返回的id等字段
                    }

                    values[item.field] = item;
                  }
                }

                if (fileList2) {
                  for (const item of fileList2) {
                    // 处理item不存在的情况,item存进数据库
                    if (!item.id && item.id !== 0) {
                      const res = await SaveFile(item);
                      Object.assign(item, res);
                    }

                    values[item.field] = item;
                  }
                }
                const obj = removeEmptyValues({ ...base.value, ...values });
                const { status, message } = await save('InterfaceConstruction', obj, null);
                if (status == 'SUCCESS') {
                  createMessage.success('保存成功');
                  resetFields();
                  closeModal1();
                  eruptTable.value.refresh();
                } else {
                  createMessage.error('保存失败:' + message);
                }
              }
              if (type.value == '编辑') {
                if (fileList1) {
                  for (const item of fileList1) {
                    if (item.url || item.des) {
                      values[item.field] = item;
                    }
                    values[item.field] = item;
                  }
                }

                if (fileList2) {
                  for (const item of fileList2) {
                    if (item.url || item.des) {
                      values[item.field] = item;
                    }
                    values[item.field] = item;
                  }
                }
                const obj = removeEmptyValues({ ...base.value, ...values });
                const { status, message } = await update('InterfaceConstruction', obj, null);
                if (status == 'SUCCESS') {
                  createMessage.success('保存成功');
                  resetFields();
                  closeModal1();
                  eruptTable.value.refresh();
                } else {
                  createMessage.error('保存失败:' + message);
                }
              }
              console.log(values);
              Modal.destroyAll(); //确保所有弹窗关闭
            },
            onCancel: () => {
              Modal.destroyAll();
            },
            style: {
              top: '40%',
            },
          });
        },
        handleCancelEdit: () => {},
        base,
      };
    },
  });
</script>

<style scoped>
  .desc-table {
    width: 100%;
    border-collapse: collapse;
  }

  .desc-table td {
    padding: 8px;
    border: 1px solid #ddd;
  }

  .table-header td {
    background-color: #f2f2f2; /* 标题栏和字段名设为灰色 */
  }

  .section-title {
    background-color: #f2f2f2; /* 分类标题设为灰色 */
  }
</style>
