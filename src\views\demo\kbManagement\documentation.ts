import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetDocumentationTree = '/documentation/tree',
  AddDocumentationFolder = '/documentation/add',
  DeleteDocumentationFolder = '/documentation/delete',
  RenameDocumentationFolder = '/documentation/rename',
  GetDocumentationList = '/documentation/list',
  AddDocumentationFile = '/documentation/saveFile',
  DeleteDocumentationFile = '/documentation/deleteFile',
}

export interface DocumentationDirectoryModel {
  id: number;
  name: string;
  parentId: number;
  parentName: string;
  module: number;
  children?: DocumentationDirectoryModel[];
}

export interface AddFolderParams {
  name: string;
  parentId?: number;
  module: number;
}

export interface DeleteFolderParams {
  id: number;
}

export interface RenameFolderParams {
  id: number;
  name: string;
}

export interface DocumentationFileModel {
  id: number;
  lineId: string;
  type: string;
  des: string;
  fileName: string;
  fileSize: string;
  enclosure: string;
  directoryId: number;
}

export interface GetDocumentationListParams {
  directoryId: number;
  fileName: string;
}

export interface AddDocumentationFileParams {
  lineId: string;
  type: string;
  des: string;
  enclosure: string;
  directoryId: number;
}

export interface DeleteDocumentationFileParams {
  id: number;
}

export const getDocumentationTree = () => {
  return defHttp.get<DocumentationDirectoryModel[]>({
    url: Api.GetDocumentationTree + '/1',
  });
};

export const addDocumentationFolder = (params: AddFolderParams) => {
  return defHttp.post<DocumentationDirectoryModel>({
    url: Api.AddDocumentationFolder,
    data: params,
  });
};

export const deleteDocumentationFolder = (params: DeleteFolderParams) => {
  return defHttp.post<string>({
    url: Api.DeleteDocumentationFolder,
    data: params,
  });
};

export const renameDocumentationFolder = (params: RenameFolderParams) => {
  return defHttp.post<DocumentationDirectoryModel>({
    url: Api.RenameDocumentationFolder,
    data: params,
  });
};

export const getDocumentationList = (params: GetDocumentationListParams) => {
  return defHttp.post<DocumentationFileModel[]>({
    url: Api.GetDocumentationList,
    params,
  });
};

export const addDocumentationFile = (params: AddDocumentationFileParams) => {
  return defHttp.post<DocumentationFileModel>({
    url: Api.AddDocumentationFile,
    data: params,
  });
};

export const deleteDocumentationFile = (params: DeleteDocumentationFileParams) => {
  return defHttp.post<string>({
    url: Api.DeleteDocumentationFile,
    data: params,
  });
};
