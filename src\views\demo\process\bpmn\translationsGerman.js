export default {
  Name: '名称',
  Value: '值',
  ID: '唯一标识(ID)',
  General: '基础属性',

  'Activate the create/remove space tool': '启动创建/删除空间工具',
  'Activate the global connect tool': '启动全局连接工具',
  'Activate the hand tool': '启动手动工具',
  'Activate the lasso tool': '启动套索工具',
  'Ad-hoc': 'Ad-hoc子流程',
  'Add Lane above': '添加到通道之上',
  'Add Lane below': '添加到通道之下',
  'Append ConditionIntermediateCatchEvent': '添加中间条件捕获事件',
  'Append element': '添加元素',
  'Append EndEvent': '添加结束事件',
  'Append Gateway': '添加网关',
  'Append Intermediate/Boundary Event': '添加中间/边界事件',
  'Append MessageIntermediateCatchEvent': '添加消息中间捕获事件',
  'Append ReceiveTask': '添加接收任务',
  'Append SignalIntermediateCatchEvent': '添加信号中间捕获事件',
  'Append Task': '添加任务',
  'Append TimerIntermediateCatchEvent': '添加定时器中间捕获事件',
  'Append compensation activity': '追加补偿活动',
  'Append {type}': '追加 {type}',
  'Boundary Event': '边界事件',
  'Business Rule Task': '规则任务',
  'Call Activity': '引用流程',
  'Cancel Boundary Event': '取消边界事件',
  'Cancel End Event': '取消结束事件',
  'Change type': '更改类型',
  'Collapsed Pool': '折叠池',
  Collection: '集合',
  'Compensation Boundary Event': '补偿边界事件',
  'Compensation End Event': '结束补偿事件',
  'Compensation Intermediate Throw Event': '中间补偿抛出事件',
  'Compensation Start Event': '补偿启动事件',
  'Complex Gateway': '复杂网关',
  'Conditional Boundary Event': '条件边界事件',
  'Conditional Boundary Event (non-interrupting)': '条件边界事件 (非中断)',
  'Conditional Flow': '条件流',
  'Conditional Intermediate Catch Event': '中间条件捕获事件',
  'Conditional Start Event': '条件启动事件',
  'Conditional Start Event (non-interrupting)': '条件启动事件 (非中断)',
  'Connect using Association': '文本关联',
  'Connect using DataInputAssociation': '数据关联',
  'Connect using Sequence/MessageFlow or Association': '消息关联',
  'Create IntermediateThrowEvent/BoundaryEvent': '创建中间抛出/边界事件',
  'Create DataObjectReference': '创建数据对象引用',
  'Create DataStoreReference': '创建数据存储引用',
  'Create element': '创建元素',
  'Create EndEvent': '创建结束事件',
  'Create Gateway': '创建网关',
  'Create Group': '创建组',
  'Create Intermediate/Boundary Event': '创建中间/边界事件',
  'Create Pool/Participant': '创建池/参与者',
  'Create StartEvent': '创建开始事件',
  'Create Task': '创建任务',
  'Create expanded SubProcess': '创建可折叠子流程',
  'Create {type}': '创建 {type}',
  Data: '数据',
  'Data Object Reference': '数据对象引用',
  'Data Store Reference': '数据存储引用',
  'Default Flow': '默认流',
  'Divide into three Lanes': '分成三条通道',
  'Divide into two Lanes': '分成两条通道',
  'Empty Pool': '空泳道',
  'Empty Pool (removes content)': '清空泳道（删除内容）',
  'End Event': '结束事件',
  'Error Boundary Event': '错误边界事件',
  'Error End Event': '结束错误事件',
  'Error Start Event': '错误启动事件',
  'Escalation Boundary Event': '升级边界事件',
  'Escalation Boundary Event (non-interrupting)': '升级边界事件 (非中断)',
  'Escalation End Event': '结束升级事件',
  'Escalation Intermediate Throw Event': '中间升级抛出事件',
  'Escalation Start Event': '升级启动事件',
  'Escalation Start Event (non-interrupting)': '升级启动事件 (非中断)',
  Events: '事件',
  'Event Sub Process': '事件子流程',
  'Event based Gateway': '事件网关',
  'Exclusive Gateway': '独占网关',
  'Expanded Pool': '展开泳道',
  Gateways: '网关',
  'Inclusive Gateway': '包容网关',
  'Intermediate Throw Event': '中间抛出事件',
  'Link Intermediate Catch Event': '中间链接捕获事件',
  'Link Intermediate Throw Event': '中间链接抛出事件',
  Loop: '循环',
  'Manual Task': '手动任务',
  'Message Boundary Event': '消息边界事件',
  'Message Boundary Event (non-interrupting)': '消息边界事件 (非中断)',
  'Message End Event': '结束消息事件',
  'Message Intermediate Catch Event': '中间消息捕获事件',
  'Message Intermediate Throw Event': '中间消息抛出事件',
  'Message Start Event': '消息启动事件',
  'Message Start Event (non-interrupting)': '消息启动事件 (非中断)',
  'Parallel Gateway': '并行网关',
  'Parallel Multi Instance': '并行多实例',
  Participants: '参与者',
  'Participant Multiplicity': '参与者多重性',
  'Receive Task': '接受任务',
  Remove: '移除',
  'Script Task': '脚本任务',
  'Send Task': '发送任务',
  'Sequence Flow': '顺序流',
  'Sequential Multi Instance': '串行多实例',
  'Service Task': '服务任务',
  'Signal Boundary Event': '信号边界事件',
  'Signal Boundary Event (non-interrupting)': '信号边界事件 (非中断)',
  'Signal End Event': '结束信号事件',
  'Signal Intermediate Catch Event': '中间信号捕获事件',
  'Signal Intermediate Throw Event': '中间信号抛出事件',
  'Signal Start Event': '信号启动事件',
  'Signal Start Event (non-interrupting)': '信号启动事件 (非中断)',
  'Start Event': '开始事件',
  'Sub Process': '子流程',
  'Sub Processes': '子流程',
  'Sub Process (collapsed)': '可折叠子流程',
  'Sub Process (expanded)': '可展开子流程',
  Task: '任务',
  Tasks: '任务',
  'Terminate End Event': '终止边界事件',
  'Timer Boundary Event': '定时边界事件',
  'Timer Boundary Event (non-interrupting)': '定时边界事件 (非中断)',
  'Timer Intermediate Catch Event': '中间定时捕获事件',
  'Timer Start Event': '定时启动事件',
  'Timer Start Event (non-interrupting)': '定时启动事件 (非中断)',
  Transaction: '事务',
  'User Task': '用户任务',
  'already rendered {element}': '{element} 已呈现',
  'diagram not part of bpmn:Definitions': '图表不是 bpmn:Definitions 的一部分',
  'element required': '需要元素',
  'correcting missing bpmnElement on {plane} to {rootElement}':
    '在 {plane} 上更正缺失的 bpmnElement 为 {rootElement}',
  'element {element} referenced by {referenced}#{property} not yet drawn':
    '元素 {element} 的引用 {referenced}#{property} 尚未绘制',
  'failed to import {element}': '{element} 导入失败',
  'flow elements must be children of pools/participants': '元素必须是池/参与者的子级',
  'more than {count} child lanes': '超过 {count} 条通道',
  'missing {semantic}#attachedToRef': '在 {element} 中缺少 {semantic}#attachedToRef',
  'multiple DI elements defined for {element}': '为 {element} 定义了多个 DI 元素',
  'no bpmnElement referenced in {element}': '{element} 中没有引用 bpmnElement',
  'no diagram to display': '没有要显示的图表',
  'no shape type specified': '未指定形状类型',
  'no parent for {element} in {parent}': '在 {element} 中没有父元素 {parent}',
  'no process or collaboration to display': '没有可显示的流程或协作',
  'out of bounds release': '越界释放',
  'Version tag': '版本标记',
  'Change element': '改变元素',
  Documentation: '文档',
  PROCESS: '流程',
  'Element documentation': '元素文档说明',
  'User assignment': '分配用户',
  'History cleanup': '历史记录清理',
  'Time to live': '历史记录生存时间',
  Tasklist: '任务列表',
  'Candidate starter': '候选启动器',
  'Candidate starter groups': '候选启动组',
  'Specify more than one group as a comma separated list.': "多个组用','分隔.",
  'Candidate starter users': '候选发起人',
  'Specify more than one user as a comma separated list.': "多个用户用','分隔.",
  'External task': '外部任务',
  Startable: '可启动(Startable)',
  Executable: '可直接执行',
  'Job execution': '作业执行',
  Priority: '优先级',
  Forms: '表单',
  'Execution listeners': '执行侦听器',
  'Extension properties': '扩展属性',
  'Event type': '事件类型',
  'Listener type': '侦听器类型',
  'Field injection': '字段注入',
  'Start initiator': '开始发起人',
  Initiator: '发起人',
  'Asynchronous continuations': '异步延续',
  Before: '之前',
  After: '之后',
  Inputs: '输入',
  Outputs: '输出',
  'Local variable name': '局部变量名称',
  'Assignment type': '分配类型',
  Format: '格式',
  Type: '类型',
  Expression: '表达式(Expression)',
  Script: '脚本(Script)',
  'Delegate expression': '委托表达式(Delegate expression)',
  'Java class': 'Java类(Java class)',
  start: '开始(start)',
  end: '结束(end)',
  'Start typing "${}" to create an expression.': '开始键入"${}"以创建表达式.',
  'Process variable name': '过程变量名称',
  'List values': '列表值',
  'Map entries': '映射条目',
  Key: '键',
  Values: '值',
  'Form reference': '引用表单ID',
  Binding: '结合',
  Version: '版本',
  'Form fields': '表单字段',
  'Form key': '表单ID',
  'Embedded or External Task Forms': '拓展表单',
  'Camunda Forms': '标准表单',
  'Generated Task Forms': '内置表单',
  'Refers to the process variable name': '指的是(引用)过程变量名称',
  Label: '标签',
  'Default value': '默认值',
  Constraints: '限制',
  Properties: '属性',
  Config: '配置',
  Implementation: '实施',
  'Field injections': '字段注入',
  'Task listeners': '任务侦听器',
  'Listener ID': '侦听器ID',
  Message: '消息',
  'Global message reference': '引用全局消息ID',
  'Result variable': '结果变量',
  Resource: '资源',
  'External resource': '外部资源',
  'Inline script': '内联脚本',
  'Process variables': '过程变量',
  'Global signal reference': '引用全局信号ID',
  Signal: '信号',
  'Called element': '被调用元素',
  'In mapping propagation': '在映射传播中',
  'Propagate all variables': '传播所有变量',
  'Out mapping propagation': '向外映射传播',
  'In mappings': '在映射中',
  Source: '来源',
  Target: '目标',
  Local: '局部的(Local)',
  'Out mappings': '输出映射',
  Link: '链接',
  Timer: '定时器',
  'Retry time cycle': '重试时间周期',
  'Variable name': '变量名称',
  'Condition Expression': '条件表达式',
  Condition: '条件',
  'Process documentation': '流程文档',
  Assignee: '委托人',
  'Candidate groups': '候选组',
  'Candidate users': '候选用户',
  'Due date': '期限',
  'The due date as an EL expression (e.g. ${someDate}) or an ISO date (e.g. 2015-06-26T09:54:00).':
    '到期日期为EL表达式(例如${someDate})或ISO日期(例如2015-06-26T09:54:00)',
  'Follow up date': '跟进日期',
  'The follow up date as an EL expression (e.g. ${someDate}) or an ISO date (e.g. 2015-06-26T09:54:00).':
    '作为EL表达式(例如${someDate})或ISO日期(例如2015-06-26T09:54:00)的跟进日期',
  'Connector ID': '连接器ID',
  'Connector inputs': '连接器输入',
  'Connector outputs': '连接器输出',
  Topic: '主题',
  Errors: '错误',
  'Global error reference': '引用全局错误ID',
  'Throw expression': 'Throw表达式',
  'Decision reference': '引用决策ID',
  'Tenant ID': '租户ID',
  'Multi-instance': '多实例',
  'Loop cardinality': '循环基数',
  'Completion condition': '完成条件',
  'Element variable': '元素变量',
  'Asynchronous before': '异步之前',
  'Asynchronous after': '异步之后',
};
