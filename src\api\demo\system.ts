import {
  AccountParams,
  DeptListItem,
  PersonParams,
  RoleParams,
  RolePageParams,
  PersonListGetResultModel,
  DeptListGetResultModel,
  AccountListGetResultModel,
  RolePageListGetResultModel,
  RoleListGetResultModel,
  RoleInfo,
  AccountInfo,
  MenuListGetResultModel,
} from './model/systemModel';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  AccountList = '/system/getAccountList',
  IsAccountExist = '/system/accountExist',
  DeptList = '/system/getDeptList',
  SetDeptFlag = '/system/setDeptFlag',
  setRoleStatus = '/system/setRoleStatus',
  PersonList = '/system/getPersonList',
  RolePageList = '/system/getRoleListByPage',
  GetAllRoleList = '/system/getAllRoleList',
  GetUserModel = '/system/getUserModel',
  ResetPassword = '/system/resetPassword',
  ResetAllPassword = '/system/resetAllPassword',
  disableAccount = '/system/disableAccount',
  enableAccount = '/system/enableAccount',
  RoleSaveOrUpdate = '/system/role/saveOrUpdate',
  RoleDelete = '/system/role/delete',
  AccountSaveOrUpdate = '/system/account/saveOrUpdate',
  MenuList = '/system/getMenuList',
  UpdatePeople = '/system/updateDeptAndPerson',
  CreateAccount = '/system/createAccount',
  DeleteAccount = '/system/deleteAccount',
}

export const getAccountList = (params: AccountParams) =>
  defHttp.post<AccountListGetResultModel>({ url: Api.AccountList, params });

export const getDeptList = (params?: DeptListItem) =>
  defHttp.post<DeptListGetResultModel>({ url: Api.DeptList, params });

export const setDeptFlag = (id: number) =>
  defHttp.post({ url: Api.SetDeptFlag, params: { id }});

export const getPersonList = (params?: PersonParams) =>
  defHttp.post<PersonListGetResultModel>({ url: Api.PersonList, params });

export const getRoleListByPage = (params?: RolePageParams) =>
  defHttp.post<RolePageListGetResultModel>({ url: Api.RolePageList, params });

export const getAllRoleList = (params?: RoleParams) =>
  defHttp.post<RoleListGetResultModel>({ url: Api.GetAllRoleList, params });

export const setRoleStatus = (id: number, status: string) =>
  defHttp.post({ url: Api.setRoleStatus, params: { id, status } });

export const isAccountExist = (account: string) =>
  defHttp.post({ url: Api.IsAccountExist, params: { account } }, { errorMessageMode: 'none' });

export const getUserModel = (id: number) =>
  defHttp.post({ url: Api.GetUserModel, params: { id } }, { errorMessageMode: 'none' });

export const getMenuList = () =>
  defHttp.post<MenuListGetResultModel>({ url: Api.MenuList }, { errorMessageMode: 'none' });

export const resetPassword = (id: number) =>
  defHttp.post<string>({ url: Api.ResetPassword, params: { id } }, { errorMessageMode: 'message' });
export const resetAllPassword = () =>
  defHttp.post({ url: Api.ResetAllPassword }, { errorMessageMode: 'message' });

export const enableAccount = (id: number) =>
  defHttp.post<string>({ url: Api.enableAccount, params: { id } }, { errorMessageMode: 'message' });

export const disableAccount = (id: number) =>
  defHttp.post<string>(
    { url: Api.disableAccount, params: { id } },
    { errorMessageMode: 'message' },
  );

export const roleSaveOrUpdate = (params?: RoleInfo) =>
  defHttp.post({ url: Api.RoleSaveOrUpdate, params });

export const roleDeleteByIds = (params?: number) =>
  defHttp.post({ url: Api.RoleDelete + '/', params });

export const AccountSaveOrUpdate = (params?: AccountInfo) =>
  defHttp.post({ url: Api.AccountSaveOrUpdate, params });

export const UpdatePeople = () => {
  return defHttp.post({ url: Api.UpdatePeople });
};

export const createAccount = async (id) => {
  return await defHttp.post({ url: Api.CreateAccount, params: { id: id } });
};


export const deleteAccount = async (id) => {
  return await defHttp.post({ url: Api.DeleteAccount, params: { id: id } });
};
