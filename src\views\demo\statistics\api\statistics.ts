import { defHttp } from '/@/utils/http/axios';
enum Api {
  SPECIALCHART = '/newline/statistics/getSpecialStats',
  SPECIALTABLE = '/newline/statistics/getSpecialTable',
  INTEGRATEDCHART = '/newline/statistics/getIntegratedStats',
  INTEGRATEDTABLE = '/newline/statistics/getIntegratedTable',
  TRANSFERCHART = '/newline/statistics/getTransferStats',
  TRANSFERTABLE = '/newline/statistics/getTransferTable',
  QUALITY = '/newline/statistics/chart',
  getQUALITY = '/newline/statistics/table',
  getASSEMBLY = '/newline/statistics/getAssembly',
  getASSEMBLYOne = '/newline/statistics/getAssemblyOne',
  GetMeeting = '/newline/statistics/meetingStatistics',
  GetMeetingDept = '/newline/statistics/meetingDeptStatistics',
  GetMeetingLine = '/newline/statistics/meetingLineStatistics',
  GetMeetingDailyStats = '/newline/statistics/meetingDailyStatistics',
  getInterfaceConstruction = '/newline/statistics/getInterfaceConstruction',
  getTableData = '/newline/statistics/getTableData',
  meetingHtmlstc = '/newline/statistics/meetingHtmlstc',
}

export const getMeetingDailyStats = async (month?: string) => {
  return await defHttp.get({
    url: Api.GetMeetingDailyStats,
    params: { month },
  });
};
export const meetingHtmlstc = async () => {
  return await defHttp.get({ url: Api.meetingHtmlstc });
};
export const getSpecialTable = (data) => {
  return defHttp.post({ url: Api.SPECIALTABLE, data: data });
};
export const getSpecialStats = (data) => {
  return defHttp.post({ url: Api.SPECIALCHART, data: data });
};
export const getIntegratedTable = (data) => {
  return defHttp.post({ url: Api.INTEGRATEDTABLE, data: data });
};
export const getIntegratedStats = (data) => {
  return defHttp.post({ url: Api.INTEGRATEDCHART, data: data });
};

export const getTransferTable = (data) => {
  return defHttp.post({ url: Api.TRANSFERTABLE, data: data });
};
export const getTransferStats = (data) => {
  return defHttp.post({ url: Api.TRANSFERCHART, data: data });
};

export const getQualityTable = (data) => {
  return defHttp.post({ url: Api.getQUALITY, data: data });
};
export const getQualityStats = (data) => {
  return defHttp.post({ url: Api.QUALITY, data: data });
};
export const getTableData = async (data) => {
  return await defHttp.post({ url: Api.getTableData, data: data });
};
export const getInterfaceConstruction = async (data) => {
  return await defHttp.post({ url: Api.getInterfaceConstruction, data: data });
};
/*export const getAssembly1 = async () => {
  return await defHttp.get({ url: Api.getASSEMBLY });
};*/
export const getAssembly1 = (data) => {
  return defHttp.post({ url: Api.getASSEMBLY, data: data });
};

export const getAssemblyOne = () => {
  return defHttp.post({ url: Api.getASSEMBLYOne });
};
export const getMeetingStats = async () => {
  return await defHttp.get({ url: Api.GetMeeting });
};

export const getMeetingDeptStats = async () => {
  return await defHttp.get({ url: Api.GetMeetingDept });
};

export const getMeetingLineStats = async () => {
  return await defHttp.get({ url: Api.GetMeetingLine });
};
