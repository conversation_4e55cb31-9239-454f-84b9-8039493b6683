<template>
  <div>
    <erupt-table class-name="AssemblyTemplate" :extra-action="extraAction"></erupt-table>
    <BasicModal
      @register="register"
      :showFooter="true"
      title="请选择线路"
      @ok="handleSubmit"
      @close="handleCancel"
    >
      <BasicForm style="height: 350px" @register="registerForm"></BasicForm>
    </BasicModal>
    <BasicModal
      @register="register1"
      :showFooter="true"
      title="请上传附件"
      @ok="handleSubmit1"
      @close="handleCancel1"
    >
      <BasicForm style="height: 300px" @register="registerForm1"></BasicForm>
    </BasicModal>
  </div>
</template>
<script lang="ts">
import {defineComponent, ref} from 'vue';
import eruptTable from "../../../../components/EruptTable/eruptTable";
import {NColorPicker} from 'naive-ui'
import {useRouter} from "vue-router";
import {useModal} from "/@/components/Modal";
import BasicForm from "/@/components/Form/src/BasicForm.vue";
import BasicModal from "/@/components/Modal/src/BasicModal.vue";
import {useForm} from "/@/components/Form";
import {
  GetLineFilter,
  getOptionsApi,
  getUpLoadApi,
  issuedApi,
  persistApi
} from "/@/views/demo/system/assembly/api/assembly";
import {useMessage} from "/@/hooks/web/useMessage";
export default defineComponent({
  name: 'AssemblyTemplate',
  components: {BasicModal, BasicForm, eruptTable, NColorPicker },
  setup() {
    const router = useRouter();
    const [register, { openModal, closeModal }] = useModal();
    const schema = ref([{
      field: 'file',
      label: '附件',
      required: true,
      component: 'Attachment',
      colProps: { span: 12 },
    }])
    const [register1, {openModal:openModal1, closeModal:closeModal1}] = useModal();
    const {createMessage} = useMessage();
    const rowId = ref()
    const [registerForm, {resetFields, validate}] = useForm({
      labelWidth: 100,
      schemas: [
        /*{
          field: 'isAuto',
          label: '是否自动线路',
          required: true,
          component: 'Select',
          colProps: { span: 16 },
          componentProps: {
            options: [{label: '全自动', value: 'auto'},{label: '非全自动', value: 'unauto'}]
          },
        },*/
        {
        field: 'lineName',
        label: '线路',
        required: true,
        component: 'ApiSelect',
        colProps: { span: 16 },
        componentProps: {
          api: GetLineFilter,
          resultField: 'list',
          // use name as label
          labelField: 'label',
          // use id as values
          valueField: 'value',
          // not request untill to select
          placeholder: '请选择线路',
        },
      }],
      showActionButtonGroup: false,
    });

    const [registerForm1, {resetFields:resetFields1, setFieldsValue: setFieldsValue1,validate:validate1}] = useForm({
      labelWidth: 100,
      schemas: schema,
      showActionButtonGroup: false,
    })
    return {
      register,
      openModal,
      closeModal,
      register1,
      openModal1,
      closeModal1,
      registerForm1,
      resetFields1,
      validate1,
      handleSubmit: async () => {
        const data = await validate();
        issuedApi(rowId.value, data)
        createMessage.success('正在下发')

        /*then(res => {
          if (res == 200) {
            createMessage.success('下发完成')
          } else {
            createMessage.error('下发失败')
          }
          closeModal()
        })*/
      },
      handleSubmit1: () => {
        persistApi("assTpl_" + rowId.value).then(res => {
          if (res) {
            createMessage.success('导入完成')
          } else {
            createMessage.success('导入失败')
          }
          closeModal1()
        })
      },
      handleCancel: () => {

      },
      registerForm,

      extraAction: {
        nums: 3,
        actions: (row) => {return [
          {
            label: '明细',
            onClick: () => {
              router.push({path: 'assembly/tpl/items/' +row.row.id, query: row.row})
            },
          },
          {
            label: '下发',
            onClick: () => {

              rowId.value = row.row.id
              openModal()
            },
          },
          {
            label: '导入',
            onClick: () => {
              rowId.value = row.row.id
              schema.value = [{
                field: 'file',
                label: '附件',
                required: true,
                component: 'Attachment',
                colProps: { span: 12 },
                componentProps: {
                  multiple:false,
                  maxNumber:1,
                  accept:['xlsx,xls'],
                  maxSize: 50,
                  api: getUpLoadApi(row.row.id)
                }
              }]
              openModal1()
             /* updateSchema1([{
                field: 'file',
                label: '附件',
                required: true,
                component: 'Attachment',
                colProps: { span: 12 },
                componentProps: {
                  multiple:false,
                  accept:['xlsx,xls'],
                  maxSize: 50,
                  api: getUpLoadApi(row.row.id)
                }
              }])*/


            },
          }
        ]}
      }
    };
  }
});
</script>

