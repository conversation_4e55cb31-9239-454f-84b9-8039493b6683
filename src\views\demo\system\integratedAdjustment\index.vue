<template>
  <div>
  <erupt-table
    title="综合联调"
    seq="true"
    ref="eruptTableRef"
    class-name="IntegratedAdjustment"
    :extra-action="extraAction"
    :overwrite-action="overwriteAction"
    :pre-conditions="preCondition"
    :query-sort="querySort"
   >
  <template #state="{ row }">
        <div v-if="row.state == '已发布'" style="align-self: center">
          <a-tag color="red" style="width: 60px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
        <div v-if="row.state == '实施中'" style="align-self: center">
          <a-tag color="green" style="width: 60px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
        <div v-if="row.state == '已闭环'" style="align-self: center">
          <a-tag color="yellow" style="width: 60px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
      </template>
      </erupt-table>
  <BasicModal @register="register" :showFooter="false" width="80%" title="详情">
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="详细信息">
          <table class="desc-table">
            <tbody>
              <tr class="table-header">
                <td>计划名称</td>
                <td>线路</td>
                <td>任务</td>
                <td>分类</td>
              </tr>
              <tr>
                <td>{{ formatTemplate(descData['template']) }}</td>
                <td>{{ descData['lineName'] }}</td>
                <td>{{ formatTask(descData['task']) }}</td>
                <td>{{ formatClassification(descData['classification']) }}</td>
              </tr>
              <tr class="table-header">
                <td>项目</td>
                <td>任务编码</td>
                <td>级别</td>
                <td>实施专业</td>
              </tr>
              <tr>
                <td>{{ descData['project'] }}</td>
                <td>{{ descData['taskCode'] }}</td>
                <td>{{ formatLevel(descData['level']) }}</td>
                <td>{{ descData['major'] }}</td>
              </tr>
              <tr class="table-header">
                <td>实施单位</td>
                <td>配合单位</td>
                <td>计划完成时间</td>
                <td></td>
              </tr>
              <tr>
                <td>{{ formatMainDept(descData['mainDept']) }}</td>
                <td>{{  descData['otherDept']}}</td>
                <td>{{
                  descData['planDate'] ? new Date(descData['planDate']).toLocaleDateString() : ''
                }}</td>
                <td></td>
              </tr>
            </tbody>
          </table>
          <table class="desc-table">
            <tbody>
              <tr class="table-header">
                <td>备注</td>
              </tr>
              <tr>
                <td colspan="4">{{ descData['note'] }}</td>
              </tr>
            </tbody>
          </table>
        </a-collapse-panel>


        <a-collapse-panel key="2" header="附件">
  <a-descriptions layout="vertical" :column="2" bordered :label-style="{ fontWeight: 'bold' }">
    <!-- 实施申报表 -->
    <a-descriptions-item v-if="[0].includes(Number(descData.task))" label="实施申报表">
      <template v-if="descData.file1">
        <div v-for="(item, index) in (descData.file1.split('|') || [])" :key="index">
          <a @click="previewShow(item, index)">{{ getFileName(item) }}</a>
          <a :href="eruptAttachment + item" style="margin-left: 10px; color: #ff6118" target="_blank">下载</a>
        </div>
      </template>
    </a-descriptions-item>

    <!-- 实施方案 -->
    <a-descriptions-item v-if="[0,1].includes(Number(descData.task))" label="实施方案">
      <template v-if="descData.file2">
        <div v-for="(item, index) in (descData.file2.split('|') || [])" :key="index">
          <a @click="previewShow(item, index)">{{ getFileName(item) }}</a>
          <a :href="eruptAttachment + item" style="margin-left: 10px; color: #ff6118" target="_blank">下载</a>
        </div>
      </template>
    </a-descriptions-item>

    <!-- 实施记录表 -->
    <a-descriptions-item v-if="[0,1].includes(Number(descData.task))" label="实施记录表">
      <template v-if="descData.file3">
        <div v-for="(item, index) in (descData.file3.split('|') || [])" :key="index">
          <a @click="previewShow(item, index)">{{ getFileName(item) }}</a>
          <a :href="eruptAttachment + item" style="margin-left: 10px; color: #ff6118" target="_blank">下载</a>
        </div>
      </template>
    </a-descriptions-item>

    <!-- 实施评估报告 -->
    <a-descriptions-item v-if="[0].includes(Number(descData.task))" label="实施评估报告">
      <template v-if="descData.file4">
        <div v-for="(item, index) in (descData.file4.split('|') || [])" :key="index">
          <a @click="previewShow(item, index)">{{ getFileName(item) }}</a>
          <a :href="eruptAttachment + item" style="margin-left: 10px; color: #ff6118" target="_blank">下载</a>
        </div>
      </template>
    </a-descriptions-item>

    <!-- 质量评估报告 -->
    <a-descriptions-item v-if="[0].includes(Number(descData.task))" label="质量评估报告">
      <template v-if="descData.file5">
        <div v-for="(item, index) in (descData.file5.split('|') || [])" :key="index">
          <a @click="previewShow(item, index)">{{ getFileName(item) }}</a>
          <a :href="eruptAttachment + item" style="margin-left: 10px; color: #ff6118" target="_blank">下载</a>
        </div>
      </template>
    </a-descriptions-item>

    <!-- 项目交底会 -->
    <a-descriptions-item v-if="[1].includes(Number(descData.task))" label="项目交底会">
      <template v-if="descData.file6">
        <div v-for="(item, index) in (descData.file6.split('|') || [])" :key="index">
          <a @click="previewShow(item, index)">{{ getFileName(item) }}</a>
          <a :href="eruptAttachment + item" style="margin-left: 10px; color: #ff6118" target="_blank">下载</a>
        </div>
      </template>
    </a-descriptions-item>

    <!-- 项目报告 -->
    <a-descriptions-item v-if="[1].includes(Number(descData.task))" label="项目报告">
      <template v-if="descData.file7">
        <div v-for="(item, index) in (descData.file7.split('|') || [])" :key="index">
          <a @click="previewShow(item, index)">{{ getFileName(item) }}</a>
          <a :href="eruptAttachment + item" style="margin-left: 10px; color: #ff6118" target="_blank">下载</a>
        </div>
      </template>
    </a-descriptions-item>
  </a-descriptions>
</a-collapse-panel>
      </a-collapse>
    </BasicModal>
  <BasicModal
      @register="registerEdit"
      :showFooter="false"
      width="80%"
      :title="type"
      :defaultFullscreen="true"
      @ok="handleSubmitEdit"

    >
      <BasicForm @register="registerFormEdit" style="height: 350px"> </BasicForm>
    </BasicModal>
    <BasicModal
  @register="registerModal"
  title="预览文件"
  :width="1000"
  :height="600"
  :footer="null"
>
  <iframe :key="key" :src="modalUrl" width="1000" height="800"></iframe>
</BasicModal>
  </div>
</template>
<script lang="ts">
  import eruptTable from '/@/components/EruptTable/eruptTable';
  import { h, ref, defineComponent, onMounted, computed } from 'vue';
  import { buildApi,getOne,save, update } from '/@/api/erupt/erupt';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRoute } from 'vue-router';
  import { Base64 } from 'js-base64';
import { useGlobSetting } from '/@/hooks/setting';
import { useUserStore } from '/@/store/modules/user';

  import { useModal,BasicModal } from '/@/components/Modal';
  import { useForm } from '/@/components/Form';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import {
  IsSure,GetDetail
} from '/@/views/demo/system/integratedAdjustment/api/index';

  import {
    getComponent,
    getComponentProps,
  } from '/@/components/EruptTable/componets';
  export default defineComponent({
    name: 'IntegratedAdjustment',
    components: { eruptTable,BasicModal,BasicForm },
    setup() {
      const { createMessage } = useMessage();
      const editSchemasWithoutFile = ref([]);
      const editSchemas = ref([]);
      const activeKey = ref(['1', '2']); // 默认展开详细信息和附件面板
      const modalUrl = ref('');
      const key = ref(0);
      const globSetting = useGlobSetting();
      const eruptAttachment = globSetting?.eruptAttachment;

      const taskStr = ref('');
      const templateOptions = ref([]);
      const taskOptions = ref([]);
      const classificationOptions = ref([]);
      const levelOptions = ref([]);
      const deptsOptions = ref([]);
      const depts1Options = ref([]);


      const type = ref('新增');
      const base = ref({});
      const eruptTableRef = ref();
      const { createConfirm } = useMessage();
      const rowId = ref();
      const descData = ref({});
      const eruptFieldModels = ref([]);

      const route = useRoute();
      const [register, { openModal, closeModal }] = useModal();
      const [registerEdit, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
      const [
        registerFormEdit,
        { resetFields, setFieldsValue, updateSchema, validate, getFieldsValue },
      ] = useForm({
        labelWidth: 100,
        wrapperCol: { span: 24 },
        schemas: editSchemas,
        showActionButtonGroup: false,
      });
      const [registerModal, { openModal: openModalPreview }] = useModal();
      const [registerForm] = useForm({
        labelWidth: 100,
        schemas: editSchemas,
        showActionButtonGroup: false,
      });

      const preCondition = new Map([]);
      const tplId = computed(() => route.query.tplId);

      onMounted(() => {
        if (tplId) {
          preCondition.set('template', tplId);
        }
        buildApi('IntegratedAdjustment').then((res) => {
          const {
            eruptModel: { eruptFieldModels },
          } = res;
          let edits = [];
          eruptFieldModels.forEach((item) => {
            const key = item.fieldName;
            const title = item.eruptFieldJson.edit.title;

            if (item.eruptFieldJson.edit.show.edit_show && key !== 'id') {
              if (
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
                item.eruptFieldJson.edit.type !== 'TAB_TREE'
              ) {
                const e = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, 'IntegratedAdjustment'),
                      disabled: item.eruptFieldJson.edit.readOnly.edit,
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };

                edits.push(e);
              }
            }
          });
          editSchemasWithoutFile.value = edits;
          edits.forEach((item) => {
            if (item.field == 'file1' || item.field == 'file4' || item.field == 'file5') {
              const props = { ...item.componentProps() };
              item.ifShow = ({ values }) => {
                console.log(values);
                return values.task == '0';
              };
            } else if (item.field == 'file6' || item.field == 'file7') {
              const props = { ...item.componentProps() };
              item.ifShow = ({ values }) => {
                console.log(values);
                return values.task == '1';
              };
            }else if (item.field == 'file2' || item.field == 'file3') {
              const props = { ...item.componentProps() };
              item.ifShow = ({ values }) => {
                console.log(values);
                return values.task == '1'||values.task == '0';
              };
            }
          }); 
          editSchemas.value = edits;
        }).catch(err => {
          console.error(err) 
        });
      });

      function previewShow(downloadUrl, index) {
      downloadUrl = encodeURIComponent(Base64.encode(globSetting.eruptAttachment + downloadUrl));
      const userStore = useUserStore();
      const watermarkTxt = userStore.userInfo.username + ' ' + userStore.userInfo.realName;
      modalUrl.value = '';
      modalUrl.value =
        globSetting.previewUrl +
        '/onlinePreview?url=' +
        downloadUrl +
        '&watermarkTxt=' +
        watermarkTxt;
      key.value = index;
      openModalPreview(true);
    }
    const borderStyle = {
    border: '1px solid #e8e8e8'
    };
    const headerStyle = {
      backgroundColor: '#fafafa',
      color: '#535151',
      fontWeight: 'bold',
      fontSize: '12',
      textAlign: 'left'
    };

    const getFileName = (path: string) => {
  return path.substring(path.lastIndexOf('/') + 1)
}

      function refresh() {
            document.querySelector('button[content="查询"]').click()
        }

        const formatTemplate = (templateValue) => {
        const matchedTemplate = templateOptions.value.find(item => item.value === templateValue);
        return matchedTemplate ? matchedTemplate.label : '-';
      };
      const formatTask = (taskValue) => {
          const matchedTask = taskOptions.value.find((task) => task.value === taskValue);
          return matchedTask ? matchedTask.label : '-';
        };

        const formatClassification = (classificationValue) => {
          const matchedClassification = classificationOptions.value.find(
            (item) => item.value === classificationValue
          );
          return matchedClassification ? matchedClassification.label : '-';
        };

        const formatLevel = (levelValue) => {
          const matchedLevel = levelOptions.value.find((item) => item.value === levelValue);
          return matchedLevel ? matchedLevel.label : '-';
        };

        const formatMainDept = (deptValue) => {
          const matchedDept = deptsOptions.value.find((item) => item.value === deptValue);
          return matchedDept ? matchedDept.label : '-';
        };

        const formatOtherDept = (deptValue) => {
          const matchedDept = depts1Options.value.find((item) => item.value === deptValue);
          return matchedDept ? matchedDept.label : '-';
        };

      const resetAllFeilds = () => {
              resetFields();
              setFieldsValue({
                id: null,
                createDateTime: null,
                creator: null,
                createUser: null,
                updateDateTime: null,
                updateUser: null,
                updator: null,
                version: 1,
                template: null,       // 计划名称
                lineId: null,         // 线路
                task: null,           // 任务
                classification: null, // 分类
                project: null,        // 项目
                taskCode: null,       // 任务编码
                level: null,          // 级别
                major: null,          // 实施专业
                mainDept: null,       // 实施单位
                otherDept: null,      // 配合单位
                planDate: null,       // 计划完成时间
                note: null,           // 备注
                file1: null,          // 实施申报表
                file2: null,      // 实施方案
                file3: null,          // 实施记录表
                file4: null,          // 实施评估报告
                file5: null,          // 质量评估报告
                file6: null,          // 项目交底会
                file7: null,          // 项目报告
                state: null           // 项目状态
              })}

      const overwriteAction = {
        add: async () => {
          debugger
          resetAllFeilds();
          type.value = '新增';
          setTimeout(() => {
            resetFields();
            openModal1();
            }, 200);
        },

        edit: async (row) => {
          type.value = '编辑';
          const res = await getOne('IntegratedAdjustment', row.id);
          base.value = res;
          setTimeout(() => {
            resetFields();
            openModal1();
            setTimeout(() => {
              setFieldsValue(res);
            }, 20);
          }, 200);
        },
        detail: async (row) => {
          const res = await GetDetail( row.id); // 获取详情数据
          const {
            eruptModel: { eruptFieldModels: fieldModels },
          } = await buildApi('IntegratedAdjustment');

          eruptFieldModels.value = fieldModels;
          debugger
          console.log("qweqweqwe12313",res);

          // 提取线路和区域的选择项
          const taskField = fieldModels.find((item) => item.fieldName === 'task');
          const classificationField = fieldModels.find((item) => item.fieldName === 'classification');
          const levelField = fieldModels.find((item) => item.fieldName === 'level');
          const deptsField = fieldModels.find((item) => item.fieldName === 'mainDept');
          const depts1Field = fieldModels.find((item) => item.fieldName === 'otherDept');
          const templateField = fieldModels.find((item) => item.fieldName === 'template');

          taskOptions.value = taskField?.componentValue || [];
          classificationOptions.value = classificationField?.componentValue || [];
          levelOptions.value = levelField?.componentValue || [];
          deptsOptions.value = deptsField?.componentValue || [];
          depts1Options.value = depts1Field?.componentValue || [];
          templateOptions.value = templateField?.componentValue || [];

          descData.value = res;

          setTimeout(() => {
            openModal();
          }, 200);
        },
      }
      
        const extraAction = {
        nums: 0.1,
        actions: (row) => {
          return [
            
            {
              label: '闭环',
              onClick: () => {
                createConfirm({
                  title: '确认闭环',
                  content: '确定要闭环吗？',
                  onOk() {
                    rowId.value = Number(row.row.id); // 强制转换为数字
                    IsSure(rowId.value).then((res) => {
                    if (res=="200") {
                      createMessage.success('闭环成功');
                      eruptTableRef.value.refresh();
                    } else {
                      createMessage.error('未上传所有文件，不能闭环');
                    }});
                  },
                  iconType: 'warning',
                });
              },
            },
          ];
        },
      };

      return {
        register,
        resetFields,
        type,
        overwriteAction,
        registerFormEdit,
        registerModal,
        registerEdit,
        openModal1,
        preCondition,
        updateSchema,
        validate, 
        getFieldsValue,
        closeModal1,
        closeModal,
        registerForm,
        extraAction,
        descData,
        activeKey,
        previewShow,
        modalUrl,
        key,
        eruptAttachment,
        borderStyle,
        headerStyle,
        getFileName,
        handleSubmitEdit: async () => {
          const value = await validate();
          if (type.value == '新增') {
            const { status, message } = await save('IntegratedAdjustment', value, null);
            if (status == 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              closeModal1();
              eruptTableRef.value.refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          }
          if (type.value == '编辑') {
            const { status, message } = await update('IntegratedAdjustment', { ...base.value, ...value }, null);
            if (status == 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              closeModal1();
              eruptTableRef.value.refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          }
        },
        formatTemplate,
        eruptTableRef,
        formatTask,
        formatClassification,
        formatLevel,
        formatMainDept,
        formatOtherDept,
        querySort: (querys) => {
          for (let i = 0; i < querys.length; i++) {
            querys[i].span = 8;
            if (i >= 3) {
              querys[i].folding = true;
            }
          }
          console.log('afterSort', querys);
          return querys;
        },
      };
    },
  });
</script>
<style scoped>
  .desc-table {
    width: 100%;
    border-collapse: collapse;
  }

  .desc-table td {
    padding: 8px;
    border: 1px solid #ddd;
  }

  .table-header td {
    background-color: #f2f2f2; /* 标题栏和字段名设为灰色 */
  }

  .section-title {
    background-color: #f2f2f2; /* 分类标题设为灰色 */
  }
</style>