<template>
  <Card title="运营筹备状态及类型完成率" :loading="loading">
    <template #extra>
      <a-space direction="horizontal">
        <a-select
          v-model="line"
          style="width: 200px"
          placeholder="请选择线路"
          :options="lineOpt"
          @change="setLine"
          :loading="loading"
          :disabled="loading"
          allowClear
        ></a-select>
        <a-select
          v-if="userStore.getUserInfo.dept === '新线管理部'"
          v-model="dept"
          style="width: 200px"
          placeholder="请选择部门"
          :options="deptOpt"
          @change="setDept"
          :loading="loading"
          :disabled="loading"
          allowClear
        ></a-select>
      </a-space>
    </template>
    <div v-loading="loadings" ref="chartRef" :style="{ width, height }"></div>
  </Card>
</template>
<script lang="ts" setup>
  import { Ref, ref, watch } from 'vue';
  import { Card } from 'ant-design-vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { useUserStore } from '/@/store/modules/user';
  import { useRouter } from 'vue-router';
  import { getOperation } from '/@/views/dashboard/workbench/components/api/home';

  const props = defineProps({
    loading: Boolean,
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '400px',
    },
    lineOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    deptOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });

  const line = ref('');
  const dept = ref('');
  const loadings = ref(true);
  const deptLabel = ref('');
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const userStore = useUserStore();
  const router = useRouter();

  const setLine = (val) => {
    loadings.value = true;
    line.value = val;
    getChartDate();
  };
  const setDept = (val) => {
    loadings.value = true;
    const selectedOption = props.deptOpt.find((option) => option.value === val);
    if (selectedOption) {
      deptLabel.value = selectedOption.label; // 假设你有一个变量 lineLabel 来保存 label
    }
    dept.value = val;
    getChartDate();
  };

  watch(
    () => props.loading,
    () => {
      if (props.loading) {
        return;
      }
      getChartDate();
    },
    { immediate: true },
  );

  const getChartDate = async () => {
    const data = {
      line: line.value,
      dept: dept.value,
    };
    try {
      const res = await getOperation(data);
      updateChart(res);
      loadings.value = false;
    } catch (error) {
      console.log(error);
    }
  };

  function updateChart(value) {
    setOptions({
      tooltip: {},
      xAxis: {
        type: 'category',
        data: value.name,
        name: '状态', // 设置x轴名称
        nameLocation: 'end', // 名称显示的位置
        nameTextStyle: {
          color: '#000', // 名称的颜色
          fontSize: 14, // 名称的字体大小
        },
        axisLabel: {
          formatter: function (value) {
            if (value.length > 8) {
              return value.slice(0, 8) + '\n' + value.slice(8);
            }
            return value;
          },
        },
      },
      yAxis: {
        name: '数量', // 设置y轴名称
        nameLocation: 'end', // 名称显示的位置
        nameGap: 21, // 调整名称与轴线之间的距离
        nameTextStyle: {
          color: '#333',
          fontSize: 14,
          fontWeight: 'bold',
          backgroundColor: '#f8f8f8', // 添加背景色
          borderColor: '#d9d9d9', // 边框颜色
          borderWidth: 1, // 边框宽度
          borderRadius: 4, // 圆角边框
          padding: [4, 8], // 内边距
        },
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
      },
      grid: {
        left: '10%', // 左边距
        right: '12%', // 右边距
        top: '10%',
        bottom: '10%',
      },
      series: [
        {
          name: '数量',
          type: 'bar', // 显示柱状图
          data: value.data || [0, 0, 0],
          barWidth: '20%',
          itemStyle: {
            color: function (params) {
              // 根据索引返回不同的颜色
              const colorList = ['#6A4C93', '#F7B731', '#3867D6'];
              return colorList[params.dataIndex];
            },
          },
          label: {
            show: true,
            position: 'top', // 或者其他位置如：'inside', 'right'
            formatter: '{c}', // 显示数值
            color: '#000', // 文本颜色
            fontSize: 14, // 字体大小
          },
        },
      ],
      graphic: [
        {
          type: 'group',
          right: 20, // 保持右侧位置
          top: 25, // 保持顶部位置
          children: value.name
            .map((state, index) => {
              const colorList = ['#6A4C93', '#F7B731', '#3867D6'];
              const color = colorList[index % colorList.length];
              return [
                {
                  type: 'text',
                  style: {
                    text: state,
                    fill: '#000000D9',
                    fontSize: 12, // 减小字体大小
                    textAlign: 'left',
                  },
                  top: `${index * 20}px`,
                  right: 45, // 距离右侧 30px
                  onclick: () => {
                    router.push({
                      path: '/newLine/operation/details',
                      query: {
                        line: line.value,
                        dept: deptLabel.value,
                        state: state,
                      },
                    });
                  },
                },
                {
                  type: 'rect',
                  shape: {
                    width: 25,
                    height: 14,
                    r: 5, // 添加圆角半径
                  },
                  style: {
                    fill: color,
                  },
                  top: `${index * 20}px`,
                  right: 19, // 距离右侧 15px
                  onclick: () => {
                    router.push({
                      path: '/newLine/operation/details',
                      query: {
                        line: line.value,
                        dept: deptLabel.value,
                        state: state,
                      },
                    });
                  },
                },
              ];
            })
            .flat(),
        },
      ],
    });
  }
</script>
