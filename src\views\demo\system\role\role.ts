import { defHttp } from '/@/utils/http/axios';
import { permissionListGetResultModel, rolePermissionItem } from './model/permission';

enum Api {
  PermissionList = '/system/getPermissionList',
  RolePermission = '/system/getRolePermission',
  SaveRolePermission = '/system/rolePermission/save',
}

export const getPermissionList = () =>
  defHttp.post<permissionListGetResultModel>({ url: Api.PermissionList });

export const getPermissionByRoleId = (id: number) =>
  defHttp.post({ url: Api.RolePermission, params: { id: id } });

export const saveRolePermission = (params: rolePermissionItem) =>
  defHttp.post<rolePermissionItem>({ url: Api.SaveRolePermission, params });
