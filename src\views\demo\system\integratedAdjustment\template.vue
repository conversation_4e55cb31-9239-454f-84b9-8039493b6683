
<template>
  <div>
    <erupt-table class-name="IgAdjustmentTemplate" 
    :overwrite-action="overwriteAction"></erupt-table>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import eruptTable from '../../../../components/EruptTable/eruptTable';
  import { useRouter } from 'vue-router';
  import { useModal } from '/@/components/Modal';
  import { useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { remove } from '/@/api/erupt/erupt'; // 导入原删除接口

  export default defineComponent({
    name: 'IgAdjustmentTemplate',
    components: { eruptTable },
    setup() {
      
  const router = useRouter();
  const { createConfirm } = useMessage();
  const [register, { openModal, closeModal }] = useModal();

  
  
  const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
  const { notification, createMessage } = useMessage();
  const rowId = ref();


  function refresh() {
            document.querySelector('button[content="查询"]').click()
        }
  const overwriteAction = ({
    detail: (row: any) => {
      router.push({
                    path: '/newLine/integratedAdjustment/details',
                    query: {
                      tplId: row.id,
                    },
                  });
      setTimeout(() => {
            openModal();
          }, 200);
    },
    delete: (row: any) => {
    createConfirm({
    content: '请确认是否删除综合联调项目（删除后综合联调详情相关内容同步删除）',
    onOk: async () => {
      try {
        debugger;
        const { status } = await remove('IgAdjustmentTemplate', row);
        debugger;
        if (status === 'SUCCESS') {
          createMessage.success('删除成功');
          close();
          refresh();
        }
      } catch (error) {
        createMessage.error('删除失败');
      }
    },
    onCancel: () => {
    },
    iconType: 'warning',
  });
},
  });
   
      return {
        overwriteAction,
        register,
        register1,
        openModal,
      };
    },
  });
</script>

