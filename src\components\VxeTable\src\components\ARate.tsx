import {
  create<PERSON><PERSON><PERSON><PERSON>,
  createD<PERSON><PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  createDefaultFilterR<PERSON>,
  createFormItemRender,
} from './common';

export default {
  renderDefault: createDefaultRender(),
  renderEdit: createEditRender(),
  renderFilter: createFilter<PERSON><PERSON>(),
  defaultFilterMethod: createDefaultFilterRender(),
  renderItemContent: createFormItemRender(),
};
