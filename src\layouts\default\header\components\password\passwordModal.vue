<template>
  <BasicModal
    :footer="null"
    :title="t('layout.header.modifyPassword')"
    v-bind="$attrs"
    :class="prefixCls"
    @register="register"
  >
    <div>
      <BasicForm @register="registerForm" />

      <div :class="`${prefixCls}__footer`">
        <a-button type="primary" block class="mt-2" @click="handleSubmit"> 提交 </a-button>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { BasicModal, useModalInner } from '/@/components/Modal/index';
  import { BasicForm, useForm } from '/@/components/Form/index';

  import { useUserStore } from '/@/store/modules/user';
  import { useLockStore } from '/@/store/modules/lock';
  import headerImg from '/@/assets/images/header.jpg';
  import { modifyPassword } from '/@/api/sys/user';
  import { useMessage } from '/@/hooks/web/useMessage';
  export default defineComponent({
    name: 'LockModal',
    components: { BasicModal, BasicForm },

    setup() {
      const { t } = useI18n();
      const { prefixCls } = useDesign('header-lock-modal');
      const userStore = useUserStore();
      const { createMessage } = useMessage();
      const getRealName = computed(() => userStore.getUserInfo?.realName);
      const [register, { closeModal }] = useModalInner();

      const [registerForm, { validateFields, resetFields }] = useForm({
        showActionButtonGroup: false,
        schemas: [
          {
            field: 'passwordOld',
            label: '当前密码',
            component: 'InputPassword',
            colProps: {
              span: 24,
            },
            required: true,
          },
          {
            field: 'passwordNew',
            label: '新密码',
            component: 'StrengthMeter',
            colProps: {
              span: 24,
            },
            componentProps: {
              placeholder: '新密码',
            },
            dynamicRules: ({ values }) => {
              return [
                {
                  required: true,
                  validator: (_, value) => {
                    if (!value) {
                      return Promise.reject('新密码不能为空');
                    }
                    if (value == values.passwordOld) {
                      return Promise.reject('新旧密码不得相同!');
                    }
                    return Promise.resolve();
                  },
                },
              ];
            },
          },
          {
            field: 'confirmPassword',
            label: '确认密码',
            component: 'InputPassword',
            colProps: {
              span: 24,
            },
            dynamicRules: ({ values }) => {
              return [
                {
                  required: true,
                  validator: (_, value) => {
                    if (!value) {
                      return Promise.reject('密码不能为空');
                    }
                    if (value !== values.passwordNew) {
                      return Promise.reject('两次输入的密码不一致!');
                    }
                    return Promise.resolve();
                  },
                },
              ];
            },
          },
        ],
      });

      async function handleSubmit() {
        const values = (await validateFields()) as any;
        console.log(values);
        const password: string | undefined = values.password;
        const userName = userStore.getUserInfo.username;
        const params = { userName, ...values };
        const res = await modifyPassword(params);
        if (res == 'success') {
          createMessage.success('密码修改成功！');
          closeModal();
        }
        /*closeModal();

      lockStore.setLockInfo({
        isLock: true,
        pwd: password,
      });
      await resetFields();*/
      }

      return {
        t,
        prefixCls,
        getRealName,
        register,
        registerForm,
        handleSubmit,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-lock-modal';

  .@{prefix-cls} {
    &__entry {
      position: relative;
      //height: 240px;
      padding: 130px 30px 30px;
      border-radius: 10px;
    }

    &__header {
      position: absolute;
      top: 0;
      left: calc(50% - 45px);
      width: auto;
      text-align: center;

      &-img {
        width: 70px;
        border-radius: 50%;
      }

      &-name {
        margin-top: 5px;
      }
    }

    &__footer {
      text-align: center;
    }
  }
</style>
