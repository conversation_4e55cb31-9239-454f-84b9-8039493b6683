<template>
  <div :class="prefixCls">
    <Popover title="" trigger="click" :overlayClassName="`${prefixCls}__overlay`">
      <Badge :count="count" dot :numberStyle="numberStyle">
        <span style="display: flex; align-items: center" @click="onNoticeClick">
          <BellOutlined /><span style="font-size: 12px; padding-right: 8px">消息</span>
        </span>
      </Badge>
    </Popover>
  </div>
</template>
<script lang="ts">
  import { computed, defineComponent, onMounted, ref } from 'vue';
  import VFormCreate from '/src/views/form-design/components/VFormCreate/index.vue';
  import { Popover, Tabs, Badge, Steps, Card, Collapse, CollapsePanel } from 'ant-design-vue';
  import { BellOutlined } from '@ant-design/icons-vue';
  import { tabListData, ListItem } from '../notify/data';
  import NoticeList from '../notify/NoticeList.vue';
  import { useDesign } from '/src/hooks/web/useDesign';
  import { useMessage } from '/src/hooks/web/useMessage';
  import {
    complete,
    getApprovalRecords,
    getClassName,
    getFlowsTask,
    getProcessComponent,
    getProcessComponentOnChange,
    getProcessOne,
  } from '/src/api/process/process';
  import { useTaskStore } from '/src/store/modules/task';
  import { Description } from '/src/components/Description';
  import { BasicModal, useModal } from '/src/components/Modal';
  import BasicForm from '/src/components/Form/src/BasicForm.vue';
  import { useForm } from '/src/components/Form';
  import { buildApi } from '/src/api/erupt/erupt';
  import { getComponent, getComponentProps } from '/src/components/EruptTable/componets';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '/src/store/modules/user';
  import { getMessageCount } from '/@/layouts/default/header/components/message/api';
  export default defineComponent({
    components: {
      BasicForm,
      BasicModal,
      Description,
      Popover,
      BellOutlined,
      Tabs,
      TabPane: Tabs.TabPane,
      [Steps.name]: Steps,
      [Steps.Step.name]: Steps.Step,
      [Card.name]: Card,
      [Collapse.name]: Collapse,
      [CollapsePanel.name]: CollapsePanel,
      Badge,
      NoticeList,
      VFormCreate,
    },
    setup() {
      const { prefixCls } = useDesign('header-notify');
      const pid = ref();
      const processInstanceId = ref('');
      const { createMessage } = useMessage();
      const lookSchema = ref([]);
      const taskSchema = ref([]);
      const [register, { openModal, closeModal }] = useModal();
      const userStore = useUserStore();
      const activeKey = ref('1');
      const acts = ref([]);
      const cName = ref('');
      const step = ref('');
      const steps = ref([]);
      const descData = ref([]);
      const router = useRouter();
      const bussinesKey = ref('');
      const formModel = ref({});
      const formJson = ref();
      const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
        labelWidth: 100,
        schemas: taskSchema,
        showActionButtonGroup: false,
      });

      // const listData = ref([]);
      const taskStore = useTaskStore();

      const listData = computed(() => taskStore.getTaskList);
      onMounted(() => {
        if (userStore.getUserInfo.username) {
          getMessageCount().then((res) => {
            count.value = res;
          });
        }
      });
      const count = ref(1);

      function onNoticeClick() {
        router.push('/dashboard/message');
      }

      return {
        onClick: () => {
          console.log(listData.value);
        },
        cancel: () => {
          resetFields();
          closeModal();
        },
        prefixCls,
        listData,
        cName,
        step,
        pid,
        count,
        onNoticeClick,
        register,
        openModal,
        closeModal,
        registerForm,
        descData,
        activeKey,
        formJson,
        onSubmit: () => {},
        fApi: () => {},
        formModel,
        acts,
        lookSchema,
        taskSchema,
        steps,
        setFieldsValue,
        numberStyle: {},
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-notify';

  .@{prefix-cls} {
    padding-top: 2px;

    &__overlay {
      max-width: 360px;
    }

    .ant-tabs-content {
      width: 300px;
    }

    .ant-badge {
      font-size: 18px;

      .ant-badge-multiple-words {
        padding: 0 4px;
      }

      svg {
        width: 0.9em;
      }
    }
  }
</style>
