import { defineStore } from 'pinia';
import {lineOptionsApi, lineOptionsContainApi} from '/@/api/demo/specialLine';
import { store } from '/@/store';

export const useSpecialStore = defineStore({
  id: 'special',
  actions: {
    async getLineOption(id) {
      const res = await lineOptionsApi(id);
      return res;
    },
    async getLineOptionContain(id) {
      const res = await lineOptionsContainApi(id);
      return res;
    },
  },
});

export function useSpecialStoreWithOut() {
  return useSpecialStore(store);
}
