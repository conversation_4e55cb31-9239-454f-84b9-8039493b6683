import { BasicColumn, FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '系统名称',
    dataIndex: 'sysClientName',
    width: 200,
  },
  {
    title: '用户名',
    dataIndex: 'sysUserUsername',
    width: 200,
  },
  {
    title: '所属关系',
    dataIndex: 'visit',
    width: 80,
    customRender: ({ record }) => {
      const visit = record.visit;
      const enable = ~~visit === 1;
      const color = enable ? 'green' : 'red';
      const text = enable ? '允许访问' : '禁止访问';
      return h(Tag, { color: color }, () => text);
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'sysClientName',
    label: '系统名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'sysUserUsername',
    label: '用户名',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    required: false,
    component: 'Input',
    show: false,
  },
  {
    field: 'sysClientName',
    label: '系统名称',
    required: true,
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'sysUserUsername',
    label: '用户账号',
    required: true,
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'visit',
    label: '是否显示',
    required: true,
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '允许访问', value: 1 },
        { label: '禁止访问', value: 2 },
      ],
    },
    colProps: { span: 24 },
  },
];
