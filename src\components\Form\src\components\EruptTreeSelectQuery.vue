<template>
  <ApiTreeSelect v-model:value="selectedRef" v-bind="getAttrs" :api="buildApi" result-field="list" @change="onChange"></ApiTreeSelect>
</template>

<script lang="ts">
import {computed, defineComponent, ref, unref,watch} from 'vue';
import ApiTreeSelect from './ApiTreeSelect.vue'

import {refTreeBuildApi} from "/@/api/erupt/erupt";
export default defineComponent({
  name: 'EruptTreeSelectQuery',
  components: { ApiTreeSelect },
  props: {
    value:{type:String},
    className: {},
    tabName: {}
  },
  emits: ['change', 'update:value'],
  setup(props, { attrs, emit }) {
    const {className, tabName} = props
    const selectedRef = ref()
    const getAttrs = computed(() => {
      return {
        ...attrs,
      };
    });
    const buildApi = () => {
      return refTreeBuildApi(className, tabName)
    }

    const onChange = (e) => {
      emit('update:value', e);
      return emit('change', e);
    }
    /*watch(
      () => props.value,
      (v) => {
        if (v && v instanceof Object) {
          selectedRef.value = v.id
        } else {
          selectedRef.value = null
        }
      },
    );*/

    return { buildApi, onChange,getAttrs,selectedRef };
  },
});
</script>

<style scoped lang="less">

</style>
