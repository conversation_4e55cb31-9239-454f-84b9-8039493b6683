import { BasicFetchResult } from '/@/api/model/baseModel';

export interface BasicItem {
  id: number;
  objid: string;
  createDateTime: string;
  createUser: number;
  creator: string;
  updateDateTime: string;
  updateUser: number;
  updator: string;
  version: number;
}

export type permissionListItem = BasicItem & {
  name: string;
  permission: string;
  children?: permissionListItem[];
};

export type rolePermissionItem = {
  roleId: number;
  permissions?: string[];
};

export type permissionListGetResultModel = BasicFetchResult<permissionListItem>;
