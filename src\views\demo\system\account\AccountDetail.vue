<template>
  <PageWrapper
    :title="`用户: ` + userRef.user.personName + `的资料`"
    contentBackground
    @back="goBack"
  >
    <template #extra>
      <a-button type="primary" danger @click="handleDisableAccount"> 禁用账号 </a-button>
      <a-button type="primary" @click="handleEnableAccount"> 启用账号 </a-button>
      <a-popconfirm title="是否确认重置密码" placement="left" @confirm="handleChangePassword">
        <a-button type="primary"> 重置密码 </a-button>
      </a-popconfirm>
    </template>
    <template #footer>
      <a-tabs default-active-key="detail" v-model:activeKey="currentKey">
        <a-tab-pane key="detail" tab="用户资料" />
        <!--        <a-tab-pane key="logs" tab="操作日志" />-->
      </a-tabs>
    </template>
    <div class="pt-4 m-4 desc-wrap">
      <template v-if="currentKey == 'detail'">
        <Description
          size="middle"
          title="用户信息"
          :bordered="false"
          :column="2"
          :data="userRef.user"
          :schema="personSchema"
        />
      </template>
      <template v-if="currentKey == 'logs'">
        <div v-for="i in 10" :key="i">这是用户{{ userId }}操作日志Tab</div>
      </template>
    </div>
  </PageWrapper>
</template>

<script>
  import { defineComponent, ref, reactive } from 'vue';
  import { useRoute } from 'vue-router';
  import { PageWrapper } from '/@/components/Page';
  import { Description } from '/@/components/Description/index';
  import { useGo } from '/@/hooks/web/usePage';
  import { useTabs } from '/@/hooks/web/useTabs';
  import { Tabs } from 'ant-design-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { resetPassword, getUserModel, enableAccount, disableAccount } from '/@/api/demo/system';
  import { personSchema } from './account.data';
  export default defineComponent({
    name: 'AccountDetail',
    components: { PageWrapper, Description, ATabs: Tabs, ATabPane: Tabs.TabPane },
    setup() {
      const { notification } = useMessage();
      const route = useRoute();
      const go = useGo();
      // 此处可以得到用户ID
      const userId = ref(route.params?.id);
      const currentKey = ref('detail');
      const userRef = reactive({
        user: {},
      });
      const { setTitle } = useTabs();
      let id = userId.value;

      getUserModel(id).then((res) => {
        userRef.user = res;
        console.log(res);
        setTitle('详情：用户' + userRef.user.username);
      });

      // 设置Tab的标题（不会影响页面标题）

      // 页面左侧点击返回链接时的操作
      function goBack() {
        // 本例的效果时点击返回始终跳转到账号列表页，实际应用时可返回上一页
        go('/system/account');
      }

      function handleChangePassword() {
        resetPassword(id).then(() => {
          notification.success({
            message: '重置密码',
            description: '重置密码成功',
          });
        });
      }

      function handleDisableAccount() {
        disableAccount(id).then(() => {
          notification.success({
            message: '禁用账号',
            description: '禁用账号成功',
          });
        });
      }

      function handleEnableAccount() {
        enableAccount(id).then(() => {
          notification.success({
            message: '启用账号',
            description: '启用账号成功',
          });
        });
      }

      return {
        userId,
        currentKey,
        userRef,
        personSchema,
        goBack,
        handleChangePassword,
        handleDisableAccount,
        handleEnableAccount,
      };
    },
  });
</script>

<style></style>
