<template>
  <BasicModal
    defaultFullscreen
    :width="1200"
    v-bind="$attrs"
    title="值列表"
    @ok="handleSubmit"
    @cancel="close"
  >
    <!--      <template #appendFooter>
      <a-button @click="clear">
        清空
      </a-button>
    </template>-->
    <vxe-basic-table ref="tableRef" v-bind="gridOptions"> </vxe-basic-table>
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, watchEffect, computed, unref, watch, h } from 'vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import {
    Table,
    Form,
    Row,
    FormItem,
    Col,
    Button,
    DatePicker,
    RadioGroup,
    Radio,
    Select,
    SelectOption,
    Tooltip,
    Tag,
    Modal,
  } from 'ant-design-vue';
  import { bool, string } from 'vue-types';

  import { buildApi, refQueryApi, tableQueryApi } from '/@/api/erupt/erupt';
  import VxeBasicTable from '/@/components/VxeTable/src/VxeBasicTable';
  import {
    getColFomatter,
    getComponent,
    getComponentProps,
    getSearchComponent,
  } from '/@/components/EruptTable/componets';
  import { useUserStore } from '/@/store/modules/user';

  const useForm = Form.useForm;

  export default defineComponent({
    name: 'PersonSelector',
    props: {
      dataFetch: { type: Promise },
      preConditions: { default: new Map([]) },
      queryContions: { type: Array, default: [] },
      value: { type: Object },
      className: { type: string },
      onOk: { type: Function },
      onCancel: { type: Function },
      multiple: { type: Boolean, default: false },
    },
    emits: ['change', 'update:value', 'ok', 'cancle'],
    components: {
      Tooltip,
      Icon,
      BasicModal,
      aTable: Table,
      aButton: Button,
      Form,
      Row,
      FormItem,
      Col,
      Button,
      DatePicker,
      RadioGroup,
      Radio,
      Tag,
      aSelect: Select,
      SelectOption,
      VxeBasicTable,
      //FormAction
    },
    setup(props, { attrs, emit }) {
      const { onOk, onCancel, multiple, queryContions, preConditions, dataFetch } = props;
      const [register, { openModal, closeModal }] = useModal();
      const tableRef = ref();
      const uploadModalRef = ref();
      const collapseStatus = ref(true);
      let dataSource = ref([]);
      let columns = [
        {
          type: multiple ? 'checkbox' : 'radio',
          width: '40px',
        },
        {
          key: 'no',
          field: 'no',
          showOverflow: 'title',
          title: '工号',
          sortable: true,
          width: '',
        },
        {
          key: 'name',
          field: 'name',
          showOverflow: 'title',
          title: '姓名',
          sortable: false,
          width: '',
        },
        {
          key: 'lines',
          field: 'lines',
          showOverflow: 'title',
          title: '线路',
          sortable: false,
          width: '',
        },
        {
          key: 'deptId_name',
          field: 'deptId_name',
          showOverflow: 'title',
          title: '单位',
          sortable: false,
          width: '',
        },
      ];
      let querys = ref([]);
      const pagination = ref({
        // 分页配置
        pageSize: 10, // 每页显示的条数
        showSizeChanger: true, // 是否可以改变每页显示的条数
        pageSizeOptions: ['10', '20', '30', '40'], // 可选的每页显示条数
        showQuickJumper: true, // 是否可以快速跳转到指定页
        showTotal: (total) => `共 ${total} 条`, // 显示总条数和当前数据范围
        current: 1,
        onChange: (page, pageSize) => {
          pagination.value.current = page;
          pagination.value.pageSize = pageSize;
        }, // 页码改变时的回调函数
      });
      const selectedRowKeys = ref(null);
      const rowkey = ref('');
      const selectedRows = ref({});
      const formState = ref({});
      const label = ref('');

      const gridOptions = ref({
        columns: columns,
        rowkey: 'id',
        rowId: 'id',
        rowConfig: {
          isTree: false,
          children: 'children',
        },
        pagerConfig: {
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30', '40'],
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
        },
        formConfig: {
          enabled: true,
          items: queryContions.length > 0 ? queryContions : querys,
          titleAlign: 'right',
          titleWidth: '20%',
          titleColon: true,
        },
        proxyConfig: {
          ajax: {
            query: async ({ page, form }) => {
              return search(page, form);
            },
          },
        },
        toolbarConfig: {
          enabled: false,
        },
      });

      buildApi('Person').then((res) => {
        const {
          eruptModel: { eruptFieldModels, eruptJson },
        } = res;
        let qs = [];
        eruptFieldModels.forEach((item) => {
          const key = item.fieldName;
          const title = item.eruptFieldJson.edit.title;
          //  formState[key] = null
          if (item.eruptFieldJson.edit.search.value) {
            const qComponent = getSearchComponent(key, title, item, 6, 'Person', undefined);
            qs.push(qComponent);
          }
        });
        for (let i = 0; i < qs.length; i++) {
          if (i >= 4) {
            qs[i].folding = true;
          }
        }
        qs.push({
          span: 4,
          align: 'right',
          className: '!pr-0',
          itemRender: {
            name: 'AButtonGroup',
            children: [
              {
                props: { type: 'primary', content: '查询', htmlType: 'submit' },
                attrs: { class: 'mr-2' },
              },
              { props: { type: 'default', htmlType: 'reset', content: '重置' } },
            ],
          },
        });

        querys.value = qs;
        console.log(qs);
        //gridOptions.columns = columns
        //search()
      });

      watch(
        () => rowkey.value,
        (v) => {
          debugger;
          emit('update:value', selectedRows.value);
          return emit('change', selectedRows.value);
        },
      );
      watch(
        () => props.value,
        (v) => {
          if (v) {
            label.value = v.name;
          } else {
            label.value = '';
            selectedRows.value = null;
            rowkey.value = '';
            if (tableRef.value) tableRef.value.clearRadioRow();
          }
        },
      );

      async function search(page, form) {
        console.log(page, form);
        let condition = [];
        preConditions.entries().forEach((item) => {
          if (form[item[0]] == undefined) {
            form[item[0]] = item[1];
          }
        });
        const conditionObjs = JSON.parse(JSON.stringify(form));
        for (let key in conditionObjs) {
          if (conditionObjs[key]) {
            condition.push({ key, value: conditionObjs[key] });
          }
        }
        const query = {
          pageIndex: page.currentPage,
          pageSize: page.pageSize,
          condition,
        };
        console.log(tableRef);
        const data = await tableQueryApi('Person', query);
        return data;
      }

      /*function search() {
      console.log(pagination)
      let condition = []
      for (let key in formState.value) {
        if (formState.value[key]) {
          condition.push({key,value:formState.value[key]})
        }
      }
      const query = {
        pageIndex: pagination.value.current,
        pageSize: pagination.value.pageSize,
        condition
      }
    refQueryApi(className,refClassName,query).then(res => {
        dataSource.value = res.records
      })
  }*/
      function handleSubmit() {
        if (!multiple) {
          const selected = tableRef.value.getRadioRecord();
          rowkey.value = selected.key;
          selectedRows.value = selected;
          //closeModal()
          emit('update:value', selectedRows.value);
          emit('ok', selected.no);
        } else {
          const selected = tableRef.value.getCheckboxRecords();
          rowkey.value = selected.map((item) => item.key);
          selectedRows.value = selected;
          const nos = selected.map((item) => item.no);
          emit('update:value', selectedRows.value);
          emit('ok', nos);
        }
      }

      return {
        register,
        clear: (v) => {
          if (!v.target.innerHTML) {
            debugger;
            tableRef.value.clearRadioRow();
            rowkey.value = '';
            selectedRows.value = null;
            label.value = '';
            emit('update:value', null);
            emit('change', null);
          }
        },
        dataSource,
        pagination,
        columns,
        querys,
        label,
        handleSubmit,
        formState,
        search,
        selectedRowKeys,
        uploadModalRef,
        collapseStatus,
        tableRef,
        gridOptions,
        close: () => {
          if (!label.value) {
            rowkey.value = '';
            selectedRows.value = '';
            tableRef.value.clearRadioRow();
          }
          //resetFields()
          closeModal();
        },
      };
    },
  });
</script>
