import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export type BannerItemParams = {
  name?: string;
  tip?: string;
};

export type ExternalLinkParams = {
  name?: string;
  url?: string;
};

export type NewsParams = {
  title?: string;
  newsNumber?: string;
};

export type NewsCategoryParams = {
  name?: string;
  system?: number;
};

export type FileCategoryParams = {
  name?: string;
  code?: string;
};

export type FileAttachmentParams = {
  attachmentName?: string;
  fileUnit?: string;
};

export type BannerItemPageParams = BasicPageParams & BannerItemParams;

export type ExternalLinkPageParams = BasicPageParams & ExternalLinkParams;

export type NewsPageParams = BasicPageParams & NewsParams;

export type NewsCategoryPageParams = BasicPageParams & NewsCategoryParams;

export type FileCategoryPageParams = BasicPageParams & FileCategoryParams;

export type FileAttachmentPageParams = BasicPageParams & FileCategoryParams;

export interface BannerItemListItem {
  id: number;
  pos: string;
  name: string;
  imgUrl: string;
  url: string;
  tip: string;
  note: string;
}

export interface BannerItemInfo {
  id: number;
  pos: string;
  name: string;
  imgUrl: string;
  url: string;
  tip: string;
  note: string;
}

export interface ExternalLinkListItem {
  id: number;
  pos: string;
  name: string;
  url: string;
  icon: string;
  note: string;
}

export interface ExternalLinkInfo {
  id: number;
  pos: string;
  name: string;
  url: string;
  icon: string;
  note: string;
}

export interface NewsListItem {
  id: number;
  title: string;
  categoryId: number;
  newsNumber: string;
  newsUnit: string;
  standardNumber: string;
  downloadNum: string;
  valid: string;
}

export interface NewsInfo {
  id: number;
  username: string;
  title: string;
  categoryId: number;
  content: string;
  provider: string;
  newsNumber: string;
  newsUnit: string;
  standardNumber: string;
  downloadNum: string;
  valid: string;
}

export interface NewsCategoryListItem {
  id: number;
  name: string;
  description: string;
  imgUrl: string;
  deptId: number;
  system: number;
}

export interface FileCategoryListItem {
  id: number;
  name: string;
  code: string;
  note: string;
}

export interface NewsCategoryInfo {
  id: number;
  name: string;
  description: string;
  imgUrl: string;
  deptId: number;
  system: number;
}

export interface FileCategoryInfo {
  id: number;
  name: string;
  code: string;
  note: string;
}

export interface FileAttachmentListItem {
  id: number;
  categoryName: string;
  categoryId: number;
  fileType: string;
  fileName: string;
  attachmentName: string;
  extName: string;
  filePath: string;
  pdfPath: string;
  fileDate: string;
  fileUnit: string;
  note: string;
}

export interface FileAttachmentInfo {
  id: number;
  categoryName: string;
  categoryId: number;
  fileType: string;
  fileName: string;
  attachmentName: string;
  extName: string;
  filePath: string;
  pdfPath: string;
  fileDate: string;
  fileUnit: string;
  note: string;
}

export type BannerItemListGetResultModel = BasicFetchResult<BannerItemListItem>;

export type ExternalLinkListGetResultModel = BasicFetchResult<ExternalLinkListItem>;

export type NewsListGetResultModel = BasicFetchResult<NewsListItem>;

export type NewsCategoryListGetResultModel = BasicFetchResult<NewsCategoryListItem>;

export type FileCategoryListGetResultModel = BasicFetchResult<FileCategoryListItem>;

export type FileAttachmentListGetResultModel = BasicFetchResult<FileAttachmentListItem>;
