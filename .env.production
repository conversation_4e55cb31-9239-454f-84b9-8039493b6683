# Whether to open mock
VITE_USE_MOCK = false

# public path
VITE_PUBLIC_PATH = /
VITE_REPORT_BASE=http://**************:9000
# Delete console
VITE_DROP_CONSOLE = true

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS = 'none'

# Whether to delete origin files when using compress, default false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# Basic interface address SPA
VITE_GLOB_API_URL=http://**************/serviceapi/basic-api

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VITE_GLOB_ERUPT_URL=http://**************/serviceapi/erupt-api

VITE_GLOB_ERUPT_ATTACHMENT=http://**************/serviceapi/erupt-attachment

# File upload address， optional
VITE_GLOB_UPLOAD_URL=http://**************/serviceapi/basic-api/upload

# Interface prefix
VITE_GLOB_API_URL_PREFIX=

# Whether to enable image compression
VITE_USE_IMAGEMIN=true

# use pwa
VITE_USE_PWA=false

# Is it compatible with older browsers
VITE_LEGACY=false
VITE_GLOB_FILE_PREVIEW_URL=http://**************:8012

VITE_GLOB_FULL_SCREEN_URL=http://**************:81/

