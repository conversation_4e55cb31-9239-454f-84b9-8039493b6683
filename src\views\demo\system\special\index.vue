<template>
  <div>
    <erupt-table
      title="专题管理"
      ref="eruptTable"
      class-name="Special"
      :seq="true"
      :is-process="true"
      :overwrite-action="overwriteAction"
      :row-dynamic-controller="rowDynamicController"
      :pre-conditions="preCondition"
      :query-sort="querySort"
    >
      <template #specialType="{ row }">
        <div v-if="row.specialType == '设计联络'" style="align-self: center">
          <a-tag color="green" style="width: 80px; text-align: center"> 设计联络 </a-tag>
        </div>
        <div v-if="row.specialType == '接口施工'" style="align-self: center">
          <a-tag color="blue" style="width: 80px; text-align: center"> 接口施工 </a-tag>
        </div>
        <div v-if="row.specialType == '咨询建议'" style="align-self: center">
          <a-tag color="orange" style="width: 80px; text-align: center"> 咨询建议 </a-tag>
        </div>
        <div v-if="row.specialType == '运营标准'" style="align-self: center">
          <a-tag color="purple" style="width: 80px; text-align: center"> 运营标准 </a-tag>
        </div>
      </template>

      <template #approveStatus="{ row }">
        <div v-if="row.approveStatus == '待提报'" style="align-self: center">
          <a-tag color="green" style="width: 70px; text-align: center">
            {{ row.approveStatus }}
          </a-tag>
        </div>
        <div v-if="row.approveStatus == '反馈中'" style="align-self: center">
          <a-tag color="blue" style="width: 70px; text-align: center">
            {{ row.approveStatus }}
          </a-tag>
        </div>
        <div v-if="row.approveStatus == '已闭环'" style="align-self: center">
          <a-tag color="red" style="width: 70px; text-align: center">
            {{ row.approveStatus }}
          </a-tag>
        </div>
      </template>
    </erupt-table>

    <BasicModal
      @register="registerModal"
      title="预览文件"
      :width="1000"
      :height="600"
      :footer="null"
    >
      <iframe :key="key" :src="modalUrl" width="1000" height="800"></iframe>
    </BasicModal>

    <BasicModal
      defaultFullscreen
      @register="register"
      :showFooter="true"
      :title="type"
      width="50%"
      @ok="handleSubmit"
      okText="保存"
    >
      <BasicForm @register="registerForm"></BasicForm>
      <template #appendFooter>
        <a-popconfirm title="是否确认提报" @confirm="onConfirm">
          <a-button> 提报</a-button>
        </a-popconfirm>
      </template>
    </BasicModal>

    <BasicModal
      defaultFullscreen
      v-bind="$attrs"
      @register="register2"
      :footer="null"
      width="80%"
      title="详情"
    >
      <div style="display: flex; flex-direction: row">
        <div style="width: 78%">
          <a-card
            :head-style="headerStyle"
            :body-style="borderStyle"
            :bordered="true"
            title="专题信息"
          >
            <a-descriptions
              layout="vertical"
              :column="4"
              bordered
              :label-style="{ fontWeight: 'bold' }"
            >
              <a-descriptions-item label="专题标题" :contentStyle="contentStyle"
                >{{ detailData['title'] }}
              </a-descriptions-item>
              <a-descriptions-item label="专题类别" :contentStyle="contentStyle">
                <div v-if="detailData['specialType'] == '设计联络'">
                  <a-tag color="green">设计联络</a-tag>
                </div>
                <div v-if="detailData['specialType'] == '接口施工'">
                  <a-tag color="blue">接口施工</a-tag>
                </div>
                <div v-if="detailData['specialType'] == '咨询建议'">
                  <a-tag color="orange">咨询建议</a-tag>
                </div>
                <div v-if="detailData['specialType'] == '运营标准'">
                  <a-tag color="purple">运营标准</a-tag>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="所属线路" :contentStyle="contentStyle"
                >{{ detailData['lineIds'] }}
              </a-descriptions-item>
              <!--              <a-descriptions-item label="所属区域" :contentStyle="contentStyle"
              >{{ detailData['bidSection'] }}
              </a-descriptions-item>-->

              <a-descriptions-item label="所属专业" :contentStyle="contentStyle"
                >{{ detailData['typeId'] }}
              </a-descriptions-item>
              <a-descriptions-item label="当前状态" :contentStyle="contentStyle">
                <div v-if="detailData['approveStatus'] == '待提报'">
                  <a-tag color="green">{{ detailData['approveStatus'] }}</a-tag>
                </div>
                <div v-if="detailData['approveStatus'] == '反馈中'">
                  <a-tag color="blue">{{ detailData['approveStatus'] }}</a-tag>
                </div>
                <div v-if="detailData['approveStatus'] == '已闭环'">
                  <a-tag color="red">{{ detailData['approveStatus'] }}</a-tag>
                </div></a-descriptions-item
              >
              <a-descriptions-item label="截止日期" :contentStyle="contentStyle"
                >{{ detailData['createTime'] }}
              </a-descriptions-item>
              <a-descriptions-item label="专题内容">{{ detailData['des'] }}</a-descriptions-item>
              <a-descriptions-item label="部门">{{ detailData['mainDeptId'] }}</a-descriptions-item>
            </a-descriptions>
          </a-card>

          <a-card :head-style="headerStyle" :body-style="borderStyle" :bordered="true" title="附件">
            <a-descriptions
              layout="vertical"
              :column="2"
              bordered
              :label-style="{ fontWeight: 'bold' }"
            >
              <a-descriptions-item label="附件">
                <div
                  v-if="detailData['enclosure']"
                  v-for="(item, index) in detailData['enclosure'].split('|')"
                >
                  <div
                    ><a @click="previewShow(item, index)">{{
                      item.substring(item.lastIndexOf('/') + 1)
                    }}</a>
                    <a
                      style="margin-left: 10px; color: #ff6118"
                      :href="eruptAttachment + item"
                      target="_blank"
                      >下载</a
                    ></div
                  >
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
          <a-card
            :head-style="headerStyle"
            :body-style="borderStyle"
            :bordered="true"
            title="反馈意见"
          >
            <a-table
              :dataSource="feedbackList"
              :pagination="false"
              bordered
              size="small"
              :rowKey="(record, index) => index"
            >
              <a-table-column title="部门" data-index="deptName" align="center" width="15%" />
              <a-table-column title="处理人" data-index="userName" align="center" width="15%" />
              <a-table-column title="处理状态" data-index="opinion" align="center" width="15%">
                <template #default="{ record }">
                  <a-tag :color="getStatusColor(record.opinion)">
                    {{ record.opinion }}
                  </a-tag>
                </template>
              </a-table-column>
              <a-table-column title="处理意见" data-index="message" align="center" />
              <a-table-column title="附件" align="center" width="25%">
                <template #default="{ record }">
                  <div v-if="record.enclosure && record.enclosure.trim()" style="text-align: left">
                    <div v-for="(file, fileIndex) in record.enclosure.split('|')" :key="fileIndex">
                      <div v-if="file.trim()" style="margin: 3px 0">
                        <a @click="previewShow(file.trim(), fileIndex)">
                          {{ file.trim().substring(file.trim().lastIndexOf('/') + 1) }}
                        </a>
                        <a
                          style="margin-left: 10px; color: #ff6118"
                          :href="eruptAttachment + file.trim()"
                          target="_blank"
                        >
                          下载
                        </a>
                      </div>
                    </div>
                  </div>
                  <span v-else>-</span>
                </template>
              </a-table-column>
            </a-table>
          </a-card>

          <!-- 闭环意见表格（仅闭环数据） -->
          <a-card
            v-if="hasClosedFeedback"
            :head-style="headerStyle"
            :body-style="borderStyle"
            :bordered="true"
            title="闭环意见"
            style="margin-top: 16px"
          >
            <a-table
              :dataSource="closedFeedbackList"
              :pagination="false"
              bordered
              size="small"
              :rowKey="(record, index) => index"
            >
              <a-table-column title="部门" data-index="deptName" align="center" width="15%" />
              <a-table-column title="处理人" data-index="userName" align="center" width="15%" />
              <a-table-column title="处理状态" data-index="opinion" align="center" width="15%">
                <template #default="{ record }">
                  <a-tag color="red">闭环</a-tag>
                </template>
              </a-table-column>
              <a-table-column title="处理意见" data-index="message" align="center" />
              <a-table-column title="附件" align="center" width="25%">
                <template #default="{ record }">
                  <div v-if="record.enclosure && record.enclosure.trim()" style="text-align: left">
                    <div v-for="(file, fileIndex) in record.enclosure.split('|')" :key="fileIndex">
                      <div v-if="file.trim()" style="margin: 3px 0">
                        <a @click="previewShow(file.trim(), fileIndex)">
                          {{ file.trim().substring(file.trim().lastIndexOf('/') + 1) }}
                        </a>
                        <a
                          style="margin-left: 10px; color: #ff6118"
                          :href="eruptAttachment + file.trim()"
                          target="_blank"
                        >
                          下载
                        </a>
                      </div>
                    </div>
                  </div>
                  <span v-else>-</span>
                </template>
              </a-table-column>
            </a-table>
          </a-card>
        </div>
        <div style="width: 20%; height: 100%">
          <a-card :bordered="false" title="流程信息">
            <a-steps direction="vertical" :current="currentStep">
              <a-step
                style="margin: 3%"
                v-for="(item, index) in approveRecords"
                :key="index"
                :status="!item.action ? 'process' : 'finish'"
                :title="item.name"
                :style="!item.action ? { color: 'green' } : {}"
              >
                <template #description>
                  <div :style="{ color: !item.action ? 'rgba(0, 0, 0, 0.45)' : 'black' }">
                    <div> {{ !item.action ? '待办理人' : '办理人' }}：{{ item.person }}</div>
                    <div v-show="item.action">处理方式：{{ item.action }}</div>
                    <div v-show="item.opinion">审批意见：{{ item.opinion }}</div>
                    <p v-show="item.createDateTime">办理时间：{{ item.createDateTime }}</p>
                  </div>
                </template>
              </a-step>
            </a-steps>
          </a-card>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="tsx">
  import eruptTable from '/@/components/EruptTable/eruptTable';
  import { defineComponent, ref, h, onMounted, computed } from 'vue';
  import { NColorPicker } from 'naive-ui';
  import { Card, Table, Tag } from 'ant-design-vue';
  import { buildApi, getOne, save, update } from '/@/api/erupt/erupt';
  import { useMessage } from '/@/hooks/web/useMessage';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useForm } from '/@/components/Form';
  import {
    getComponent,
    getComponentProps,
    getSearchComponent,
  } from '/@/components/EruptTable/componets';

  import { useRoute } from 'vue-router';
  import { getApprovalRecordsByBussiness } from '/@/api/process/process';
  import { useUserStore } from '/@/store/modules/user';
  import Template from '/@/views/demo/system/quality/template.vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Base64 } from 'js-base64';
  import { attachments, detail1, detail2, getNextNode } from '/@/views/task/special/api/special';

  export default defineComponent({
    name: 'Special',
    components: {
      Template,
      BasicModal,
      BasicForm,
      eruptTable,
      NColorPicker,
      [Card.name]: Card,
      [Table.name]: Table,
      [Tag.name]: Tag,
    },
    setup: function () {
      const [register, { openModal, closeModal }] = useModal();
      const [registerModal, { openModal: openModalPreview }] = useModal();
      //详情modal
      const activeKey = ref('1');
      const detailData = ref({});
      const lineOptions = ref([]);
      const typeOptions = ref([]);
      const currentStep = ref<number>(0);
      const typeIdOptions = ref([]);
      const approveRecords = ref([]);
      const modalUrl = ref('');
      const key = ref(0);
      const subAttachments = ref([]);
      const globSetting = useGlobSetting();
      const eruptAttachment = globSetting?.eruptAttachment;
      const [register2, { openModal: openModal2, closeModal: closeModal2 }] = useModal();
      const eruptFieldModels = ref([]);
      const editSchemas = ref([]);
      const extraTitle = ref('');
      const base = ref({ id: '', version: '' });
      const preCondition = new Map([]);
      const route = useRoute();
      const line = computed(() => route.query.line);
      const dept = computed(() => route.query.dept);
      const state = computed(() => route.query.state);
      const statusColorMap = {
        待提报: 'orange',
        反馈中: 'blue',
        已闭环: 'green',
      };

      // 计算属性
      const feedbackList = computed(() =>
        subAttachments.value.filter((item) => item.opinion !== '闭环'),
      );

      const closedFeedbackList = computed(() =>
        subAttachments.value.filter((item) => item.opinion === '闭环'),
      );

      const hasClosedFeedback = computed(() => closedFeedbackList.value.length > 0);

      // 状态标签颜色
      const getStatusColor = (status) => statusColorMap[status] || 'gray';
      const [
        registerForm,
        { resetFields, setFieldsValue, updateSchema, validate, getFieldsValue },
      ] = useForm({
        labelWidth: 100,
        wrapperCol: { span: 24 },
        schemas: editSchemas,
        showActionButtonGroup: false,
      });

      const eruptTable = ref();
      const type = ref('新增');
      const dynamicController = {
        lineIds: (form, e) => {
          const { formModel, formActionType } = form;
          if (e) {
            console.log(e, formModel);
          }
        },
      };

      const rowDynamicController = {
        edit: (row) => {
          if (row.approveStatus == '待提报') {
            return true;
          } else {
            return false;
          }
        },
        delete: (row) => {
          if (row.approveStatus == '待提报') {
            return true;
          } else {
            return false;
          }
        },
      };

      const overwriteAction = {
        add: () => {
          type.value = '新增';
          setTimeout(() => {
            resetFields();
            openModal();
          }, 200);
        },

        edit: async (row) => {
          type.value = '编辑';
          const res = await getOne('Special', row.id);
          base.value = res;
          setTimeout(() => {
            resetFields();
            openModal();
            setTimeout(() => {
              setFieldsValue(res);
            }, 20);
          }, 200);
        },

        detail: async (row) => {
          const res = await getOne('Special', row.id); // 获取详情数据
          const {
            eruptModel: { eruptFieldModels: fieldModels },
          } = await buildApi('Special');
          eruptFieldModels.value = fieldModels;
          const data = await attachments(row.id);

          subAttachments.value = data;
          // 提取线路和专业的选择项
          const typeIdField = fieldModels.find((item) => item.fieldName === 'typeId');
          typeIdOptions.value = typeIdField?.componentValue || [];
          const departments = await detail1(row.id);
          const lineField = await detail2(row.id);
          res.mainDeptId = departments.departments;
          res.lineIds = lineField.lineIds;
          detailData.value = res;

          getApprovalRecordsByBussiness('Special' + '.' + row.id).then((res) => {
            getNextNode(row.id).then((r) => {
              if (r) {
                res.push({ person: r.users, name: r.task });
                approveRecords.value = res;
                currentStep.value = approveRecords.value.length - 2;
              } else {
                approveRecords.value = res;
              }
            });
          });
          setTimeout(() => {
            openModal2();
          }, 100);
        },
      };

      const formatLineName = (lineIds) => {
        const matchedLine = lineOptions.value.find((line) => line.value === lineIds);
        return matchedLine ? matchedLine.label : '-';
      };

      function previewShow(downloadUrl, index) {
        downloadUrl = encodeURIComponent(Base64.encode(globSetting.eruptAttachment + downloadUrl));
        const userStore = useUserStore();
        const watermarkTxt = userStore.userInfo.username + ' ' + userStore.userInfo.realName;
        modalUrl.value = ''; // 清空旧缓存
        modalUrl.value =
          globSetting.previewUrl +
          '/onlinePreview?url=' +
          downloadUrl +
          '&watermarkTxt=' +
          watermarkTxt; // 设置预览 URL
        key.value = index; // 重新加载 iframe
        openModalPreview(true); // 打开 modal 窗口
      }

      onMounted(() => {
        const userStore = useUserStore();
        if (state.value != null && state.value != '' && state.value != '总数') {
          preCondition.set('approveStatus', state.value);
        }
        if (line.value != null && line.value != '') {
          preCondition.set('lineIds', line.value);
        }
        if (dept.value != null && dept.value != '' && dept.value != '新线管理部') {
          preCondition.set('mainDeptNames', dept.value);
        } else if (userStore.userInfo.dept != '新线管理部') {
          preCondition.set('mainDeptNames', userStore.userInfo.dept);
        }

        buildApi('Special').then((res) => {
          const {
            eruptModel: { eruptFieldModels, eruptJson },
            tabErupts,
            power,
          } = res;
          let qs = [];
          let edits = [];
          let adds = [];
          eruptFieldModels.forEach((item) => {
            const key = item.fieldName;
            const title = item.eruptFieldJson.edit.title;

            if (item.eruptFieldJson.edit.search.value) {
              qs.push(getSearchComponent(key, title, item, 4, 'Special'));
            }
            if (item.eruptFieldJson.edit.show.edit_show && key !== 'id') {
              if (
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
                item.eruptFieldJson.edit.type !== 'TAB_TREE'
              ) {
                const e = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, 'Special'),
                      disabled: item.eruptFieldJson.edit.readOnly.edit,
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };
                const a = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, 'Special'),
                      disabled: item.eruptFieldJson.edit.readOnly.add,
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };
                edits.push(e);
                adds.push(a);
              }
            }
          });

          qs.push({
            span: 4,
            align: 'right',
            Question: '!pr-0',
            itemRender: {
              name: 'AButtonGroup',
              children: [
                {
                  props: { type: 'primary', content: '查询', htmlType: 'submit' },
                  attrs: { class: 'mr-2' },
                },
                { props: { type: 'default', htmlType: 'reset', content: '重置' } },
              ],
            },
          });

          editSchemas.value = edits;
        });
      });

      const { createMessage } = useMessage();

      return {
        onConfirm: async () => {
          const v = await validate();
          const value = { ...base.value, ...v };
          if (!value.version) delete value.version;
          if (!value.id) delete value.id;
          value.approveFlag = 1;
          if (value.id) {
            const { status, message } = await update('Special', value, null);
            if (status == 'SUCCESS') {
              createMessage.success('提报成功');
              closeModal();
              eruptTable.value.refresh();
            } else {
              createMessage.error('提报失败:' + message);
            }
          } else {
            const { status, message } = await save('Special', value, null);
            if (status == 'SUCCESS') {
              createMessage.success('提报成功');
              closeModal();
              eruptTable.value.refresh();
            } else {
              createMessage.error('提报失败:' + message);
            }
          }
        },
        dynamicController,
        overwriteAction,
        rowDynamicController,
        eruptTable,
        extraTitle,
        type,
        base,
        eruptAttachment,
        statusColorMap,
        feedbackList,
        hasClosedFeedback,
        closedFeedbackList,
        getStatusColor,
        register,
        openModal,
        closeModal,
        activeKey,
        openModal2,
        closeModal2,
        register2,
        eruptFieldModels,
        openModalPreview,
        detailData,
        approveRecords,
        formatLineName,
        subAttachments,
        previewShow,
        registerModal,
        registerForm,
        resetFields,
        validate,
        setFieldsValue,
        currentStep,
        modalUrl,
        key,
        handleSubmit: async () => {
          const value = await validate();
          if (type.value == '新增') {
            const { status, message } = await save('Special', value, null);
            if (status == 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              closeModal();
              eruptTable.value.refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          }
          if (type.value == '编辑') {
            const { status, message } = await update('Special', { ...base.value, ...value }, null);
            if (status == 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              closeModal();
              eruptTable.value.refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          }
        },
        preCondition,
        querySort: (querys) => {
          // 线路、提报部门、专题类别、所属专业
          let map = new Map([]);
          map.set('所属线路', 4);
          map.set('提报部门', 3);
          map.set('专题类别', 2);
          map.set('所属专业', 1);
          querys.sort(function (a, b) {
            const va = map.get(a.title) ? map.get(a.title) : 0;
            const vb = map.get(b.title) ? map.get(b.title) : 0;
            return vb - va;
          });
          return querys;
        },
        borderStyle: {
          border: '1px solid #e8e8e8',
        },
        headerStyle: {
          backgroundColor: '#fafafa',
          color: '#535151',
          fontWeight: 'bold',
          fontSize: '12',
          textAlign: 'left',
        },
        contentStyle: {
          width: '130px',
          wordBreak: 'break-all',
          whiteSpace: 'normal',
          display: 'inline-block',
        },
      };
    },
  });
</script>

<style>
  .ant-descriptions-item-content {
    padding: 12px 24px;
  }

  .sub-attachment-item {
    margin-bottom: 8px;
    padding: 8px;
    background-color: #fafafa;
    border-radius: 4px;
  }

  .ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: #f0f0f0;
  }

  .ant-card-body {
    padding: 0px;
  }
</style>
