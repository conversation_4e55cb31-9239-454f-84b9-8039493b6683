<template>
  <div class="containers">
    <div class="canvas" ref="canvas" style="height: 100vh"></div>
  </div>
</template>

<script>
  // 引入自定义 Viewer
  import { CustomViewer } from '/@/utils/custom-bpmn';

  export default {
    name: 'BpmnViewer',
    props: { xmlText: {} },
    data() {
      return {
        bpmnViewer: null,
        container: null,
        canvas: null,
      };
    },
    watch: {
      //监听文件url改变时重新赋值
      xmlText(newVal, oldVal) {
        console.log('xml', newVal);
        this.createNewDiagram(newVal);
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        const canvas = this.$refs.canvas;
        this.bpmnViewer = new CustomViewer({
          container: canvas,
        });
        //this.createNewDiagram();
      },
      async createNewDiagram(xml) {
        try {
          // 本地 .bpmn 文件地址
          const result = await this.bpmnViewer.importXML(xml);
          const { warnings } = result;
          console.log(warnings);
          this.addMarker();
        } catch (err) {
          console.log(err.message, err.warnings);
        }
      },
    },
  };
</script>
