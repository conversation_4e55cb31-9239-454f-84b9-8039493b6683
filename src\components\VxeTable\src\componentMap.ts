import type { Component } from 'vue';

import type { ComponentType } from './componentType';
import { ApiSelect, ApiTreeSelect } from '/@/components/Form';
import {
  Input,
  Textarea,
  Select,
  Radio,
  Checkbox,
  AutoComplete,
  Cascader,
  DatePicker,
  InputNumber,
  Switch,
  TimePicker,
  TreeSelect,
  Rate,
  Empty
} from 'ant-design-vue';
import { Button } from '/@/components/Button';
import EntitySelector from "/@/components/Form/src/components/EntitySelector.vue";
import TagSelect from "/@/components/Form/src/components/TagSelect.vue";
import ColorPicker from "/@/components/Form/src/components/ColorPicker.vue";
import EruptTreeSelect from "/@/components/Form/src/components/EruptTreeSelect.vue";
const componentMap = new Map<ComponentType, Component>();

componentMap.set('AButton', Button);

componentMap.set('AInput', Input);
componentMap.set('ATextArea', Textarea);
componentMap.set('AInputSearch', Input.Search);
componentMap.set('AInputNumber', InputNumber);
componentMap.set('AAutoComplete', AutoComplete);
componentMap.set('AEntitySelector',EntitySelector)
componentMap.set('ASelect', Select);
componentMap.set('ATreeSelect', TreeSelect);
componentMap.set('ASwitch', Switch);
componentMap.set('ARadioGroup', Radio.Group);
componentMap.set('ACheckboxGroup', Checkbox.Group);
componentMap.set('ACascader', Cascader);
componentMap.set('ARate', Rate);

componentMap.set('ADatePicker', DatePicker);
componentMap.set('AMonthPicker', DatePicker.MonthPicker);
componentMap.set('ARangePicker', DatePicker.RangePicker);
componentMap.set('AWeekPicker', DatePicker.WeekPicker);
componentMap.set('AYearPicker', DatePicker.YearPicker);
componentMap.set('ATimePicker', TimePicker);

componentMap.set('AApiSelect', ApiSelect);
componentMap.set('AApiTreeSelect', ApiTreeSelect);
componentMap.set('ATagSelect', TagSelect);
componentMap.set('AEruptTreeSelect', EruptTreeSelect);
componentMap.set('AColorPicker', ColorPicker);
componentMap.set('AEmpty', Empty);
componentMap.set('AEmpty', Empty);
export function add(compName: ComponentType, component: Component) {
  componentMap.set(compName, component);
}

export function del(compName: ComponentType) {
  componentMap.delete(compName);
}

export { componentMap };
