import { useUserStore } from '/@/store/modules/user';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  taskList = '/newline/special/process/taskList/',
  GetNextNode = '/newline/special/getNextNode',
  Detail = '/newline/special/detail1/',
  Detail2 = '/newline/special/detail2/',
  Attachments = '/newline/special/attachments/',
  RECTIFY = '/newline/special/rectify/', // 新增整改接口
}

// 保存专题整改信息
export const saveSpecialRectify = (specialId: string, data: any) => {
  return defHttp.post({
    url: Api.RECTIFY + specialId,
    data: {
      ...data,
      userId: data.userId, // 包含用户ID
    },
  });
};
export const attachments = async (id) => {
  return await defHttp.get({ url: Api.Attachments + id });
};
export const detail1 = async (id) => {
  return await defHttp.get({ url: Api.Detail + id });
};

export const detail2 = async (id) => {
  return await defHttp.get({ url: Api.Detail2 + id });
};
export const getTasksList = () => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.taskList + userName });
};

export const getNextNode = async (id) => {
  return await defHttp.post({ url: Api.GetNextNode + `/${id}` });
};
