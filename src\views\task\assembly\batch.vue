<template>
  <div style="height: 900px">
    <VxeBasicTable ref="tableRef" v-bind="gridOptions">
      <template #action="{ row }">
        <TableAction outside :actions="createActions(row)" />
      </template>
    </VxeBasicTable>
    <PersonSelect
      @register="register1"
      :showFooter="true"
      title="请选择转办人"
      @ok="handleSubmit"
      @close="handleCancel"
      v-model:value="personNo"
      :query-contions="queryContions"
      :pre-conditions="preCondition"
    >
    </PersonSelect>
    <!--    <BasicModal

    >
      <EntitySelector
        class-name="Student"
        label-field="name"
        value-field="no"
        ref-class-name="person"
        v-model:value="personNo"
      ></EntitySelector>
      &lt;!&ndash;            <BasicForm @register="registerForm1">
                  </BasicForm>&ndash;&gt;
    </BasicModal>-->
    <BasicModal @register="register" :showFooter="true" defaultFullscreen title="请办理">
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="详细信息">
          <Description
            class="mt-4"
            layout="vertical"
            :column="4"
            :data="descData"
            :schema="lookSchema"
          />
        </a-collapse-panel>
        <a-collapse-panel key="2" header="审批历史">
          <a-steps status="finish" direction="vertical" size="small" v-for="step in steps">
            <a-step :title="step.name" :status="!step.action ? 'process' : 'finish'">
              <template #description>
                <div>办理人：{{ step.person }}</div>
                <div>处理方式：{{ step.action }}</div>
                <div>审批意见：{{ step.opinion }}</div>
                <p>办理时间：{{ step.createDateTime }}</p>
              </template>
            </a-step>
          </a-steps>
        </a-collapse-panel>
      </a-collapse>
      <a-card style="width: 100%; margin-top: 1%" title="审批处理">
        <BasicForm style="height: 100%" @register="registerForm"></BasicForm>
      </a-card>

      <template #footer>
        <VxeButton type="primary" @click="approve">确定</VxeButton>
        <VxeButton type="primary" @click="cancel">取消</VxeButton>
      </template>
    </BasicModal>

    <BasicModal @register="register2" :showFooter="true" defaultFullscreen title="请处理">
      <a-card style="width: 100%; margin-top: 1%" title="审批处理">
        <BasicForm style="height: 100%" @register="registerForm2"></BasicForm>
      </a-card>

      <template #footer>
        <VxeButton type="primary" @click="approve1">确定</VxeButton>
        <VxeButton type="primary" @click="cancel1">取消</VxeButton>
      </template>
    </BasicModal>

    <BasicModal @register="register3" :showFooter="true" title="批量处理">
      <BasicForm style="height: 100%; width: 400px" @register="registerForm3"></BasicForm>

      <template #footer>
        <VxeButton type="primary" @click="approve2">确定</VxeButton>
        <VxeButton type="primary" @click="cancel2">取消</VxeButton>
      </template>
    </BasicModal>
  </div>
</template>

<script lang="tsx">
  import {
    defineComponent,
    PropType,
    ref,
    onMounted,
    watchEffect,
    computed,
    unref,
    watch,
    h,
  } from 'vue';
  import { VXETable, VxeTableInstance } from 'vxe-table';
  import BpmnViewer from '/@/views/process/bpmn/index1.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import EntitySelector from '/@/components/Form/src/components/EntitySelector.vue';
  import PersonSelect from '/@/views/task/components/personSelect.vue';
  import {
    complete,
    deploy,
    deployedList,
    getApprovalRecords,
    getClassName,
    getFlowsTask,
    getProcessComponent,
    getProcessComponentOnChange,
    getProcessOne,
    getTasksListByUser,
    moduleListApi,
    read,
  } from '/@/api/process/process';
  import { ActionItem, TableAction } from '/@/components/Table';
  import { BasicTableProps, VxeBasicTable } from '/@/components/VxeTable';
  import { useRouter } from 'vue-router';
  import {
    Modal,
    Tag,
    Tooltip,
    Steps,
    Step,
    Tabs,
    TabPane,
    Card,
    Collapse,
    CollapsePanel,
  } from 'ant-design-vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { processDef, task } from '/@/views/process/definition/processDefData';
  import { useForm } from '/@/components/Form';
  import { getOptionsApi, getDepartmentList } from '/@/views/demo/system/assembly/api/assembly';
  import { buildApi, getDetailOne, getOne } from '/@/api/erupt/erupt';
  import { useTaskStore } from '/@/store/modules/task';
  import { Description } from '/@/components/Description';
  import {
    batchComplete,
    delegate,
    delegateQ,
    getFormTaskList,
  } from '/@/views/task/assembly/api/assembly';
  import { useUserStore } from '/@/store/modules/user';
  import { Base64 } from 'js-base64';
  import { useGlobSetting } from '/@/hooks/setting';

  export default defineComponent({
    components: {
      EntitySelector,
      Description,
      Tooltip,
      BpmnViewer,
      Tag,
      Tabs,
      TabPane,
      [Steps.name]: Steps,
      [Steps.Step.name]: Steps.Step,
      [Card.name]: Card,
      [Collapse.name]: Collapse,
      [CollapsePanel.name]: CollapsePanel,
      BasicModal,
      VxeBasicTable,
      TableAction,
      BasicForm,
      BasicDrawer,
      VXETable,
      PersonSelect,
    },
    setup(props, { attrs, emit }) {
      const tableRef = ref();
      const globSetting = useGlobSetting();
      const batchAssign = ref(false);
      const userStore = useUserStore();
      const processInstanceId = ref('');
      const pid = ref();
      const pid_batch = ref([]);
      const personNo = ref();
      const batchIds = ref([]);
      const note = ref('');
      const { createMessage } = useMessage();
      const taskStore = useTaskStore();
      const lookSchema = ref([]);
      const taskSchema = ref([]);
      const [register, { openModal, closeModal }] = useModal();
      const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
      const [register2, { openModal: openModal2, closeModal: closeModal2 }] = useModal();
      const [register3, { openModal: openModal3, closeModal: closeModal3 }] = useModal();
      const activeKey = ref('1');
      const acts = ref([]);
      const cName = ref('');
      const step = ref('');
      const steps = ref([]);
      const descData = ref([]);
      const current = ref(0);
      const bussinesKey = ref('');
      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        schemas: taskSchema,
        showActionButtonGroup: false,
      });

      const [
        registerForm3,
        {
          resetFields: resetFields3,
          updateSchema: updateSchema3,
          validate: validate3,
          setFieldsValue: setFieldsValue3,
        },
      ] = useForm({
        labelWidth: 100,
        schemas: [
          {
            field: 'message',
            label: '备注',
            required: true,
            component: 'InputTextArea',
            componentProps: {
              rows: 6,
              defaultValue: note,
            },
            colProps: {
              span: 24,
            },
          },
        ],
        showActionButtonGroup: false,
      });

      const [
        registerForm2,
        { resetFields: resetFields2, updateSchema: updateSchema2, validate: validate2 },
      ] = useForm({
        labelWidth: 100,
        schemas: [
          {
            field: 'option',
            label: '处理方式',
            required: true,
            component: 'Select',
            colProps: {
              span: 16,
            },
            componentProps: {
              options: [
                {
                  label: '审批',
                  value: '审批',
                },
              ],
            },
          },
          {
            field: 'message',
            label: '备注',
            required: true,
            component: 'InputTextArea',
            colProps: {
              span: 24,
            },
          },
        ],
        showActionButtonGroup: false,
      });
      const [
        registerForm1,
        { resetFields: resetFields1, updateSchema: updateSchema1, validate: validate1 },
      ] = useForm({
        labelWidth: 100,
        schemas: [
          {
            field: 'personNo',
            label: '人员',
            component: 'EntitySelector',
            componentProps: {
              valueField: 'no',
              labelField: 'name',
              className: 'Student',
              refClassName: 'person',
            },
            required: true,
            colProps: {
              span: 24,
            },
          },
        ],
        showActionButtonGroup: false,
      });
      const lineOptions = ref([]);
      const departmentList = ref([]);
      const gridOptions = ref<BasicTableProps>({
        id: 'VxeTable',
        keepSource: true,
        editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
        rowConfig: { isHover: true, useKey: true, isCurrent: false },
        columnConfig: { isHover: true, isCurrent: false },
        formConfig: {
          enabled: true,
          items: [
            {
              field: 'line',
              title: '所属线路',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: lineOptions,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'grandDes',
              title: '专业',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: [
                    { label: '车辆', value: '车辆' },
                    { label: '线路', value: '线路' },
                    { label: '轨道', value: '轨道' },
                    { label: '路基', value: '路基' },
                    { label: '车站建筑', value: '车站建筑' },
                    { label: '高架结构', value: '高架结构' },
                    { label: '地下结构', value: '地下结构' },
                    { label: '工程防水', value: '工程防水' },
                    { label: '通风、空调与供暖', value: '通风、空调与供暖' },
                    { label: '给水与排水', value: '给水与排水' },
                    { label: '供电', value: '供电' },
                    { label: '通信', value: '通信' },
                    { label: '信号', value: '信号' },
                    { label: '自动售检票系统', value: '自动售检票系统' },
                    { label: '火灾自动报警系统', value: '火灾自动报警系统' },
                    { label: '综合监控系统', value: '综合监控系统' },
                    { label: '环境与设备监控系统', value: '环境与设备监控系统' },
                    { label: '乘客信息系统', value: '乘客信息系统' },
                    { label: '门禁', value: '门禁' },
                    { label: '运营控制中心', value: '运营控制中心' },
                    { label: '站内客运设备', value: '站内客运设备' },
                    { label: '站台门', value: '站台门' },
                    { label: '车辆基地', value: '车辆基地' },
                    { label: '网络安全', value: '网络安全' },
                    { label: '其他', value: '其他' },
                  ],
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'chapterNumber',
              title: '所属章节',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'mainDeptName',
              title: '责任单位',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: departmentList,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'assistNames',
              title: '配合单位',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: departmentList,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              field: 'stage',
              title: '阶段',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: [
                    { label: '2初步设计', value: '2初步设计' },
                    { label: '3施工设计', value: '3施工设计' },
                    { label: '4合同谈判与设联', value: '4合同谈判与设联' },
                  ],
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              field: 'state',
              title: '状态',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: [
                    { label: '已落实-闭口', value: '已落实-闭口' },
                    { label: '未落实-开口', value: '未落实-开口' },
                    { label: '已反馈-闭口', value: '已反馈-闭口' },
                    { label: '其他-闭口', value: '其他-闭口' },
                    { label: '本条线路不适用', value: '本条线路不适用' },
                    { label: '待操作', value: '待操作' },
                  ],
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              field: 'updateTime',
              title: '下发时间',
              itemRender: {
                name: 'ARangePicker',
                props: {
                  showTime: false, // 设置为 false 只显示年月日
                  valueFormat: 'YYYY-MM-DD', // 设置日期格式
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              field: 'des',
              title: '条款内容',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              span: 24,
              align: 'center',
              className: '!pr-0',
              collapseNode: true,
              itemRender: {
                name: 'AButtonGroup',
                children: [
                  {
                    props: {
                      type: 'primary',
                      content: '查询',
                      htmlType: 'submit',
                    },
                    attrs: {
                      class: 'mr-2',
                    },
                  },
                  {
                    props: {
                      type: 'default',
                      htmlType: 'reset',
                      content: '重置',
                    },
                  },
                ],
              },
            },
          ],
        },
        cellStyle({ row, column }) {
          if (column.field === 'state') {
            // 根据状态值设置不同的字体颜色
            if (row.state === '已落实-闭口') {
              return {
                color: '#1E90FF',
              };
            } else if (row.state === '未落实-开口') {
              return {
                color: '#32CD32',
              };
            } else if (row.state === '已反馈-闭口') {
              return {
                color: '#FFD700',
              };
            } else if (row.state === '其他-闭口') {
              return {
                color: '#FF6347',
              };
            } else if (row.state === '本条线路不适用') {
              return {
                color: '#228B22',
              };
            } else if (row.state === '待操作') {
              return {
                color: '#DC143C',
              };
            }
            // 默认字体颜色
            return {
              color: '#606266',
            };
          }
        },
        columns: [
          {
            type: 'checkbox',
            width: '50',
            align: 'center',
          },
          {
            title: '序号',
            type: 'seq',
            width: '50',
            align: 'center',
          },
          {
            field: 'line',
            showOverflow: 'title',
            title: '所属线路',
            sortable: false,
            width: '150',
          },
          {
            field: 'grandDes',
            showOverflow: 'title',
            title: '专业',
            sortable: false,
            width: '150',
          },
          {
            field: 'chapterNumber',
            showOverflow: 'title',
            title: '所属章节',
            sortable: false,
            width: '150',
          },
          {
            field: 'des',
            showOverflow: 'title',
            title: '条款内容',
            sortable: false,
            width: '300',
          },
          {
            field: 'mainDeptName',
            showOverflow: 'title',
            title: '责任单位',
            sortable: false,
            width: '150',
          },
          {
            field: 'assistDeptName',
            showOverflow: 'title',
            title: '配合单位',
            sortable: false,
            width: '150',
          },
          {
            field: 'stage',
            showOverflow: 'title',
            title: '阶段',
            sortable: true,
            width: '150',
          },
          {
            field: 'state',
            showOverflow: 'title',
            title: '状态',
            sortable: true,
            width: '150',
          },
          {
            field: 'updateTime',
            showOverflow: 'title',
            title: '下发时间',
            sortable: false,
            width: '150',
          },
          {
            title: '操作',
            align: 'center',
            width: 230,
            slots: {
              default: 'action',
            },
            fixed: 'right',
          },
        ],
        toolbarConfig: {
          refresh: true, // 显示刷新按钮
          import: false, // 显示导入按钮
          export: false, // 显示导出按钮
          print: false, // 显示打印按钮
          zoom: false, // 显示全屏按钮
          custom: true,
          buttons: [
            {
              content: '批量处理',
              buttonRender: {
                name: 'AButton',
                props: {
                  type: 'primary',
                },
                events: {
                  click: () => {
                    debugger;
                    const records = tableRef.value.getCheckboxRecords();
                    if (records.length == 0) {
                      createMessage.error('请选择需要处理的记录！');
                    } else {
                      let flag = true;
                      let pids = [];
                      let stepStr = '';
                      pid_batch.value = [];
                      for (const recordsKey in records) {
                        pids.push(records[recordsKey].processInstanceId);
                        stepStr = records[recordsKey].taskStep;
                        if (records[recordsKey].taskStep == '经办人') {
                          flag = false;
                          break;
                        }
                      }
                      console.log('step', stepStr);
                      if (flag) {
                        pid_batch.value = pids;
                        if (stepStr == '科室中层') {
                          note.value = '已核实，同意提交';
                        }
                        if (stepStr == '单位中层') {
                          note.value = '已确认，可闭环流程';
                        }
                        openModal3();
                        setTimeout(() => {
                          setFieldsValue3({ message: note.value });
                        }, 100);

                        /*batchComplete(pids).then((res) => {
                          if (res) {
                            createMessage.success('批量处理成功');
                            document.querySelector('button[title="刷新"]').click();
                          }
                        });*/
                      } else {
                        createMessage.error('存在无法批量的步骤');
                      }
                    }
                  },
                },
              },
            },
            {
              content: '转办',
              buttonRender: {
                name: 'AButton',
                props: {
                  type: 'primary',
                },
                events: {
                  click: () => {
                    debugger;
                    let flag = true;
                    const records = tableRef.value.getCheckboxRecords();
                    if (records.length == 0) {
                      createMessage.error('请选择需要处理的记录！');
                    } else {
                      for (const recordsKey in records) {
                        if (records[recordsKey].taskStep == '单位中层') {
                          flag = false;
                          break;
                        }
                      }
                      if (flag) {
                        openModal1();
                        let ids = [];
                        records.forEach((item) => {
                          ids.push(item.taskId);
                        });
                        batchIds.value = ids;
                        batchAssign.value = true;
                      } else {
                        createMessage.error('存在无法批量的步骤');
                      }
                    }
                  },
                },
              },
            },
            /*{
              content: '批量处理',
              buttonRender: {
                name: 'AButton',
                props: {
                  type: 'primary',
                },
                events: {
                  click: () => {
                    debugger;
                    const records = tableRef.value.getCheckboxRecords();
                    if (records.length == 0) {
                      createMessage.error('请选择需要处理的记录！');
                    } else {
                      let flag = true;
                      let pids = [];
                      for (const recordsKey in records) {
                        pids.push(records[recordsKey].processInstanceId);
                        if (records[recordsKey].taskStep == '经办人') {
                          flag = false;
                          break;
                        }
                      }
                      if (flag) {
                        batchComplete(pids).then((res) => {
                          if (res) {
                            createMessage.success('批量处理成功');
                            document.querySelector('button[title="刷新"]').click();
                          }
                        });
                      } else {
                        createMessage.error('存在无法批量的步骤');
                      }
                    }
                  },
                },
              },
            },*/
          ],
        },
        height: 'auto',
        proxyConfig: {
          ajax: {
            query: async ({ page, form }) => {
              debugger;
              const bpmns = await getFormTaskList(page, form);
              return bpmns;
            },
            queryAll: async ({ form }) => {
              const bpmns = await getFormTaskList();
              let rows = [];
              bpmns.forEach((bpmn) => {
                rows.push({ name: bpmn.split('.bpmn')[0] });
              });
              return rows;
            },
          },
        },
      });
      function actionChange(e) {
        console.log(e, step.value);
        getProcessComponentOnChange(pid.value, cName.value, { option: e ? e : '' }).then((res) => {
          if (res) {
            res.forEach((item) => {
              if (item.field == 'option') item.componentProps.onChange = (e) => actionChange(e);
            });
            taskSchema.value = res;
          }
        });
      }
      interface RowVO {
        name: string;
      }

      const tableData = ref<Array<RowVO>>([]);

      onMounted(async () => {
        const options = await getOptionsApi();
        lineOptions.value = options;
        const departments = await getDepartmentList();
        departmentList.value = departments;
      });

      return {
        tableData,
        gridOptions,
        register,
        register1,
        register2,
        register3,
        openModal,
        openModal1,
        openModal2,
        openModal3,
        closeModal,
        closeModal1,
        closeModal2,
        closeModal3,
        registerForm,
        registerForm1,
        registerForm2,
        registerForm3,
        resetFields1,
        resetFields2,
        resetFields3,
        validate1,
        validate2,
        validate3,
        descData,
        activeKey,
        acts,
        lookSchema,
        taskSchema,
        steps,
        personNo,
        pid_batch,
        setFieldsValue,
        setFieldsValue3,
        tableRef,
        current,
        lineOptions,
        departmentList,
        cancel: () => {
          closeModal();
        },
        approve1: () => {},
        cancel1: () => {},
        approve2: async () => {
          let values = await validate3();
          batchComplete({ pids: pid_batch.value, ...values }).then((res) => {
            if (res) {
              closeModal3();
              resetFields3();
              createMessage.success('批量处理成功');
              document.querySelector('button[title="刷新"]').click();
            }
          });
        },
        cancel2: () => {
          closeModal3();
          resetFields3();
        },
        approve: async () => {
          let values = await validate();
          const params = {
            step: step.value,
            ...values,
            bussinesKey: bussinesKey.value,
            processInstanceId: processInstanceId.value,
          };
          complete(pid.value, params, cName.value).then((res) => {
            if (res == 200) {
              createMessage.success('办理完成');
              resetFields();
              document.querySelector('button[title="刷新"]').click();
            }
            taskStore.setTaskList();
            closeModal();
          });
        },
        handleShow: (downloadUrl, index) => {
          console.log(downloadUrl);
          const userStore = useUserStore();
          const watermarkTxt = userStore.userInfo.username + ' ' + userStore.userInfo.realName;
          modalUrl.value = ''; // 清空旧缓存
          modalUrl.value =
            globSetting.previewUrl +
            '/onlinePreview?url=' +
            downloadUrl +
            '&watermarkTxt=' +
            watermarkTxt; // 设置预览 URL
          key.value = index; // 重新加载 iframe
          openModalPreview(true); // 打开 modal 窗口
        },
        handleSubmit: async (v) => {
          /* let values = await validate1();
         console.log(values)*/
          if (!batchAssign.value) {
            delegate([pid.value], v).then((res) => {
              if (res) {
                createMessage.success('转办成功');
                closeModal1();
                document.querySelector('button[title="刷新"]').click();
              }
            });
          } else {
            delegate(batchIds.value, v).then((res) => {
              if (res) {
                createMessage.success('转办成功');
                closeModal1();
                document.querySelector('button[title="刷新"]').click();
              }
            });
          }
        },
        processInstanceId,
        batchAssign,
        batchIds,
        queryContions: [
          {
            field: 'no',
            title: '工号',
            itemRender: {
              name: 'AInput',
              defaultValue: '',
            },
            span: 6,
          },
          {
            field: 'name',
            title: '姓名',
            itemRender: {
              name: 'AInput',
              defaultValue: '',
            },
            span: 6,
          },
          {
            span: 4,
            align: 'right',
            className: '!pr-0',
            itemRender: {
              name: 'AButtonGroup',
              children: [
                {
                  props: {
                    type: 'primary',
                    content: '查询',
                    htmlType: 'submit',
                  },
                  attrs: {
                    class: 'mr-2',
                  },
                },
                {
                  props: {
                    type: 'default',
                    htmlType: 'reset',
                    content: '重置',
                  },
                },
              ],
            },
          },
        ],
        preCondition: new Map([
          ['deptStr', userStore.getUserInfo.dept],
          ['postStr', userStore.getUserInfo.post],
        ]),
        handleCancel: () => {
          resetFields();
        },
        createActions: (record) => {
          const actions: ActionItem[] = [
            {
              label: '办理',
              onClick: async () => {
                console.log(record);
                step.value = record.taskStep;
                pid.value = record.taskId;
                processInstanceId.value = record.processInstanceId;
                let schema = [];

                getClassName(record.taskId).then(async (res) => {
                  if (res) {
                    bussinesKey.value = res;
                    const arr = res.split('.');
                    const className = arr[0];
                    cName.value = className;
                    getProcessComponent(record.taskId, className).then((res) => {
                      console.log('component', res);
                      if (res) {
                        res.forEach((item) => {
                          if (item.field == 'option')
                            item.componentProps.onChange = (e) => actionChange(e);
                        });
                        taskSchema.value = res;
                      }
                    });
                    const id = arr[1];
                    buildApi(className).then(async (res) => {
                      const {
                        eruptModel: { eruptFieldModels, eruptJson },
                        tabErupts,
                        power,
                      } = res;

                      let details = [];
                      eruptFieldModels.forEach((item) => {
                        const key = item.fieldName;
                        const title = item.eruptFieldJson.edit.title;
                        if (item.eruptFieldJson.views.length > 0) {
                          item.eruptFieldJson.views.forEach((v) => {
                            if (v.show) {
                              const k = v.column ? key + '_' + v.column : key;
                              if (item.eruptFieldJson.edit.show.detail_show) {
                                debugger;
                                const d = {
                                  field: k,
                                  label: v.title,
                                  span: item.eruptFieldJson.edit.detailSpan,
                                };
                                details.push(d);
                              }
                            } else {
                              if (item.eruptFieldJson.edit.show.detail_show) {
                                const d = {
                                  field: key,
                                  label: title,
                                  span: item.eruptFieldJson.edit.detailSpan,
                                };
                                details.push(d);
                              }
                            }
                          });
                        } else {
                          debugger;
                          if (item.eruptFieldJson.edit.show.detail_show && key !== 'id') {
                            if (item.eruptFieldJson.edit.type == 'ATTACHMENT') {
                              if (!item.eruptFieldJson.edit.attachmentType.isgBigFile) {
                                const d = {
                                  field: key,
                                  label: title,
                                  render: (val) => {
                                    return (
                                      <div>
                                        {val
                                          ? val.split('|').map((item, index) => {
                                              return (
                                                <div>
                                                  <a
                                                    onClick={() =>
                                                      handleShow(
                                                        encodeURIComponent(
                                                          Base64.encode(
                                                            globSetting.eruptAttachment + item,
                                                          ),
                                                        ),
                                                        index,
                                                      )
                                                    }
                                                  >
                                                    {item.substring(item.lastIndexOf('/') + 1)}
                                                  </a>{' '}
                                                  <a
                                                    style={{ marginLeft: '15px', color: '#ff6118' }}
                                                    href={globSetting.eruptAttachment + item}
                                                  >
                                                    下载
                                                  </a>
                                                </div>
                                              );
                                            })
                                          : ''}

                                        {/*<Tooltip placement={'bottom'}>
                          <a-button
                            onClick={() => {
                              const files = val ? val.split('|') : [];
                              uploadModalRef.value.setFiles(files);
                              openPreviewModal();
                            }}
                          >
                            <Icon icon="bi:eye" />
                          </a-button>
                        </Tooltip>*/}
                                      </div>
                                    );
                                  },
                                };
                                details.push(d);
                              } else {
                                const d = {
                                  field: key,
                                  label: title,
                                  render: (val) => {
                                    return (
                                      <div>
                                        {val
                                          ? val.split('|').map((item, index) => {
                                              return (
                                                <div>
                                                  <a
                                                    style={{ marginLeft: '15px', color: '#ff6118' }}
                                                    href={globSetting.eruptAttachment + item}
                                                  >
                                                    {item.substring(item.lastIndexOf('/') + 1)}
                                                  </a>
                                                </div>
                                              );
                                            })
                                          : ''}

                                        {/*<Tooltip placement={'bottom'}>
                          <a-button
                            onClick={() => {
                              const files = val ? val.split('|') : [];
                              uploadModalRef.value.setFiles(files);
                              openPreviewModal();
                            }}
                          >
                            <Icon icon="bi:eye" />
                          </a-button>
                        </Tooltip>*/}
                                      </div>
                                    );
                                  },
                                };
                                details.push(d);
                              }
                            } else {
                              const d = {
                                field: key,
                                label: title,
                                span: item.eruptFieldJson.edit.detailSpan,
                              };
                              details.push(d);
                            }
                          }
                        }
                        //  formState[key] = null
                      });
                      /*details.push({
                      field: 'message',
                      label: '备注',
                      required: true,
                      component: 'InputTextArea',
                      colProps: {span: 24},
                    })*/
                      lookSchema.value = details;
                      const entity = await getDetailOne(className, id);
                      descData.value = entity;

                      //gridOptions.columns = columns
                      //search()
                    });
                    const records = await getApprovalRecords(record.processInstanceId);
                    steps.value = records;
                    current.value = records.length;
                    openModal();
                  }
                });
              },
            },
          ];
          /*if (record.taskStep == '经办人')
            actions.push({
              label: '转办',
              onClick: () => {
                pid.value = record.taskId;
                openModal1();
              },
            });*/
          return actions;
        },
      };
    },
  });
</script>
<style scoped>
  .ant-select-disabled {
    color: #303030;
    background: #fff;
    cursor: not-allowed;
  }
</style>
