import { useUserStore } from '/@/store/modules/user';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  taskList = '/newline/assembly/process/taskList/',
  taskInfoList = '/newline/assembly/process/form/taskList/',
  taskInfoListBatch = '/newline/assembly/process/form/taskList/batch/',
  delegate = '/newline/assembly/process/delegate/',
  delegateQ = '/newline/question/process/delegate/',
  batchComplete = '/newline/assembly/process/complete/batch/',
}

export const getTasksList = () => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.taskList + userName });
};

export const getFormTaskList = (page, form) => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.taskInfoList + userName, params: { ...page, ...form } });
};

export const getFormTaskListBatch = (page, form) => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.taskInfoListBatch + userName, params: { ...page, ...form } });
};

export const delegate = (taskIds, no) => {
  return defHttp.post({ url: Api.delegate + `${no}`, params: taskIds });
};

export const delegateQ = (taskIds, no) => {
  return defHttp.post({ url: Api.delegateQ + `${no}`, params: taskIds });
};

export const batchComplete = (params) => {
  return defHttp.post({ url: Api.batchComplete, params });
};
