<template>
  <div>
    <vxe-table
      :data="tableData">
      <vxe-column type="seq" width="60"></vxe-column>
      <vxe-column field="name" title="Name"></vxe-column>
    </vxe-table>
  </div>
</template>

<script lang="ts">
import {defineComponent, ref, onMounted, watchEffect, computed, unref, watch, h} from 'vue';
import {VXETable} from 'vxe-table'
import {useMessage} from "/@/hooks/web/useMessage";
import BasicDrawer from "/@/components/Drawer/src/BasicDrawer.vue";
import BasicForm from "/@/components/Form/src/BasicForm.vue";
import {moduleListApi} from "/@/api/demo/process";

export default defineComponent({
  components: {BasicForm, BasicDrawer, VXETable},
  setup(props, {attrs, emit}) {
    const {createMessage} = useMessage();

    interface RowVO {
      name: string
    }

    const tableData = ref<Array<RowVO>>([])

    onMounted(() => {
      moduleListApi().then(res => {
        const bpmns = res.result
        let rows = []
        bpmns.forEach(bpmn => {
          rows.push({name: bpmn})
        })
        tableData.value = rows
      })
    })

    return {
      tableData
    };
  },
})


</script>
