<template>
  <div>
    <erupt-table
      title="会议管理"
      class-name="Meeting"
      ref="meetingTable"
      :row-dynamic-controller="rowDynamicController"
      :overwrite-action="overwriteAction"
    />

    <!-- 详情弹窗 -->
    <BasicModal
      @register="registerDetail"
      :showFooter="false"
      width="60%"
      title="会议详情"
      @ok="handleDetailOk"
      @close="handleDetailClose"
    >
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="会议详细信息">
          <div class="detail-container">
            <!-- 基本信息 -->
            <div class="detail-section">
              <h3 class="section-title">基本信息</h3>
              <div class="detail-grid">
                <div class="detail-label">会议内容</div>
                <div class="detail-value">{{ detailVal.title }}</div>
                <div class="detail-label">会议时间</div>
                <div class="detail-value">{{ detailVal.startMeetingDate }}</div>
                <div class="detail-label">地点</div>
                <div class="detail-value">{{ detailVal.location }}</div>
              </div>
            </div>

            <!-- 参与信息 -->
            <div class="detail-section">
              <h3 class="section-title">参与信息</h3>
              <div class="detail-grid">
                <div class="detail-label">所属线路</div>
                <div class="detail-value">{{ detailVal.lineId }}</div>
                <div class="detail-label">会议类别</div>
                <div class="detail-value">{{ detailVal.meetingCategory }}</div>
                <div class="detail-label">会议通知状态</div>
                <div class="detail-value">{{ detailVal.meetingStatus }}</div>
              </div>
            </div>

            <!-- 会议资料 -->
            <div class="detail-section">
              <h3 class="section-title">会议资料</h3>
              <a-descriptions-item label="附件">
                <div v-if="detailVal.attachment1">
                  <div
                    v-for="(item, index) in detailVal.attachment1.split('|')"
                    :key="index"
                    style="margin: 4px 0"
                  >
                    <a class="file-link" @click="previewShow(item, index)">
                      {{ item.substring(item.lastIndexOf('/') + 1) }}
                    </a>
                    <a
                      style="margin-left: 10px; color: #ff6118"
                      :href="eruptAttachment + item"
                      target="_blank"
                      download
                    >
                      下载
                    </a>
                  </div>
                </div>
                <span v-else style="color: #8b949e">暂无附件</span>
              </a-descriptions-item>
            </div>

            <!-- 会议结果 -->
            <div class="detail-section">
              <h3 class="section-title">会议结果</h3>
              <div class="detail-content">{{ detailVal.content }}</div>
            </div>

            <!-- 联络人 -->
            <div class="detail-section">
              <h3 class="section-title">联络人</h3>
              <div class="detail-content">{{ detailVal.participants }}</div>
            </div>

            <!-- 参与部门 -->
            <div class="detail-section">
              <h3 class="section-title">参会单位</h3>
              <div class="detail-content">{{ detailVal.departments }}</div>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import eruptTable from '../../../../components/EruptTable/eruptTable';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Base64 } from 'js-base64';
  import { useUserStore } from '/@/store/modules/user';
  import { buildApi, getOne } from '/@/api/erupt/erupt';
  import { detail1, detail2, detail3, detail4 } from './api/meeting';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const globSetting = useGlobSetting();
  const eruptAttachment = globSetting?.eruptAttachment;
  const activeKey = ref('1');
  const detailVal = ref<Record<string, any>>({});
  const lineOptions = ref<any[]>([]);

  // 文件预览
  const previewShow = (downloadUrl: string, index: string | number) => {
    const fullUrl = globSetting.eruptAttachment + downloadUrl;
    const encodedUrl = encodeURIComponent(Base64.encode(fullUrl));
    const userStore = useUserStore();
    const watermark = `${userStore.getUserInfo.username} ${userStore.getUserInfo.realName}`;

    window.open(
      `${globSetting.previewUrl}/onlinePreview?url=${encodedUrl}&watermarkTxt=${encodeURIComponent(
        watermark,
      )}`,
      '_blank',
    );
  };

  // 获取会议详情
  const fetchDetail = async (id: string) => {
    try {
      const res = await getOne('Meeting', id);
      const {
        eruptModel: { eruptFieldModels },
      } = await buildApi('Meeting');

      // 处理线路和部门显示
      const lineField = eruptFieldModels.find((item: any) => item.fieldName === 'lineId');
      lineOptions.value = lineField?.componentValue || [];

      const departments = await detail1(id);
      const participants = await detail2(id);
      const lineId = await detail4(id);
      if (res.meetingCategory !== undefined) {
        const meetingCategory = await detail3(res.meetingCategory);
        res.meetingCategory = meetingCategory.meetingCategory;
      }
      res.departments = departments.departments;
      res.participants = participants.participants;
      res.lineId = lineId.lineId;
      detailVal.value = res;
    } catch (error) {
      createMessage.error('获取详情失败');
    }
  };

  const rowDynamicController = {
    edit: (rowData: any) => {
      const status = rowData.meetingStatus || '未知状态';
      return status !== '取消';
    },
    delete: (rowData: any) => {
      const status = rowData.meetingStatus || '未知状态';
      return status !== '取消';
    },
  };

  // 注册弹窗
  const [registerDetail, { openModal: openDetailModal, closeModal: closeDetailModal }] = useModal();

  // 打开详情弹窗
  const showDetail = async (id: string) => {
    await fetchDetail(id);
    openDetailModal();
  };

  // 关闭详情弹窗
  const handleDetailOk = () => {
    closeDetailModal();
  };

  const handleDetailClose = () => {
    closeDetailModal();
  };

  // 定义 overwriteAction（仅保留详情操作）
  const overwriteAction = {
    detail: async (row: any) => {
      await showDetail(row.id);
    },
  };
</script>

<style scoped lang="less">
  .detail-container {
    padding: 16px;
  }

  .detail-section {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fafafa;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
    color: #333;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .detail-label {
    font-weight: bold;
    color: #666;
  }

  .detail-value {
    color: #333;
  }

  .detail-content {
    white-space: pre-wrap;
    padding: 8px;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    color: #333;
  }

  .file-link {
    color: #1890ff;
    margin-right: 12px;

    &:hover {
      text-decoration: underline;
    }
  }

  .no-data {
    color: #999;
    font-style: italic;
  }
</style>
