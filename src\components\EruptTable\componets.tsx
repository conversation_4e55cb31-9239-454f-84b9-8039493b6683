import { ComponentType } from '/@/components/Form/src/types';
import { formatDate } from '@vueuse/shared';
import { findCheckBox, getRefClass, refBuildTreeApi, upload } from '/@/api/erupt/erupt';
import { UploadFileParams } from '/#/axios';
import { defHttp } from '/@/utils/http/axios/erupt-request';
import { UploadApiResult } from '/@/api/sys/model/uploadModel';
import { Api } from '/@/api/erupt/erupt';
import { useGlobSetting } from '/@/hooks/setting';
import bigFile from '/@/views/demo/bigFile/index.vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { Col, TabPane, Tabs } from 'ant-design-vue';
import TableAdd from '/@/components/Form/src/components/TableAdd';
import EruptTable from './eruptTable';

/**
 * Component list, register here to setting it in the form
 */

//ant-desing本身的Form控件库

const globSetting = useGlobSetting();

const componentMap = new Map<string, string>();
const dateFormatMap = new Map<string, string>();

componentMap.set('INPUT', 'Input');
componentMap.set('DIVIDE', 'Divide');
componentMap.set('NUMBER', 'InputNumber');
componentMap.set('BOOLEAN', 'Select');
componentMap.set('DATE', 'DatePicker');
componentMap.set('REFERENCE_TABLE', 'EntitySelector');
componentMap.set('REFERENCE_TREE', 'EruptTreeSelect');
componentMap.set('TEXTAREA', 'InputTextArea');
componentMap.set('ATTACHMENT', 'Attachment');
componentMap.set('CODE_EDITOR', 'CodeEditor');
componentMap.set('HTML_EDITOR', 'HtmlEditor');
componentMap.set('CHOICE', 'Select');
componentMap.set('TAGS', 'TagSelect');
componentMap.set('TAGSEDIT', 'TagEdit');
componentMap.set('CHECKBOX', 'CheckGroup');

dateFormatMap.set('DATE', 'YYYY-MM-DD');
dateFormatMap.set('DATE_TIME', 'YYYY-MM-DD HH:mm');
dateFormatMap.set('TIME', 'HH:mm');

export function add(compName: ComponentType, component: string) {
  componentMap.set(compName, component);
}

export function del(compName: ComponentType) {
  componentMap.delete(compName);
}

export function getComponent(type, edit) {
  if (type == 'DATE') {
    if (edit.dateType.type == 'DATE' || edit.dateType.type == 'DATE_TIME')
      return componentMap.get(type);
    if (edit.dateType.type == 'YEAR') return 'YearPicker';
    if (edit.dateType.type == 'MONTH') return 'MonthPicker';
    if (edit.dateType.type == 'WEEK') return 'WeekPicker';
    if (edit.dateType.type == 'TIME') return 'TimePicker';
  } else if (type == 'INPUT') {
    if (edit.inputType) {
      if (edit.inputType.type == 'color') {
        return 'ColorPicker';
      }
      if (edit.inputType.type == 'text') {
        return 'Input';
      }
    }
  } else if (type == 'ATTACHMENT') {
    if (edit.attachmentType.isgBigFile) {
      return 'BigFileUpload';
    } else {
      return componentMap.get(type);
    }
  } else {
    return componentMap.get(type);
  }
}
export function getSearchRefComponent(key, title, model, span, className, tabName) {
  let q = {
    field: key,
    title,
    itemRender: {
      name: 'AInput',
    },
    span: span,
  };
  let props = {};
  if (model.eruptFieldJson.edit.type == 'DATE') {
    if (
      model.eruptFieldJson.edit.dateType.type == 'DATE' ||
      model.eruptFieldJson.edit.dateType.type == 'DATE_TIME' ||
      model.eruptFieldJson.edit.dateType.type == 'TIME'
    ) {
      props = model.eruptFieldJson.edit.dateType;
      q.itemRender.name = 'ADatePicker';
      q.itemRender.props = {
        valueFormat: dateFormatMap.get(model.eruptFieldJson.edit.dateType.type),
      };
      if (model.eruptFieldJson.edit.search.vague) {
        q.itemRender.name = 'ARangePicker';
      }
    }
  }
  if (model.eruptFieldJson.edit.type == 'BOOLEAN') {
    props = model.eruptFieldJson.edit.boolType;
    q.itemRender.name = 'ASelect';
    q.itemRender.props = {
      options: [
        { value: true, label: model.eruptFieldJson.edit.boolType.trueText },
        {
          value: false,
          label: model.eruptFieldJson.edit.boolType.falseText,
        },
      ],
    };
  }
  if (model.eruptFieldJson.edit.type == 'CHOICE') {
    props = model.eruptFieldJson.edit.boolType;
    q.itemRender.name = 'ASelect';
    q.itemRender.props = {
      options: model.componentValue,
    };
  }
  if (model.eruptFieldJson.edit.type == 'NUMBER') {
    q.itemRender.name = 'AInputNumber';
  }
  if (model.eruptFieldJson.edit.type == 'TAGS') {
    q.itemRender.name = 'ATagSelect';
    const { tagsType } = model.eruptFieldJson.edit;
    if (tagsType && !tagsType.allowExtension) {
      let options = [];
      model.componentValue.forEach((item) => options.push({ label: item, value: item }));
      q.itemRender.props = {
        options: options,
        joinSeparator: model.eruptFieldJson.edit.tagsType
          ? model.eruptFieldJson.edit.tagsType.joinSeparator
          : '|',
      };
    } else if (tagsType && tagsType.allowExtension) {
      q.itemRender.props = {
        joinSeparator: tagsType.joinSeparator,
        dropdownStyle: { display: 'none' },
      };
    } else {
      let options = [];
      model.componentValue.forEach((item) => options.push({ label: item, value: item }));
      q.itemRender.props = {
        options: options,
        joinSeparator: model.eruptFieldJson.edit.tagsType
          ? model.eruptFieldJson.edit.tagsType.joinSeparator
          : '|',
      };
    }
  }

  if (model.eruptFieldJson.edit.type == 'REFERENCE_TREE') {
    q.itemRender.name = 'AEruptTreeSelect';
    getRefClass(className, tabName).then((res) => {
      q.itemRender.props = {
        className: res.result,
        tabName: key,
      };
    });
  }
  if (model.eruptFieldJson.edit.type == 'REFERENCE_TABLE') {
    debugger;
    q.itemRender.name = 'AEntitySelector';
    const valueField = model.eruptFieldJson.edit.referenceTableType.id;
    const labelField = model.eruptFieldJson.edit.referenceTableType.label;
    getRefClass(className, tabName).then((res) => {
      q.itemRender.props = {
        valueField,
        labelField,
        className: res.result,
        refClassName: key,
      };
    });
  }
  if (
    model.eruptFieldJson.edit.type == 'INPUT' &&
    model.eruptFieldJson.edit.inputType &&
    model.eruptFieldJson.edit.inputType.type == 'color'
  ) {
    q.itemRender.name = 'AColorPicker';
  }
  return q;
}
export function getSearchComponent(key, title, model, span, className, defaultValue) {
  let q = {
    field: key,
    title,
    itemRender: {
      name: 'AInput',
      defaultValue: defaultValue != undefined ? defaultValue : '',
    },
    span: span,
  };
  let props = {};
  if (model.eruptFieldJson.edit.type == 'DATE') {
    if (
      model.eruptFieldJson.edit.dateType.type == 'DATE' ||
      model.eruptFieldJson.edit.dateType.type == 'DATE_TIME' ||
      model.eruptFieldJson.edit.dateType.type == 'TIME'
    ) {
      props = model.eruptFieldJson.edit.dateType;
      q.itemRender.name = 'ADatePicker';
      q.itemRender.props = {
        defaultValue: defaultValue != undefined ? defaultValue : '',
        valueFormat: dateFormatMap.get(model.eruptFieldJson.edit.dateType.type),
      };
      if (model.eruptFieldJson.edit.search.vague) {
        q.itemRender.name = 'ARangePicker';
      }
    }
  }
  if (model.eruptFieldJson.edit.type == 'BOOLEAN') {
    props = model.eruptFieldJson.edit.boolType;
    q.itemRender.name = 'ASelect';
    q.itemRender.props = {
      defaultValue: defaultValue != undefined ? defaultValue : '',
      options: [
        { value: true, label: model.eruptFieldJson.edit.boolType.trueText },
        {
          value: false,
          label: model.eruptFieldJson.edit.boolType.falseText,
        },
      ],
    };
  }
  if (model.eruptFieldJson.edit.type == 'CHOICE') {
    props = model.eruptFieldJson.edit.boolType;
    q.itemRender.name = 'ASelect';
    q.itemRender.props = {
      defaultValue: defaultValue != undefined ? defaultValue : '',
      options: model.componentValue,
    };
  }
  if (model.eruptFieldJson.edit.type == 'NUMBER') {
    q.itemRender.name = 'AInputNumber';
  }
  if (model.eruptFieldJson.edit.type == 'TAGS') {
    q.itemRender.name = 'ATagSelect';
    const { tagsType } = model.eruptFieldJson.edit;
    if (tagsType && !tagsType.allowExtension) {
      let options = [];
      model.componentValue.forEach((item) => options.push({ label: item, value: item }));
      q.itemRender.props = {
        defaultValue: defaultValue != undefined ? defaultValue : '',
        options: options,
        joinSeparator: model.eruptFieldJson.edit.tagsType
          ? model.eruptFieldJson.edit.tagsType.joinSeparator
          : '|',
      };
    } else if (tagsType && tagsType.allowExtension) {
      q.itemRender.props = {
        defaultValue: defaultValue != undefined ? defaultValue : '',
        joinSeparator: tagsType.joinSeparator,
        dropdownStyle: { display: 'none' },
      };
    } else {
      let options = [];
      model.componentValue.forEach((item) => options.push({ label: item, value: item }));
      q.itemRender.props = {
        defaultValue: defaultValue != undefined ? defaultValue : '',
        options: options,
        joinSeparator: model.eruptFieldJson.edit.tagsType
          ? model.eruptFieldJson.edit.tagsType.joinSeparator
          : '|',
      };
    }
  }

  if (model.eruptFieldJson.edit.type == 'REFERENCE_TREE') {
    debugger;
    q.itemRender.name = 'AEruptTreeSelect';
    q.itemRender.props = {
      className,
      tabName: model.fieldName,
      defaultValue: defaultValue != undefined ? defaultValue : '',
    };
  }
  if (model.eruptFieldJson.edit.type == 'REFERENCE_TABLE') {
    q.itemRender.name = 'AEntitySelector';
    const valueField = model.eruptFieldJson.edit.referenceTableType.id;
    const labelField = model.eruptFieldJson.edit.referenceTableType.label;
    q.itemRender.props = {
      valueField,
      labelField,
      className,
      refClassName: model.fieldName,
      defaultValue: defaultValue != undefined ? defaultValue : '',
    };
  }
  if (
    model.eruptFieldJson.edit.type == 'INPUT' &&
    model.eruptFieldJson.edit.inputType &&
    model.eruptFieldJson.edit.inputType.type == 'color'
  ) {
    q.itemRender.name = 'AColorPicker';
    q.itemRender.props = {
      defaultValue: defaultValue != undefined ? defaultValue : '',
    };
  }
  return q;
}

export function getComponentProps(model, className) {
  if (model.eruptFieldJson.edit.type == 'BOOLEAN') {
    return {
      options: [
        { label: model.eruptFieldJson.edit.boolType.trueText, value: true },
        { label: model.eruptFieldJson.edit.boolType.falseText, value: false },
      ],
    };
  }
  if (model.eruptFieldJson.edit.type == 'DIVIDE') {
    return {
      title: model.eruptFieldJson.edit.title,
    };
  }
  if (model.eruptFieldJson.edit.type == 'CHECKBOX') {
    return {
      className: className,
      fieldName: model.fieldName,
    };
    /*return {
      title: model.eruptFieldJson.edit.title,
    };*/
  }
  if (model.eruptFieldJson.edit.type == 'CHOICE') {
    return {
      options: model.componentValue,
    };
  }

  if (model.eruptFieldJson.edit.type == 'TAGS') {
    const { tagsType } = model.eruptFieldJson.edit;
    if (tagsType && !tagsType.allowExtension) {
      let options = [];
      model.componentValue.forEach((item) => options.push({ label: item, value: item }));
      return {
        options: options,
        joinSeparator: tagsType.joinSeparator,
      };
    } else if (tagsType && tagsType.allowExtension) {
      return {
        joinSeparator: tagsType.joinSeparator,
        dropdownStyle: { display: 'none' },
      };
    } else {
      let options = [];
      model.componentValue.forEach((item) => options.push({ label: item, value: item }));
      return {
        options,
        joinSeparator: '|',
      };
    }
  }

  if (model.eruptFieldJson.edit.type == 'ATTACHMENT') {
    console.log('fileTypes', model.eruptFieldJson.edit.attachmentType.fileTypes);
    return {
      api: getUpLoadApi(className, model.fieldName),
      accept: model.eruptFieldJson.edit.attachmentType.fileTypes,
    };
  }

  if (model.eruptFieldJson.edit.type == 'TAGSEDIT') {
    const { tagsType } = model.eruptFieldJson.edit;
    return {
      joinSeparator: tagsType ? tagsType.joinSeparator : '|',
    };
  }

  if (model.eruptFieldJson.edit.type == 'TEXTAREA') {
    return {
      rows: 5,
    };
  }
  if (model.eruptFieldJson.edit.type == 'DATE') {
    if (model.eruptFieldJson.edit.dateType.type == 'DATE') return { valueFormat: 'YYYY-MM-DD' };
    if (model.eruptFieldJson.edit.dateType.type == 'DATE_TIME')
      return {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: { format: 'HH:mm', minuteStep: 10, hideDisabledOptions: true },
        format: 'YYYY-MM-DD HH:mm',
      };
    if (model.eruptFieldJson.edit.dateType.type == 'TIME')
      return { valueFormat: 'HH:mm:ss', showTime: true };
    if (model.eruptFieldJson.edit.dateType.type == 'YEAR')
      return { valueFormat: 'YYYY-MM-DD', showTime: false };
  }
  if (model.eruptFieldJson.edit.type == 'REFERENCE_TABLE') {
    const valueField = model.eruptFieldJson.edit.referenceTableType.id;
    const labelField = model.eruptFieldJson.edit.referenceTableType.label;
    return {
      valueField,
      labelField,
      className,
      refClassName: model.fieldName,
    };
  }

  if (model.eruptFieldJson.edit.type == 'REFERENCE_TREE') {
    return {
      className,
      tabName: model.fieldName,
    };
  }
}
/*const openModal = (row,key) => {
  fileUrls.value = row.row[key] ? row.row[key].split('|') : []
  openPreviewModal()
}*/
export function getColFomatter(key, view, edit, slot) {
  const k = view.column ? key + '_' + view.column : key;
  let col = {
    key: k,
    field: k,
    showOverflow: 'title',
    title: view.title,
    sortable: view.sortable,
    width: view.width,
    slots: {
      default: slot
        ? slot
        : (row) => {
            if (view.viewType == 'DATE') {
              return row.row[k] ? formatDate(new Date(row.row[k]), 'YYYY-MM-DD') : '';
            }
            if (view.viewType == 'DATE_TIME') {
              return row.row[k] ? formatDate(new Date(row.row[k]), 'YYYY-MM-DD HH:mm') : '';
            }
            if (view.viewType == 'TIME') {
              return row.row[k] ? formatDate(new Date(row.row[k]), 'HH:MM') : '';
            }
            if (view.viewType == 'YEAR') {
              return row.row[k] ? formatDate(new Date(row.row[k]), 'YYYY') : '';
            }
            if (view.viewType == 'MONTH') {
              return row.row[k] ? formatDate(new Date(row.row[k]), 'MM') : '';
            }
            if (view.viewType == 'TEXT' && edit.inputType && edit.inputType.type == 'color') {
              return row.row[k]
                ? `<div style="background-color:` + row.row[k] + `;width:80px; height: 50px"></div>`
                : '';
            }
            return row.row[k];
          },
      type: k,
    },
  };
  if (view.viewType == 'ATTACHMENT' || view.viewType == 'IMAGE') {
    col.slots = { default: 'upload', type: 'upload' };
  }
  if (view.viewType == 'TEXT' && edit.type == 'TAGS') {
    col.slots = {
      default: 'tags',
      type: 'tags',
      joinSeparator: edit.tagsType ? edit.tagsType.joinSeparator : '|',
    };
  }
  /*if (view.viewType == 'DATE') {
    col.formatter = ({ cellValue }) => {
      return cellValue ? formatDate(new Date(cellValue), 'YYYY-MM-DD') : '';
    };
  }
  if (view.viewType == 'TIME') {
    col.formatter = ({ cellValue }) => {
      return cellValue ? formatDate(new Date(cellValue), 'HH:MM') : '';
    };
  }
  if (view.viewType == 'YEAR') {
    col.formatter = ({ cellValue }) => {
      debugger;
      return cellValue ? formatDate(new Date(cellValue), 'YYYY') : '';
    };
  }
  if (view.viewType == 'MONTH') {
    col.formatter = ({ cellValue }) => {
      return cellValue ? formatDate(new Date(cellValue), 'MM') : '';
    };
  }*/
  /*if (view.viewType == 'TEXT' && edit.inputType && edit.inputType.type == 'color') {
    col.type = 'html';
    col.formatter = ({ cellValue }) => {
      return cellValue
        ? `<div style="background-color:` + cellValue + `;width:80px; height: 50px"></div>`
        : '';
    };
  }*/
  return col;
}

export function getColFomatterTab(key, view, edit) {
  const k = view.column ? key + '_' + view.column : key;
  let col = { key: k, field: k, title: view.title, sortable: view.sortable, width: view.width };
  if (view.viewType == 'ATTACHMENT' || view.viewType == 'IMAGE') {
    col.slots = { default: 'upload' };
  }
  if (view.viewType == 'DATE') {
    col.formatter = ({ cellValue }) => {
      return cellValue ? formatDate(new Date(cellValue), 'YYYY-MM-DD') : '';
    };
  }
  if (view.viewType == 'TIME') {
    col.formatter = ({ cellValue }) => {
      return cellValue ? formatDate(new Date(cellValue), 'HH:MM') : '';
    };
  }
  if (view.viewType == 'BOOLEAN') {
    const boolType = edit.boolType;
    col.formatter = ({ cellValue }) => {
      return cellValue ? boolType.trueText : boolType.falseText;
    };
  }

  if (view.viewType == 'TEXT' && edit.inputType && edit.inputType.type == 'color') {
    col.type = 'html';
    col.formatter = ({ cellValue }) => {
      return cellValue
        ? `<div style="background-color:` + cellValue + `;width:80px; height: 50px"></div>`
        : '';
    };
  }
  return col;
}

export const getUpLoadApi = (className, filedName) => {
  return (params: UploadFileParams, onUploadProgress: (progressEvent: ProgressEvent) => void) => {
    return defHttp.uploadFile<UploadApiResult>(
      {
        url: `${globSetting.eruptUrl}/${Api.ERUPT_UPLOAD}${className}/${filedName}`,
        onUploadProgress,
      },
      params,
    );
  };
};

/*const refTreeApi = (className, filedName) => {
  console.log(className,filedName)
  return refBuildTreeApi(className,filedName)
}*/

export function checkEmpty(list) {
  return list.size == 0;
}
