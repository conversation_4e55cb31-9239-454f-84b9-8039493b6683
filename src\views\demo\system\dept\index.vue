<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增部门 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
              {
                icon: 'ant-design:eye-outlined',
                color: 'warning',
                popConfirm: {
                  title: '是否确认启用',
                  placement: 'left',
                  confirm: handleFlag.bind(null, record),
                },
              }
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <DeptModal @register="registerModal" @success="handleSuccess" />
    <!--<erupt-table class-name="Article"></erupt-table>-->
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getDeptList, setDeptFlag } from "/@/api/demo/system";
  import eruptTable from '/@/components/EruptTable/eruptTable';
  import { useModal } from '/@/components/Modal';
  import DeptModal from './DeptModal.vue';
  import VxeTable from 'vxe-table';
  import { columns, searchFormSchema } from './dept.data';
  import { useMessage } from "/@/hooks/web/useMessage";

  export default defineComponent({
    name: 'DeptManagement',
    components: { BasicTable, DeptModal, TableAction, eruptTable },
    setup() {
      const { notification } = useMessage();
      const [registerModal, { openModal }] = useModal();
      const [registerTable, { reload }] = useTable({
        title: '部门列表',
        api: getDeptList,
        columns,
        formConfig: {
          labelWidth: 120,
          schemas: searchFormSchema,
        },
        pagination: false,
        striped: false,
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        canResize: false,
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
          // slots: { customRender: 'action' },
          fixed: undefined,
        },
      });

      function handleCreate() {
        openModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        openModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
      }

      const handleFlag = (record: Recordable) => {
        console.log(record);
        const { id } = record;
        setDeptFlag(id).then(() => {
          notification.success({
            message: '启用部门',
            description: '启用部门成功',
          });
          reload();
        });
      }

      function handleSuccess() {
        reload();
      }

      return {
        registerTable,
        registerModal,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        handleFlag
      };
    },
  });
</script>
