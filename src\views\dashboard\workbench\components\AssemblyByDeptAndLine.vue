<template>
  <Card title="运营汇编单线路/责任单位完成率" :loading="loading">
    <template #extra>
      <a-space direction="horizontal">
        <a-button type="primary" @click="showModal">详情</a-button>
      </a-space>
    </template>
    <div v-show="isShow > 0" v-loading="loadings" ref="chartRef" :style="{ width, height }"></div>
    <div v-show="isShow <= 0" style="text-align: center; margin-top: 100px; height: 300px">
      <a-empty />
    </div>
    <a-modal
      v-model:visible="visible"
      title="图表详情"
      @ok="handleOk"
      :width="'85%'"
      :style="{ top: '100px', left: '0', right: '0', margin: '0 auto' }"
    >
      <div ref="modalChartRef" :style="{ width: '100%', height: '650px' }"></div>
    </a-modal>
  </Card>
</template>

<script setup lang="ts">
  import { <PERSON>, Modal, Button } from 'ant-design-vue';
  import { Ref, ref, watch } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { useUserStore } from '/@/store/modules/user';
  import { getAssemblyByDeptAndLine } from '/@/api/demo/home';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    loading: Boolean,
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '400px',
    },
    lineOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    deptOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });
  const line = ref('');
  const isShow = ref(0);
  const dept = ref('');
  const deptLabel = ref('');
  const loadings = ref(true);
  const chartRef = ref<HTMLDivElement | null>(null);
  const modalChartRef = ref<HTMLDivElement | null>(null);
  const { setOptions: setMainChartOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const { setOptions: setModalChartOptions } = useECharts(modalChartRef as Ref<HTMLDivElement>);
  const userStore = useUserStore();
  const router = useRouter();
  const visible = ref(false);
  let chartData: any = null;
  const status = ref('0'); // 添加 status 引用

  const setLine = (val) => {
    loadings.value = true;
    line.value = val;
    getChartDate();
  };
  const setDept = (val) => {
    loadings.value = true;
    const selectedOption = props.deptOpt.find((option) => option.value === val);
    if (selectedOption) {
      deptLabel.value = selectedOption.label;
    }
    dept.value = val;
    getChartDate();
  };

  watch(
    () => props.loading,
    () => {
      if (props.loading) {
        return;
      }
      getChartDate();
    },
    { immediate: true },
  );

  watch(
    () => visible.value,
    (newVal) => {
      if (newVal && chartData) {
        updateModalChart(chartData);
      }
    },
  );

  const getChartDate = async () => {
    const data = {
      line: line.value,
      dept: dept.value,
      status: status.value, // 使用 status 的值
    };
    try {
      const res = await getAssemblyByDeptAndLine(data);
      chartData = res; // 保存数据以便在弹窗显示时使用
      if (data.status === '0') {
        updateChart(res);
      } else {
        updateModalChart(res);
      }
      isShow.value = res.name.length - 1;
    } catch (error) {
      console.log(error);
    } finally {
      loadings.value = false; // 确保在请求结束后将 loadings 设置为 false
    }
  };

  function updateChart(value) {
    // 处理 value.name，截取最后一个 _ 后的字符串作为颜色，并去掉 _ 和其后的字符串
    const processedName = value.name.map((item) => {
      const parts = item.split('_');
      let colorPart = '#ED868C'; // 默认颜色
      let displayPart = item;
      if (parts.length > 1) {
        colorPart = parts.pop(); // 截取最后一个 _ 后的字符串作为颜色
        displayPart = parts.join('_'); // 去掉 _ 和其后的字符串
      }
      return { display: displayPart, color: colorPart };
    });

    setMainChartOptions({
      xAxis: {
        type: 'category',
        data: processedName.map((item) => item.display), // 使用处理后的 display 部分
        name: '责任单位/线路',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#000',
          fontSize: 14,
        },
        axisLabel: {
          interval: 0, // 显示所有标签
          rotate: 0, // 取消旋转
          overflow: 'break', // 换行显示
          formatter: function (value) {
            const lines = [];
            for (let i = 0; i < value.length; i += 5) {
              lines.push(value.slice(i, i + 5));
            }
            return lines.join('\n');
          },
        },
      },
      yAxis: {
        name: '完成率', // 设置y轴名称
        nameLocation: 'end', // 名称显示的位置
        nameGap: 21, // 调整名称与轴线之间的距离
        nameTextStyle: {
          color: '#333',
          fontSize: 14,
          fontWeight: 'bold',
          backgroundColor: '#f8f8f8', // 添加背景色
          borderColor: '#d9d9d9', // 边框颜色
          borderWidth: 1, // 边框宽度
          borderRadius: 4, // 圆角边框
          padding: [4, 8], // 内边距
        },
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
        axisLabel: {
          formatter: function (value) {
            return value + '%';
          },
        },
      },
      grid: {
        left: '10%', // 左边距
        right: '12%', // 右边距
        top: '10%',
        bottom: '15%',
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          const index = params.dataIndex;
          const name = processedName[index].display;
          const rate = value.data[index].toFixed(2);
          // 获取完成数量和总数量
          const completed = value.completed ? value.completed[index] : 0;
          const total = value.count ? value.count[index] : 0;

          return `
            ${name}<br/>
            完成率: ${rate}%<br/>
            完成数量: ${completed}<br/>
            总数量: ${total}
          `;
        },
      },
      series: [
        {
          name: '完成率',
          type: 'bar', // 显示柱状图
          data: value.data, // 根据选中的线路获取数据
          barWidth: '30%',
          itemStyle: {
            color: function (params) {
              // 使用处理后的 color 部分
              return processedName[params.dataIndex]?.color || '#5793f3';
            },
          },
          label: {
            show: true,
            position: 'top', // 或者其他位置如：'inside', 'right'
            formatter: function (params) {
              return params.value.toFixed(2) + '%';
            }, // 显示数值并保留两位小数
            color: '#000', // 文本颜色
            fontSize: 14, // 字体大小
          },
        },
      ],
    });
  }

  function updateModalChart(value) {
    const chartHeight = 650; // 模态框固定高度 650px
    const itemHeight = 50; // 每个条目的固定高度
    const visibleCount = Math.floor(chartHeight / itemHeight);

    // 处理 value.name，截取最后一个 _ 后的字符串作为颜色，并去掉 _ 和其后的字符串
    const processedName = value.name.map((item) => {
      const parts = item.split('_');
      let colorPart = '#ED868C'; // 默认颜色
      let displayPart = item;
      if (parts.length > 1) {
        colorPart = parts.pop(); // 截取最后一个 _ 后的字符串作为颜色
        displayPart = parts.join('_'); // 去掉 _ 和其后的字符串
      }
      return { display: displayPart, color: colorPart };
    });

    setModalChartOptions({
      xAxis: {
        type: 'value',
        name: '完成率',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          color: '#000',
          fontSize: 14,
        },
        axisLabel: {
          formatter: function (value) {
            return value + '%';
          },
        },
      },
      yAxis: {
        type: 'category',
        data: processedName.map((item) => item.display), // 使用处理后的 display 部分
        name: '责任单位/线路',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#000',
          fontSize: 14,
        },
        axisLabel: {
          interval: 0, // 显示所有标签
          rotate: 0, // 取消旋转
          overflow: 'break', // 换行显示
          formatter: function (value) {
            const lines = [];
            for (let i = 0; i < value.length; i += 8) {
              lines.push(value.slice(i, i + 8));
            }
            return lines.join('\n');
          },
        },
        inverse: true, // 设置 inverse 为 true，使条目从上往下排列
      },
      grid: {
        left: '5%',
        right: '12%',
        top: '10%',
        bottom: '10%',
        containLabel: true, // 确保标签完整显示
      },
      dataZoom: [
        {
          type: 'slider', // 滑动条类型
          orient: 'vertical', // 垂直方向
          yAxisIndex: 0, // 关联的 y 轴索引
          show: true, // 显示滑动条
          zoomLock: true, // 不锁定缩放比例
          start: 0, // 初始显示的起始百分比
          end:
            value.name.length <= visibleCount
              ? 100
              : Math.min(100, (visibleCount / value.name.length) * 100), // 初始显示的结束百分比
          minValueSpan: 13, // 最小显示的数据点数量
          maxValueSpan: visibleCount, // 最大显示的数据点数量
          filterMode: 'empty', // 过滤模式
          // fillerColor: 'rgba(27,90,169,1)', // 选中区域颜色
          borderColor: 'transparent', // 边框颜色
          left: '93%', // 滑动条位置，可以根据需要调整
          width: 10, // 滑动条宽度
          handleSize: '100%', // 滑动条手柄大小
          handleStyle: {
            // color: 'rgba(27,90,169,1)', // 滑动条手柄颜色
            borderWidth: 0, // 滑动条手柄边框宽度
          },
          // backgroundColor: 'rgba(37, 46, 100, 0.8)', // 未选中区域颜色
          showDataShadow: false, // 是否显示数据阴影
          showDetail: false, // 是否显示详细数值信息
        },
        {
          type: 'inside', // 内置缩放
          orient: 'vertical', // 垂直方向
          yAxisIndex: 0, // 关联的 y 轴索引
          zoomOnMouseWheel: false, // 允许鼠标滚轮缩放
          moveOnMouseMove: true, // 允许鼠标移动平移
          moveOnMouseWheel: true, // 允许鼠标滚轮平移
          start: 0, // 初始显示的起始百分比
          end:
            value.name.length <= visibleCount
              ? 100
              : Math.min(100, (visibleCount / value.name.length) * 100), // 初始显示的结束百分比
        },
      ],
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          const index = params.dataIndex;
          const name = processedName[index].display;
          const rate = value.data[index].toFixed(2);
          // 获取完成数量和总数量
          const completed = value.completed ? value.completed[index] : 0;
          const total = value.count ? value.count[index] : 0;

          return `
            ${name}<br/>
            完成率: ${rate}%<br/>
            完成数量: ${completed}<br/>
            总数量: ${total}
          `;
        },
      },
      series: [
        {
          name: '完成率',
          type: 'bar',
          data: value.data,
          barWidth: '30%',
          itemStyle: {
            color: function (params) {
              // 使用处理后的 color 部分
              return processedName[params.dataIndex]?.color || '#5793f3';
            },
          },
          label: {
            show: true,
            position: 'right',
            formatter: function (params) {
              return params.value.toFixed(2) + '%';
            },
            color: '#000',
            fontSize: 14,
          },
        },
      ],
      // 性能优化配置（可选）
      animation: value.name.length < 100, // 数据量较大时关闭动画
      progressive: value.name.length > 300 ? 10 : 0, // 大数据量时分片渲染
    });
  }

  const showModal = () => {
    status.value = '1'; // 设置 status 为 '1'
    loadings.value = true; // 设置 loadings 为 true
    getChartDate().then(() => {
      visible.value = true; // 在请求成功后显示弹窗
    });
  };

  const handleOk = () => {
    visible.value = false;
    status.value = '0'; // 设置 status 为 '0'
    getChartDate();
  };
</script>
