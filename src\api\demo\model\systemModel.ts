import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export type AccountParams = BasicPageParams & {
  account?: string;
  nickname?: string;
};

export type RoleParams = {
  roleName?: string;
  status?: string;
};

export type RolePageParams = BasicPageParams & RoleParams;

export type DeptParams = {
  deptName?: string;
  status?: string;
};

export type PersonParams = {
  personName?: string;
  personNo?: string;
};

export interface AccountListItem {
  id: string;
  account: string;
  email: string;
  phone: string;
  nickname: string;
  role: number;
  createTime: string;
  remark: string;
  status: number;
}

export interface DeptListItem {
  id: string;
  deptName: string;
  orderNo: string;
  createTime: string;
  remark: string;
  status: number;
}

export interface PersonListItem {
  id: string;
  name: string;
  no: string;
  gender: string;
  dimission: string;
  title: string;
  headImageUrl: string;
}

export interface RoleListItem {
  id: string;
  roleName: string;
  roleValue: string;
  status: number;
  orderNo: string;
  createTime: string;
}

export interface RoleInfo {
  id: string;
  roleName: string;
  roleValue: string;
  status: number;
  orderNo: string;
  createTime: string;
}

export interface AccountInfo {
  id: string;
  account: string;
  email: string;
  phone: string;
  nickname: string;
  roleId: number;
  createTime: string;
  remark: string;
  status: number;
}

export interface UserModel {
  id: number;
  username: string;
  loginAllowed: string;
  avatar: string;
  intro: string;
  personName: string;
  sysClientList: string;
}

export interface MenuListItem {
  name: string;
  path: string;
  title: string;
  icon: string;
  orderNo: number;
  component: string;
  key: string;
}

/**
 * @description: Request list return value
 */
export type AccountListGetResultModel = BasicFetchResult<AccountListItem>;

export type DeptListGetResultModel = BasicFetchResult<DeptListItem>;

export type PersonListGetResultModel = BasicFetchResult<PersonListItem>;

export type RolePageListGetResultModel = BasicFetchResult<RoleListItem>;

export type RoleListGetResultModel = RoleListItem[];

export type MenuListGetResultModel = MenuListItem[];
