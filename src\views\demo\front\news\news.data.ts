import { BasicColumn, FormSchema } from '/@/components/Table';
import { newsCategoryOptionsListApi } from '/@/api/demo/OptionsSelect';
import { Tinymce } from '/@/components/Tinymce/index';
import { h } from 'vue';

export const columns: BasicColumn[] = [
  {
    title: '新闻标题',
    dataIndex: 'title',
    width: 300,
  },
  {
    title: '撰稿人',
    dataIndex: 'provider',
    width: 200,
  },
  {
    title: '新闻栏目',
    dataIndex: 'categoryName',
    width: 200,
  },
  {
    title: '发布日期',
    dataIndex: 'newsDate',
    width: 200,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'title',
    label: '新闻标题',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'newsNumber',
    label: '新闻编号',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    required: false,
    component: 'Input',
    show: false,
  },
  {
    field: 'title',
    label: '新闻标题',
    required: true,
    component: 'Input',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'categoryId',
    component: 'ApiSelect',
    label: '新闻栏目',
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      api: newsCategoryOptionsListApi,
      resultField: 'list',
      // use name as label
      labelField: 'label',
      // use id as value
      valueField: 'value',
      // not request untill to select
      placeholder: '请选择新闻栏目',
    },
  },
  {
    field: 'newsDate',
    label: '新闻日期',
    required: true,
    component: 'DatePicker',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'provider',
    label: '撰稿部门和人',
    required: true,
    component: 'Input',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'visibility',
    label: '草稿',
    required: true,
    component: 'RadioGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '是', value: 0 },
        { label: '否', value: 1 },
      ],
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'note',
    label: '备注',
    required: false,
    component: 'Input',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'content',
    label: '新闻内容',
    required: false,
    component: 'Input',
    render: ({ model, field }) => {
      return h(Tinymce, {
        value: model[field],
        onChange: (value: string) => {
          model[field] = value;
        },
      });
    },
  },
];
