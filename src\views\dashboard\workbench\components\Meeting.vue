<script setup lang="ts">
  import { Card, Table, Modal, Descriptions, Tag, Divider, Empty } from 'ant-design-vue';
  import { ref, onMounted, h } from 'vue';
  import { useRouter } from 'vue-router';
  import { ScheduleOutlined } from '@ant-design/icons-vue';
  import {
    getItemsApi,
    getItemDetailsApi,
    getAnnualMeetingCountApi,
  } from '/@/views/dashboard/workbench/components/api/meeting';
  import { useUserStore } from '/@/store/modules/user';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Base64 } from 'js-base64';

  interface MeetingItem {
    id?: number;
    title: string;
    startMeetingDate: string;
    location: string;
    meetingNumber: string;
    lineId: string;
    content: string;
    meetingCategory: string;
    participants?: string[];
    attachment?: string;
    attachment1?: string;
  }

  const loading = ref(true);
  const listData = ref<MeetingItem[]>([]);
  const annualMeetingCount = ref<number>(0);
  const router = useRouter();
  const userStore = useUserStore();
  const globSetting = useGlobSetting();
  const eruptAttachment = globSetting?.eruptAttachment;

  // 预览相关
  const previewUrl = ref('');
  const previewKey = ref(0);
  const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();

  // 附件弹窗相关
  const attachmentModalVisible = ref(false);
  const currentAttachments = ref<{ name: string; url: string }[]>([]);
  const currentAttachmentType = ref('');

  const previewShow = (downloadUrl: string, index: string | number) => {
    const fullUrl = globSetting.eruptAttachment + downloadUrl;
    const encodedUrl = encodeURIComponent(Base64.encode(fullUrl));
    const watermark = `${userStore.getUserInfo.username} ${userStore.getUserInfo.realName}`;

    previewUrl.value = `${
      globSetting.previewUrl
    }/onlinePreview?url=${encodedUrl}&watermarkTxt=${encodeURIComponent(watermark)}`;
    previewKey.value = +new Date() + index;
    openPreviewModal(true);
  };

  const showAttachmentsModal = (record: MeetingItem, type: 'attachment' | 'attachment1') => {
    const files = (record[type] || '').split('|').filter(Boolean);
    if (files.length === 0) return;

    currentAttachments.value = files.map((file) => ({
      name: file.substring(file.lastIndexOf('/') + 1),
      url: file,
    }));
    currentAttachmentType.value = type === 'attachment' ? '会议结果附件' : '会议资料';
    attachmentModalVisible.value = true;
  };

  const columns = [
    {
      title: '会议内容',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      width: 120,
      customRender: ({ text }: { text: string }) => {
        return h('a-tooltip', { title: text }, h('span', { class: 'ellipsis-text' }, text));
      },
    },
    {
      title: '会议时间',
      dataIndex: 'startMeetingDate',
      key: 'time',
      width: 180,
      customRender: ({ text }: { text: string }) => {
        return h('a-tooltip', { title: text }, h('span', { class: 'ellipsis-text' }, text));
      },
    },
    {
      title: '会议地点',
      dataIndex: 'location',
      key: 'location',
      width: 150,
      customRender: ({ text }: { text: string }) => {
        return h('a-tooltip', { title: text }, h('span', { class: 'ellipsis-text' }, text));
      },
    },
    {
      title: '所属线路',
      dataIndex: 'lineId',
      key: 'line',
      width: 120,
    },
    {
      title: '会议资料',
      key: 'attachment1',
      width: 120,
      customRender: ({ record }: { record: MeetingItem }) => {
        const count = (record.attachment1 || '').split('|').filter(Boolean).length;
        return count > 0
          ? h(
              'a',
              { onClick: () => showAttachmentsModal(record, 'attachment1') },
              `查看资料（${count}个）`,
            )
          : h('span', { class: 'no-data' }, '暂无资料');
      },
    },
    {
      title: '会议类别',
      dataIndex: 'meetingCategory',
      key: 'meetingCategory',
      width: 160,
    },
    {
      title: '会议人数',
      dataIndex: 'meetingNumber',
      key: 'meetingNumber',
      width: 100,
      customRender: ({ text }: { text: string }) => {
        return h('a-tooltip', { title: text }, h('span', { class: 'ellipsis-text' }, text));
      },
    },
    {
      title: '联络人',
      key: 'participants',
      width: 250,
      customRender: ({ record }: { record: MeetingItem }) => {
        const names = record.participants?.join(', ') || '暂无联络人';
        return h('a-tooltip', { title: names }, h('span', { class: 'ellipsis-text' }, names));
      },
    },
    {
      title: '会议结果附件',
      key: 'attachment',
      width: 120,
      customRender: ({ record }: { record: MeetingItem }) => {
        const count = (record.attachment || '').split('|').filter(Boolean).length;
        return count > 0
          ? h(
              'a',
              { onClick: () => showAttachmentsModal(record, 'attachment') },
              `查看附件（${count}个）`,
            )
          : h('span', { class: 'no-data' }, '暂无附件');
      },
    },
  ];

  const fetchData = async () => {
    try {
      const userId = userStore.getUserInfo.userId;
      const [weeklyMeetings, annualCount] = await Promise.all([
        getItemsApi(userId).then((res) =>
          res.map((item) => ({
            ...item,
            participants: item.participants || [],
          })),
        ),
        getAnnualMeetingCountApi(),
      ]);
      listData.value = weeklyMeetings;
      annualMeetingCount.value = annualCount.annualMeetingCount;
      loading.value = false;
    } catch (error) {
      console.error('Failed to fetch data:', error);
      loading.value = false;
    }
  };

  onMounted(() => {
    fetchData();
  });
</script>

<template>
  <BasicModal
    @register="registerPreviewModal"
    title="预览文件"
    :width="1000"
    :height="600"
    :footer="null"
  >
    <iframe :key="previewKey" :src="previewUrl" width="1000" height="800"></iframe>
  </BasicModal>
  <Modal
    v-model:visible="attachmentModalVisible"
    :title="currentAttachmentType"
    width="600px"
    :footer="null"
    destroyOnClose
  >
    <div class="attachment-list">
      <template v-if="currentAttachments.length > 0">
        <div v-for="(file, index) in currentAttachments" :key="index" class="attachment-item">
          <div class="file-info">
            <a-tooltip :title="file.name">
              <span class="file-name">{{ file.name }}</span>
            </a-tooltip>
            <div class="actions">
              <a-button type="link" size="small" @click="previewShow(file.url, index)">
                预览
              </a-button>
              <a-button type="link" size="small">
                <a
                  :href="`${eruptAttachment}${file.url}`"
                  :download="file.name"
                  class="download-link"
                >
                  下载
                </a>
              </a-button>
            </div>
          </div>
          <a-divider v-if="index < currentAttachments.length - 1" />
        </div>
      </template>
      <Empty v-else description="暂无附件" image-style="height: 40px" />
    </div>
  </Modal>

  <Card :loading="loading" class="table-card">
    <template #title>
      <div class="card-header">
        <span class="card-title">本周会议列表</span>
        <a-tag color="blue" class="annual-tag">
          <ScheduleOutlined />
          年度会议数量: {{ annualMeetingCount }}次
        </a-tag>
      </div>
    </template>

    <Table
      :data-source="listData"
      :columns="columns"
      :pagination="false"
      size="middle"
      class="meeting-table"
      bordered
    >
      <template #emptyText>
        <Empty image-style="height: 60px" description="暂无会议数据" />
      </template>
    </Table>
  </Card>
</template>

<style scoped lang="less">
  .attachment-list {
    max-height: 60vh;
    overflow-y: auto;
    padding: 8px;

    .attachment-item {
      padding: 8px 0;
      transition: background-color 0.3s;

      &:hover {
        background-color: #fafafa;
      }

      .file-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 8px;

        .file-name {
          flex: 1;
          margin-right: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .actions {
          flex-shrink: 0;
          display: flex;
          gap: 8px;

          :deep(.ant-btn-link) {
            padding: 0;
            height: auto;

            &::after {
              display: none;
            }
          }

          .download-link {
            color: #ff7d4f;

            &:hover {
              color: #ff6118;
            }
          }
        }
      }

      :deep(.ant-divider) {
        margin: 12px 0;
      }
    }
  }

  .table-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;

    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }

    .annual-tag {
      margin-left: auto;
      background: #f0f9ff;
      border-color: #91d5ff;
      color: #1890ff;
      border-radius: 4px;
      padding: 4px 10px;

      :deep(.anticon) {
        color: #1890ff;
        margin-right: 6px;
      }
    }
  }

  .meeting-table {
    :deep(.ant-table-container) {
      border-radius: 4px;
    }

    :deep(.ant-table-thead > tr > th) {
      background: #fafafa;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }

    :deep(.ant-table-tbody > tr > td) {
      transition: background 0.3s;
    }

    :deep(.ant-table-tbody > tr:hover > td) {
      background: #e6f7ff !important;
      cursor: pointer;
    }

    .no-data {
      color: #8b949e;
      font-style: italic;
    }
  }
</style>
