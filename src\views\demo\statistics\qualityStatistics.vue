<template>
  <Card title="质保管理" :loading="loading">
    <template #extra>
      <a-space direction="horizontal">
        <!--  <a-select
           v-model="line"
           style="width: 200px"
           placeholder="请选择线路"
           :options="lineOptions"
           @change="setLine"
           :loading="loading"
           :disabled="loading"
           allowClear
         ></a-select>-->
        <a-select
          v-if="userStore.getUserInfo.dept === '新线管理部'"
          v-model="dept"
          style="width: 200px"
          placeholder="请选择部门"
          :options="deptOptions"
          @change="setDept"
          :loading="loading"
          :disabled="loading"
          allowClear
        ></a-select>
      </a-space>
    </template>

    <!-- 柱状图容器 -->
    <div v-loading="chartLoading" ref="chartRef" :style="{ width: '100%', height: '400px' }"></div>

    <!-- 数据表格 -->
    <div v-loading="tableLoading" style="margin-top: 20px">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        bordered
        class="custom-striped-table"
      >
        <template #footer>
          合计：待启动 {{ total.unresolved || 0 }} / 未完成 {{ total.resolved || 0 }} / 已完成
          {{ total.feedback || 0 }} / 总数 {{ total.total1 || 0 }}
        </template>
      </a-table>
    </div>
  </Card>
</template>

<script setup lang="ts">
  import { Card } from 'ant-design-vue';
  import { onMounted, Ref, ref } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { useUserStore } from '/@/store/modules/user';
  import { getQualityStats, getQualityTable } from '/@/views/demo/statistics/api/statistics';
  import { useLineStoreWithOut } from '/@/store/modules/line';
  import { useDeptStoreWithOut } from '/@/store/modules/dept';

  // 类型定义
  interface ChartData {
    labels: string[];
    values: number[];
  }

  interface TableData {
    dept: string;
    unresolved: number;
    resolved: number;
    feedback: number;
    total1: number;
  }

  interface SelectOption {
    label: string;
    value: string | number;
  }

  // 响应式状态
  const line = ref('');
  const dept = ref('');
  const deptLabel = ref('');
  const lineOptions = ref<SelectOption[]>([]);
  const deptOptions = ref<SelectOption[]>([]);
  const loading = ref(true);
  const chartLoading = ref(true);
  const tableLoading = ref(true);
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const userStore = useUserStore();
  const lineStore = useLineStoreWithOut();
  const deptStore = useDeptStoreWithOut();

  // 表格列定义
  const columns = [
    {
      title: '部门',
      dataIndex: 'dept',
      key: 'dept',
      width: 150,
    },
    {
      title: '待启动',
      dataIndex: 'unresolved',
      key: 'unresolved',
      width: 150,
    },
    {
      title: '未完成',
      dataIndex: 'resolved',
      key: 'resolved',
      width: 150,
    },
    {
      title: '已完成',
      dataIndex: 'feedback',
      key: 'feedback',
      width: 150,
    },
    {
      title: '总数',
      dataIndex: 'total1',
      key: 'total1',
      width: 150,
    },
  ];

  const tableData = ref<TableData[]>([]);
  const total = ref({
    unresolved: 0,
    resolved: 0,
    feedback: 0,
    total1: 0,
  });

  // 处理筛选条件变化
  const handleFilterChange = () => {
    chartLoading.value = true;
    tableLoading.value = true;
    fetchChartData();
    fetchTableData();
  };

  // 获取柱状图数据
  const fetchChartData = async () => {
    try {
      const params = {
        line: line.value,
        dept: dept.value,
      };
      const res = await getQualityStats(params);
      if (res && res.labels && res.values) {
        updateChart(res);
      } else {
        console.error('柱状图数据格式不正确:', res);
        // 显示空数据状态
        updateChart({
          labels: ['待启动', '未完成', '已完成'],
          values: [0, 0, 0],
        });
      }
    } catch (error) {
      console.error('柱状图数据获取失败:', error);
    } finally {
      chartLoading.value = false;
    }
  };

  // 获取表格数据
  const fetchTableData = async () => {
    try {
      const params = {
        line: line.value,
        dept: dept.value,
      };
      const res = await getQualityTable(params);
      if (res && res.list && res.total) {
        updateTable(res);
      } else {
        console.error('表格数据格式不正确:', res);
        // 显示空数据状态
        updateTable({
          list: [],
          total: {
            unresolved: 0,
            resolved: 0,
            feedback: 0,
            total1: 0,
          },
        });
      }
    } catch (error) {
      console.error('表格数据获取失败:', error);
    } finally {
      tableLoading.value = false;
    }
  };

  // 更新柱状图
  function updateChart(value: ChartData) {
    setOptions({
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br/>{a}: {c}',
      },
      xAxis: {
        type: 'category',
        data: value.labels,
        name: '状态',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#000',
          fontSize: 14,
        },
        axisLabel: {
          interval: 0,
        },
      },
      yAxis: {
        type: 'value',
        name: '数量',
        nameLocation: 'end',
        nameGap: 21,
        nameTextStyle: {
          color: '#333',
          fontSize: 14,
          fontWeight: 'bold',
          backgroundColor: '#f8f8f8', // 添加背景色
          borderColor: '#d9d9d9', // 边框颜色
          borderWidth: 1, // 边框宽度
          borderRadius: 4, // 圆角边框
          padding: [4, 8], // 内边距
        },
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '15%',
        bottom: '10%',
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          data: value.values,
          barWidth: '30%',
          itemStyle: {
            color: function (params) {
              const colorList = ['#FF9F1C', '#FF4040', '#2EC4B6'];
              return colorList[params.dataIndex];
            },
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c}',
            color: '#000',
            fontSize: 14,
          },
        },
      ],
    });
  }

  // 更新表格数据
  function updateTable(value: { list: TableData[]; total: any }) {
    tableData.value = (value.list || []).map((item) => ({
      ...item,
      dept:
        deptOptions.value.find((opt) => String(opt.value) === String(item.dept))?.label ||
        `未知部门(${item.dept})`,
    }));
    total.value = {
      unresolved: Number(value.total?.unresolved) || 0,
      resolved: Number(value.total?.resolved) || 0,
      feedback: Number(value.total?.feedback) || 0,
      total1: Number(value.total?.total1) || 0,
    };
  }

  // 设置线路
  const setLine = (val: string) => {
    line.value = val;
    handleFilterChange();
  };

  // 设置部门
  const setDept = (val: string) => {
    const selectedOption = deptOptions.value.find((option) => option.value === val);
    if (selectedOption) {
      deptLabel.value = selectedOption.label;
    }
    dept.value = val;
    handleFilterChange();
  };

  // 初始化加载数据
  onMounted(() => {
    Promise.all([deptStore.getDeptOptions(), lineStore.getLineOptions()])
      .then(([deptRes, lineRes]) => {
        deptOptions.value = deptRes;
        lineOptions.value = lineRes;
        loading.value = false;
        // 初始加载数据
        handleFilterChange();
      })
      .catch((error) => {
        console.error('初始化选项失败:', error);
        loading.value = false;
      });
  });
</script>

<style scoped>
  .custom-striped-table :deep(.ant-table-tbody) tr:nth-child(even) {
    background-color: #fafafa;
  }
</style>
