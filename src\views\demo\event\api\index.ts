import { defHttp } from '/@/utils/http/axios';

export enum Api {
  GETALL = '/memorabilia/getAll',
  GETDATA = '/memorabilia/getData',
  GETDEVALL = '/developmemorabilia/getAll',
  GETBYLINE = '/linememorabilia/getByLine/',
  GETDEPTPERSON = '/deptEvent/getTopPerson/',
  GETDEPTEVENT = '/deptEvent/getDeptEvent/',
  GETDEPTTARGET = '/deptEvent/getDeptTarget/',
  GETDEPTGOODPERSON = '/deptEvent/getDeptGoodPerson/',
  GETDEPTPICTURE = '/deptEvent/getDeptPicture/',
  GETCAROUSELPIC = '/deptEvent/getCarouselPic/',
}

export const getAllApi = () => defHttp.get({ url: Api.GETALL });

export const getDataApi = () => defHttp.get({ url: Api.GETDATA });
export const getDevAllApi = () => defHttp.get({ url: Api.GETDEVALL });

export const getLineAllApi = (lineId) => defHttp.get({ url: Api.GETBYLINE + lineId });

export const getDeptPersonApi = () => defHttp.get({ url: Api.GETDEPTPERSON });

export const getDeptEventApi = () => defHttp.get({ url: Api.GETDEPTEVENT });

export const getDeptTargetApi = (year) => defHttp.get({ url: Api.GETDEPTTARGET + year });

export const getDeptGoodPersonApi = () => defHttp.get({ url: Api.GETDEPTGOODPERSON });

export const getDeptPictureApi = () => defHttp.get({ url: Api.GETDEPTPICTURE });

export const getCarouselPicApi = () => defHttp.get({ url: Api.GETCAROUSELPIC });
