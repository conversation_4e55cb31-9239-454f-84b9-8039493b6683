<template>
  <div style="height: 900px">
    <VxeBasicTable ref="tableRef" v-bind="gridOptions">
      <template #action="{ row }">
        <TableAction outside :actions="createActions(row)" />
      </template>
    </VxeBasicTable>
    <BasicModal :showCancelBtn="false" :showOkBtn="false" :defaultFullscreen="true" :width="900" v-bind="$attrs" @register="register" title="流程图">
     <BpmnViewer :xml-text="xmlText" style="height: 1000px"></BpmnViewer>
    </BasicModal>
  </div>
</template>

<script lang="ts">
import {defineComponent, PropType, ref, onMounted, watchEffect, computed, unref, watch, h} from 'vue';
import {VXETable, VxeTableInstance} from 'vxe-table'
import BpmnViewer from '/@/views/process/bpmn/index1.vue'
import {useMessage} from "/@/hooks/web/useMessage";
import BasicDrawer from "/@/components/Drawer/src/BasicDrawer.vue";
import BasicForm from "/@/components/Form/src/BasicForm.vue";
import {deploy, moduleListApi, read} from "/@/api/process/process";
import {ActionItem, TableAction} from "/@/components/Table";
import {BasicTableProps, VxeBasicTable} from "/@/components/VxeTable";
import { useRouter} from "vue-router";
import {Modal, Tag, Tooltip} from "ant-design-vue";
import {BasicModal, useModal} from "/@/components/Modal";

export default defineComponent({
  components: {
    Tooltip,
    BpmnViewer,
    Tag,
    BasicModal,
    VxeBasicTable, TableAction, BasicForm, BasicDrawer, VXETable},
  setup(props, {attrs, emit}) {
    const {createMessage} = useMessage();
    const [register, {openModal, closeModal}] = useModal();
    const router = useRouter();
    const xmlText =  ref()
    const gridOptions = ref<BasicTableProps>({
      id: 'VxeTable',
      keepSource: true,
      editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
      rowConfig: {isHover: true, useKey: true,isCurrent: false},
      columnConfig: {isHover: true, isCurrent: false},
      columns: [
        {
          title: '序号',
          type: 'seq',
          fixed: 'left',
          width: '50',
          align: 'center',
        },
        {
          title: '名称',
          field: 'name',
        },
        {
          width: 160,
          title: '操作',
          align: 'center',
          slots: { default: 'action' },
          fixed: 'right',
        },],
      toolbarConfig: {
        refresh: true, // 显示刷新按钮
        import: false, // 显示导入按钮
        export: false, // 显示导出按钮
        print: false, // 显示打印按钮
        zoom: false, // 显示全屏按钮
        custom: true
      },
      height: 'auto',
      proxyConfig: {
        ajax: {
          query: async ({ page, form }) => {
            const bpmns = await moduleListApi()
            return bpmns
          },
          queryAll: async ({ form }) => {
            const bpmns = await moduleListApi()
            let rows = []
            bpmns.forEach(bpmn => {
              rows.push({name: bpmn.split('.bpmn')[0]})
            })
            return rows;
          },
        },
      },
    });

    interface RowVO {
      name: string
    }


    const tableData = ref<Array<RowVO>>([])

    /*onMounted(() => {
      moduleListApi().then(res => {
        const bpmns = res
        let rows = []
        bpmns.forEach(bpmn => {
          rows.push({name: bpmn.split('.bpmn')[0]})
        })
        tableData.value = rows
      })
    })*/

    return {
      tableData,
      gridOptions,
      register,
      openModal,
      closeModal,
      xmlText,
      createActions :(record) => {
        const actions: ActionItem[] = [
          {
            label: '部署',
            popConfirm:{
              title: "是否确认部署",
              confirm: () => {
                deploy(record.name).then(res => {
                  if (res == 200) {
                    createMessage.success('部署成功!')

                  }
                })
              },
              placement: 'bottomRight'
            },

          },
          {
            label: '查看流程图',
            onClick: () => {
              read(record.name).then(res => {
                console.log(res)
                xmlText.value = res
              })
              openModal()
            },
          }
        ];

        return actions;
      },
    };
  },
})


</script>
