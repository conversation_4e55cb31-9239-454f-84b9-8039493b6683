import {
  create<PERSON>dit<PERSON><PERSON>,
  createD<PERSON><PERSON><PERSON><PERSON>,
  create<PERSON><PERSON>er<PERSON><PERSON>,
  createDefaultFilterR<PERSON>,
  createFormItemRender,
} from './common';

export default {
  autofocus: 'input.ant-input',
  renderDefault: createDefaultRender(),
  renderEdit: createEditRender(),
  renderFilter: createFilterRender(),
  defaultFilterMethod: createDefaultFilterRender(),
  renderItemContent: createFormItemRender(),
};
