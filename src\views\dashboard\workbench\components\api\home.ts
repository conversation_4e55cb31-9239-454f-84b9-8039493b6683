import { defHttp } from '/@/utils/http/axios';

enum Api {
  TODO = '/home/<USER>',
  FINISH_DATA = '/home/<USER>',
  QUESTION = '/home/<USER>',
  Operation = '/home/<USER>',
  OPERATION_BY_LINE = '/home/<USER>',
  OPERATION_BY_DEPT = '/home/<USER>',
  OPERATION_BY_DEPT_AND_LINE = '/home/<USER>',

  QUESTION_BY_LINE = '/home/<USER>',
  QUESTION_BY_DEPT = '/home/<USER>',
  QUESTION_BY_DEPT_AND_LINE = '/home/<USER>',
}

export const getQuestionByDeptAndLine = (data) => {
  return defHttp.post({ url: Api.QUESTION_BY_DEPT_AND_LINE, data: data });
};

export const getQuestionByDept = (data) => {
  return defHttp.post({ url: Api.QUESTION_BY_DEPT, data: data });
};

export const getQuestionByLine = (data) => {
  return defHttp.post({ url: Api.QUESTION_BY_LINE, data: data });
};
export const getOperationByDeptAndLine = (data) => {
  return defHttp.post({ url: Api.OPERATION_BY_DEPT_AND_LINE, data: data });
};

export const getOperationByDept = (data) => {
  return defHttp.post({ url: Api.OPERATION_BY_DEPT, data: data });
};

export const getOperationByLine = (data) => {
  return defHttp.post({ url: Api.OPERATION_BY_LINE, data: data });
};
export const getTodoData = () => {
  return defHttp.post({ url: Api.TODO });
};
export const getFinishData = (data) => {
  return defHttp.post({ url: Api.FINISH_DATA, params: { clazz: data } });
};

export const getQuestion = (data) => {
  return defHttp.post({ url: Api.QUESTION, data });
};

export const getOperation = (data) => {
  return defHttp.post({ url: Api.Operation, data });
};
