<template>
  <a-checkbox v-model:checked="selected" @click="selectAll">全选</a-checkbox>
  <a-checkbox-group @change="onChange" v-model:value="checkedVal" style="width: 100%">
    <a-row>
      <a-col v-for="item in options" :key="item.id" :span="4">
        <a-checkbox :value="item.id">{{ item.label }}</a-checkbox>
      </a-col>
    </a-row>
  </a-checkbox-group>
</template>

<script lang="ts">
  import { defineComponent, ref, watch, onMounted } from 'vue';
  import { CheckboxGroup, Checkbox } from 'ant-design-vue';
  import { findCheckBox } from '/@/api/erupt/erupt';

  export default defineComponent({
    name: 'CheckGroup',
    components: {
      [CheckboxGroup.name]: CheckboxGroup,
      [Checkbox.name]: Checkbox,
    },
    props: {
      value: {},
      className: {},
      fieldName: {},
    },
    emits: ['change', 'update:value'],
    setup(props, { emit }) {
      const options = ref([]);
      const checkedVal = ref([]);
      const selected = ref(false);
      const { className, fieldName } = props;

      onMounted(() => {
        findCheckBox(className, fieldName).then((res) => {
          res.forEach((item) => {
            item.version = item.remark;
          });
          options.value = res;
        });
      });

      watch(
        () => props.value,
        (val) => {
          if (val && val.length > 0) {
            // 更新 selected 状态
            selected.value = val.length === options.value.length;

            let cvl = [];
            if (val[0] instanceof Object) {
              val.forEach((item) => {
                cvl.push(item.id);
              });
              checkedVal.value = cvl;
            } else {
              setTimeout(() => {
                let valObjs = [];
                val.forEach((v) => {
                  options.value.forEach((item) => {
                    if (item.id === v) {
                      valObjs.push(item);
                    }
                  });
                });
                emit('update:value', valObjs);
                emit('change', valObjs);
              }, 200);
              checkedVal.value = val;
            }
          } else {
            checkedVal.value = [];
            selected.value = false; // 确保当没有选择时，全选框取消勾选
          }
        },
        { deep: true, immediate: true }, // 添加 immediate 选项以确保首次渲染时也触发监听
      );

      const selectAll = () => {
        selected.value = !selected.value;
        if (selected.value) {
          let checkVals = options.value.map((item) => item.id);
          checkedVal.value = checkVals;
          emit('update:value', checkVals);
          emit('change', checkVals);
        } else {
          checkedVal.value = [];
          emit('update:value', []);
          emit('change', []);
        }
      };

      const onChange = (val) => {
        let vals = [];
        val.forEach((item) => {
          const option = options.value.find((option) => item === option.id);
          if (option) {
            vals.push(option);
          }
        });
        emit('update:value', vals);
        emit('change', vals);

        // 更新 selected 状态
        selected.value = val.length === options.value.length;
      };

      return {
        options,
        checkedVal,
        selected,
        selectAll,
        onChange,
      };
    },
  });
</script>

<style scoped lang="less">
  .ant-checkbox-group {
    margin-top: 16px;
  }
</style>
