
<template>
  <div>
    <erupt-table class-name="QualityTemplate" 
    :overwrite-action="overwriteAction"></erupt-table>
    <BasicModal
      @register="register1"
      :showFooter="true"
      title="请上传附件"
      @ok="handleSubmit1"
      @close="handleCancel1"
    >
      <BasicForm style="height: 300px" @register="registerForm1"></BasicForm>
    </BasicModal>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import eruptTable from '../../../../components/EruptTable/eruptTable';
  import { useRouter } from 'vue-router';
  import { useModal } from '/@/components/Modal';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import BasicModal from '/@/components/Modal/src/BasicModal.vue';
  import { useForm } from '/@/components/Form';
  import {
    getOptionsApi,
    getUpLoadApi,
    issuedApi,
    persistApi,
  } from '/@/views/demo/system/quality/api/quality';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { remove } from '/@/api/erupt/erupt'; // 导入原删除接口

  export default defineComponent({
    name: 'QualityTemplate',
    components: { eruptTable },
    setup() {
      
  const router = useRouter();
  const { createConfirm } = useMessage();
  const [register, { openModal, closeModal }] = useModal();

  
  const schema = ref([
    {
      field: 'file',
      label: '附件',
      required: true,
      component: 'Attachment',
      colProps: { span: 12 },
    },
  ]);
  const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
  const { notification, createMessage } = useMessage();
  const rowId = ref();

  const [
    registerForm1,
    { resetFields: resetFields1, setFieldsValue: setFieldsValue1, validate: validate1 },
  ] = useForm({
    labelWidth: 100,
    schemas: schema,
    showActionButtonGroup: false,
  });
  const handleSubmit = async () => {
    issuedApi(rowId.value).then((res) => {
      if (res == 200) {
        createMessage.success('下发完成');
      } else {
        createMessage.error('下发失败');
      }
      closeModal();
    });
  };
  const handleSubmit1 = () => {
    persistApi('quaTpl_' + rowId.value).then((res) => {
      if (res) {
        createMessage.success('导入完成');
      } else {
        createMessage.success('导入完成');
      }
      closeModal1();
    });
  };
  const handleCancel = () => {};
  const handleCancel1 = () => {};

  function refresh() {
            document.querySelector('button[content="查询"]').click()
        }

  const extraAction = {
    nums: 3,
    actions: (row) => {
      return [
        {
          label: '明细',
          onClick: () => {
            router.push({ path: 'quality/tpl/items/' + row.row.id, query: row.row });
          },
        },
        {
          label: '下发',
          onClick: () => {
            createConfirm({
              title: '确认下发',
              content: '确定要下发吗？',
              onOk() {
                rowId.value = row.row.id;
                handleSubmit().then(() => {
                  notification.success({
                    message: '模板下发',
                    description: '模板下发成功',
                  });
                });
              },
              iconType: 'warning',
            });
          },
        },
        {
          label: '导入',
          onClick: () => {
            rowId.value = row.row.id;
            schema.value = [
              {
                field: 'file',
                label: '附件',
                required: true,
                component: 'Attachment',
                colProps: { span: 12 },
                componentProps: {
                  multiple: false,
                  maxNumber: 1,
                  accept: ['xlsx,xls'],
                  maxSize: 50,
                  api: getUpLoadApi(row.row.id),
                },
              },
            ];
            openModal1();
          },
        },
      ];
    },
  };
  const overwriteAction = ({
    detail: (row: any) => {
      router.push({
                    path: '/newLine/quality/particulars',
                    query: {
                      tplId: row.id,
                    },
                  });
      setTimeout(() => {
            openModal();
          }, 200);
    },
    delete: (row: any) => {
    createConfirm({
    content: '请确认是否删除质保项目（删除后质保详情相关内容同步删除）',
    onOk: async () => {
      try {
        debugger;
        const { status } = await remove('QualityTemplate', row);
        debugger;
        if (status === 'SUCCESS') {
          createMessage.success('删除成功');
          close();
          refresh();
        }
      } catch (error) {
        createMessage.error('删除失败');
      }
    },
    onCancel: () => {
    },
    iconType: 'warning',
  });
},
  });
   
      return {
        overwriteAction,
        extraAction,
        register,
        register1,
        registerForm1,
        handleSubmit,
        handleCancel,
        handleCancel1,
        handleSubmit1,
        openModal,
      };
    },
  });
</script>

