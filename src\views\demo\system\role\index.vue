<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增角色</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '菜单',
                onClick: handleMenu.bind(null, record),
              },
              {
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '权限',
                onClick: handlePermission.bind(null, record),
              },
              {
                label: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <RoleDrawer @register="registerDrawer" @success="handleSuccess" />
    <PermissionDrawer @register="registerPermissionDrawer" @success="handleSuccess" />
    <BasicModal
      @register="register"
      :showFooter="true"
      title="菜单"
      @ok="handleSubmit"
      @close="handleCancel"
      okText="保存"
    >
      <a-tree
        v-model:expandedKeys="expandedKeys"
        v-model:selectedKeys="selectedKeys"
        v-model:checkedKeys="checkedKeys"
        checkable
        @check="handleSelect"
        :tree-data="treeData"
      >
        <template #title="{ title, key }">
          <span v-if="key === '0-0-1-0'" style="color: #1890ff">{{ title }}</span>
          <template v-else>{{ title }}</template>
        </template>
      </a-tree>
    </BasicModal>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';

  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getMenuList, getRoleListByPage, roleDeleteByIds } from '/@/api/demo/system';

  import { useDrawer } from '/@/components/Drawer';
  import RoleDrawer from './RoleDrawer.vue';
  import PermissionDrawer from './PermissionDrawer.vue';

  import { columns, searchFormSchema } from './role.data';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { Tree } from 'ant-design-vue';
  import { getPermCode, getPermCodeById, savePermCode } from '/@/api/sys/user';
  import { useMessage } from '/@/hooks/web/useMessage';

  export default defineComponent({
    name: 'RoleManagement',
    components: {
      BasicModal,
      BasicTable,
      RoleDrawer,
      PermissionDrawer,
      TableAction,
      [Tree.name]: Tree,
    },
    setup() {
      const [registerDrawer, { openDrawer }] = useDrawer();
      const [registerPermissionDrawer, { openDrawer: openPermissionDrawer }] = useDrawer();
      const [register, { openModal, closeModal }] = useModal();
      const [registerTable, { reload }] = useTable({
        title: '角色列表',
        api: getRoleListByPage,
        columns,
        formConfig: {
          labelWidth: 120,
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        actionColumn: {
          width: 250,
          title: '操作',
          dataIndex: 'action',
          // slots: { customRender: 'action' },
          fixed: undefined,
        },
      });
      const { createMessage } = useMessage();
      const currentId = ref(null);
      const treeData = ref([]);

      const expandedKeys = ref<string[]>([]);
      const selectedKeys = ref<string[]>([]);
      const halfKeys = ref<string[]>([]);
      const checkedKeys = ref<string[]>([]);

      function handleCreate() {
        openDrawer(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        openDrawer(true, {
          record,
          isUpdate: true,
        });
        console.log(record);
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        roleDeleteByIds(record.id).then(() => {
          reload();
        });
      }

      function handleSuccess() {
        reload();
      }

      function handlePermission(record: Recordable) {
        openPermissionDrawer(true, {
          record,
          isUpdate: true,
        });
      }

      return {
        currentId,
        expandedKeys,
        selectedKeys,
        halfKeys,
        checkedKeys,
        treeData,
        register,
        openModal,
        closeModal,
        handleSubmit: () => {
          savePermCode(currentId.value, {
            selected: selectedKeys.value,
            halfSelected: halfKeys.value,
          }).then((res) => {
            if (res) {
              createMessage.success('保存成功！');
              closeModal();
            }
          });
        },
        handleCancel: () => {},
        handleMenu: (record) => {
          currentId.value = record.id;
          getMenuList().then((res) => {
            if (res) {
              getPermCodeById(record.id).then((res) => {
                if (res) {
                  expandedKeys.value = res;
                  selectedKeys.value = res;
                  checkedKeys.value = res;
                }
              });
              const addKey = (menus) => {
                menus.forEach((item) => {
                  item.key = item.permission;
                  if (item.children && item.children.length > 0) {
                    addKey(item.children);
                  }
                });
              };
              addKey(res);
              console.log('menus', res);
              treeData.value = res;
            }
          });
          openModal();
        },
        handleSelect: (keys, e) => {
          console.log(keys, e);
          halfKeys.value = e.halfCheckedKeys;
          selectedKeys.value = keys;
        },
        registerTable,
        registerDrawer,
        registerPermissionDrawer,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        handlePermission,
      };
    },
  });
</script>
