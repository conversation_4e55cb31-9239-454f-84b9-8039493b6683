<template>
  <div style="height: 900px">
    <VxeBasicTable ref="tableRef" v-bind="gridOptions">
      <template #action="{ row }">
        <TableAction outside :actions="createActions(row)" />
      </template>
    </VxeBasicTable>
    <BasicModal
      @register="register"
      :showFooter="true"
      defaultFullscreen
      title="请办理"
      @ok="handleSubmit"
      @close="handleCancel"
    >
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="详细信息">
          <Description
            class="mt-4"
            layout="vertical"
            :contentStyle="{
              width: '100%',
              wordBreak: 'break-all',
              whiteSpace: 'normal',
              display: 'inline-block',
            }"
            :column="4"
            :data="descData"
            :schema="lookSchema"
          />
        </a-collapse-panel>
        <a-collapse-panel key="2" header="审批历史">
          <a-steps status="finish" direction="vertical" size="small" v-for="step in steps">
            <a-step :title="step.name">
              <template #description>
                <div>意见：{{ step.opinion }}</div>
                <div>办理人：{{ step.person }}</div>
                <p>办理时间：{{ step.createDateTime }}</p>
              </template>
            </a-step>
          </a-steps>
        </a-collapse-panel>
      </a-collapse>
      <a-card style="width: 100%; margin-top: 1%" title="审批处理">
        <BasicForm style="height: 100%" @register="registerForm"></BasicForm>
      </a-card>
      <EruptUploadPreviewModal readOnly ref="uploadModalRef" @register="registerPreviewModal" />
      <template #footer>
        <VxeButton type="primary" @click="approve">确定</VxeButton>
        <VxeButton type="primary" @click="cancel">取消</VxeButton>
      </template>
    </BasicModal>
  </div>
</template>

<script lang="tsx">
  import {
    defineComponent,
    PropType,
    ref,
    onMounted,
    watchEffect,
    computed,
    unref,
    watch,
    h,
  } from 'vue';
  import { VXETable } from 'vxe-table';
  import BpmnViewer from '/@/views/process/bpmn/index1.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import {
    complete,
    getApprovalRecords,
    getClassName,
    getProcessComponent,
    getProcessComponentOnChange,
    getTasksListByUser,
  } from '/@/api/process/process';
  import { ActionItem, TableAction } from '/@/components/Table';
  import { BasicTableProps, VxeBasicTable } from '/@/components/VxeTable';
  import {
    Tag,
    Tooltip,
    Steps,
    Step,
    Tabs,
    TabPane,
    Card,
    Collapse,
    CollapsePanel,
  } from 'ant-design-vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { task } from '/@/views/process/definition/processDefData';
  import { useForm } from '/@/components/Form';
  import { buildApi, getDetailOne } from '/@/api/erupt/erupt';
  import { getUpLoadApi } from '/@/components/EruptTable/componets';
  import { useTaskStore } from '/@/store/modules/task';
  import { Description } from '/@/components/Description';
  import BasicUpload from '/@/components/Upload/src/BasicUpload.vue';
  import EruptUploadPreviewModal from '/@/components/Form/src/components/EruptUploadPreviewModal.vue';
  import Icon from '/@/components/Icon';
  import { Base64 } from 'js-base64';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useUserStore } from '/@/store/modules/user';

  export default defineComponent({
    components: {
      EruptUploadPreviewModal,
      BasicUpload,
      Description,
      Tooltip,
      BpmnViewer,
      Tag,
      Tabs,
      TabPane,
      [Steps.name]: Steps,
      [Step.name]: Step,
      [Card.name]: Card,
      [Collapse.name]: Collapse,
      [CollapsePanel.name]: CollapsePanel,
      BasicModal,
      VxeBasicTable,
      TableAction,
      BasicForm,
      BasicDrawer,
      VXETable,
    },
    setup(props, { attrs, emit }) {
      const pid = ref();
      const globSetting = useGlobSetting();
      const processInstanceId = ref('');
      const { createMessage } = useMessage();
      const taskStore = useTaskStore();
      const lookSchema = ref([]);
      const taskSchema = ref([]);
      const [register, { openModal, closeModal }] = useModal();
      const activeKey = ref('1');
      const acts = ref([]);
      const cName = ref('');
      const step = ref('');
      const steps = ref([]);
      const actionStep = ref('');
      const descData = ref([]);
      const current = ref(0);
      const bussinesKey = ref('');
      const uploadModalRef = ref();
      const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();
      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        schemas: taskSchema,
        showActionButtonGroup: false,
      });
      const gridOptions = ref<BasicTableProps>({
        id: 'VxeTable',
        keepSource: true,
        editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
        rowConfig: { isHover: true, useKey: true, isCurrent: false },
        columnConfig: { isHover: true, isCurrent: false },
        formConfig: {
          enabled: true,
          items: [
            {
              field: 'defName',
              title: '流程名称',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'name',
              title: '当前步骤',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'desc',
              title: '描述',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'createTimes',
              title: '时间',
              itemRender: {
                name: 'ARangePicker',
                props: {
                  showTime: false, // 设置为 false 只显示年月日
                  valueFormat: 'YYYY-MM-DD', // 设置日期格式
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              span: 24,
              align: 'center',
              className: '!pr-0',
              collapseNode: true,
              itemRender: {
                name: 'AButtonGroup',
                children: [
                  {
                    props: {
                      type: 'primary',
                      content: '查询',
                      htmlType: 'submit',
                    },
                    attrs: {
                      class: 'mr-2',
                    },
                  },
                  {
                    props: {
                      type: 'default',
                      htmlType: 'reset',
                      content: '重置',
                    },
                  },
                ],
              },
            },
          ],
        },
        columns: task,
        toolbarConfig: {
          refresh: true, // 显示刷新按钮
          import: false, // 显示导入按钮
          export: false, // 显示导出按钮
          print: false, // 显示打印按钮
          zoom: false, // 显示全屏按钮
          custom: true,
        },
        height: 'auto',
        proxyConfig: {
          ajax: {
            query: async ({ page, form }) => {
              const bpmns = await getTasksListByUser(page, form);
              return bpmns;
            },
            queryAll: async ({ form }) => {
              const bpmns = await getTasksListByUser(form);
              let rows = [];
              bpmns.forEach((bpmn) => {
                rows.push({ name: bpmn.split('.bpmn')[0] });
              });
              return rows;
            },
          },
        },
      });
      function actionChange(e) {
        console.log(e, step.value);
        if (e != '') {
          actionStep.value = e;
        }
        getProcessComponentOnChange(pid.value, cName.value, { option: e ? e : '' }).then((res) => {
          if (res) {
            res.forEach((item) => {
              if (item.field == 'option') item.componentProps.onChange = (e) => actionChange(e);
              if (item.field == 'enclosure')
                item.componentProps.api = getUpLoadApi('Operation', 'enclosure');
            });
            taskSchema.value = res;
          }
        });
      }
      interface RowVO {
        name: string;
      }

      const tableData = ref<Array<RowVO>>([]);

      /*onMounted(() => {
      moduleListApi().then(res => {
        const bpmns = res
        let rows = []
        bpmns.forEach(bpmn => {
          rows.push({name: bpmn.split('.bpmn')[0]})
        })
        tableData.value = rows
      })
    })*/

      return {
        tableData,
        gridOptions,
        register,
        openModal,
        closeModal,
        registerForm,
        descData,
        activeKey,
        acts,
        lookSchema,
        taskSchema,
        steps,
        actionStep,
        setFieldsValue,
        uploadModalRef,
        current,
        cancel: () => {
          actionStep.value = '';
          closeModal();
        },
        handleShow: (downloadUrl, index) => {
          console.log(downloadUrl);
          const userStore = useUserStore();
          const watermarkTxt = userStore.userInfo.username + ' ' + userStore.userInfo.realName;
          modalUrl.value = ''; // 清空旧缓存
          modalUrl.value =
            globSetting.previewUrl +
            '/onlinePreview?url=' +
            downloadUrl +
            '&watermarkTxt=' +
            watermarkTxt; // 设置预览 URL
          key.value = index; // 重新加载 iframe
          openModalPreview(true); // 打开 modal 窗口
        },
        approve: async () => {
          let values = await validate();
          const params = {
            step: step.value,
            ...values,
            bussinesKey: bussinesKey.value,
            processInstanceId: processInstanceId.value,
          };
          complete(pid.value, params, cName.value).then((res) => {
            if (res == 200) {
              createMessage.success('办理完成');
              resetFields();
              document.querySelector('button[title="刷新"]').click();
            }
            taskStore.setTaskList();
            actionStep.value = '';
            closeModal();
          });
        },
        registerPreviewModal,
        openPreviewModal,
        handleSubmit: () => {},
        handleCancel: () => {},
        createActions: (record) => {
          const actions: ActionItem[] = [
            {
              label: '办理',
              onClick: async () => {
                console.log(record);
                step.value = record.name;
                pid.value = record.id;
                processInstanceId.value = record.processInstanceId;
                let schema = [];

                getClassName(record.id).then(async (res) => {
                  if (res) {
                    bussinesKey.value = res;
                    const arr = res.split('.');
                    const className = arr[0];
                    cName.value = className;
                    getProcessComponent(record.id, className).then((res) => {
                      console.log('component', res);
                      if (res) {
                        res.forEach((item) => {
                          if (item.field == 'option') {
                            item.componentProps.onChange = (e) => actionChange(e);
                          }
                          if (item.field == 'enclosure') {
                            item.componentProps.api = getUpLoadApi(className, 'enclosure');
                          }
                        });
                        taskSchema.value = res;
                      }
                    });
                    const id = arr[1];
                    buildApi(className).then(async (res) => {
                      const {
                        eruptModel: { eruptFieldModels, eruptJson },
                        tabErupts,
                        power,
                      } = res;

                      let details = [];
                      eruptFieldModels.forEach((item) => {
                        const key = item.fieldName;
                        const title = item.eruptFieldJson.edit.title;

                        if (item.eruptFieldJson.views.length > 0) {
                          item.eruptFieldJson.views.forEach((v) => {
                            if (v.show) {
                              const k = v.column ? key + '_' + v.column : key;
                              if (item.eruptFieldJson.edit.show.detail_show) {
                                debugger;
                                const d = {
                                  field: k,
                                  label: v.title,
                                  span: item.eruptFieldJson.edit.detailSpan,
                                };
                                details.push(d);
                              }
                            } else {
                              if (item.eruptFieldJson.edit.show.detail_show) {
                                const d = {
                                  field: key,
                                  label: title,
                                  span: item.eruptFieldJson.edit.detailSpan,
                                };
                                details.push(d);
                              }
                            }
                          });
                        } else {
                          if (item.eruptFieldJson.edit.show.detail_show && key !== 'id') {
                            if (item.eruptFieldJson.edit.type == 'ATTACHMENT') {
                              if (!item.eruptFieldJson.edit.attachmentType.isgBigFile) {
                                const d = {
                                  field: key,
                                  label: title,
                                  render: (val) => {
                                    return (
                                      <div>
                                        {val
                                          ? val.split('|').map((item, index) => {
                                              return (
                                                <div>
                                                  <a
                                                    onClick={() =>
                                                      handleShow(
                                                        encodeURIComponent(
                                                          Base64.encode(
                                                            globSetting.eruptAttachment + item,
                                                          ),
                                                        ),
                                                        index,
                                                      )
                                                    }
                                                  >
                                                    {item.substring(item.lastIndexOf('/') + 1)}
                                                  </a>{' '}
                                                  <a
                                                    style={{ marginLeft: '15px', color: '#ff6118' }}
                                                    href={globSetting.eruptAttachment + item}
                                                  >
                                                    下载
                                                  </a>
                                                </div>
                                              );
                                            })
                                          : ''}

                                        {/*<Tooltip placement={'bottom'}>
                          <a-button
                            onClick={() => {
                              const files = val ? val.split('|') : [];
                              uploadModalRef.value.setFiles(files);
                              openPreviewModal();
                            }}
                          >
                            <Icon icon="bi:eye" />
                          </a-button>
                        </Tooltip>*/}
                                      </div>
                                    );
                                  },
                                };
                                details.push(d);
                              } else {
                                const d = {
                                  field: key,
                                  label: title,
                                  render: (val) => {
                                    return (
                                      <div>
                                        {val
                                          ? val.split('|').map((item, index) => {
                                              return (
                                                <div>
                                                  <a
                                                    style={{ marginLeft: '15px', color: '#ff6118' }}
                                                    href={globSetting.eruptAttachment + item}
                                                  >
                                                    {item.substring(item.lastIndexOf('/') + 1)}
                                                  </a>
                                                </div>
                                              );
                                            })
                                          : ''}

                                        {/*<Tooltip placement={'bottom'}>
                          <a-button
                            onClick={() => {
                              const files = val ? val.split('|') : [];
                              uploadModalRef.value.setFiles(files);
                              openPreviewModal();
                            }}
                          >
                            <Icon icon="bi:eye" />
                          </a-button>
                        </Tooltip>*/}
                                      </div>
                                    );
                                  },
                                };
                                details.push(d);
                              }
                            } else {
                              const d = {
                                field: key,
                                label: title,
                                span: item.eruptFieldJson.edit.detailSpan,
                              };
                              details.push(d);
                            }
                          }
                        }
                        //  formState[key] = null
                        /*if (item.eruptFieldJson.edit.show && key !== 'id') {
                        if (item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' && item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' && item.eruptFieldJson.edit.type !== 'TAB_TREE') {
                          const d = {
                            field: key,
                            label: title,
                            component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                            componentProps: (form) => {
                              return {
                                ...getComponentProps(item, className),
                                readonly: true,
                                style: {
                                  color: '#303030',
                                  background: '#fff',
                                  pointerEvents: item.eruptFieldJson.edit.type == 'ATTACHMENT' ? 'auto' : 'none'
                                }
                              }
                            },
                            required: item.eruptFieldJson.edit.notNull,
                            colProps: {
                              span: item.eruptFieldJson.edit.colSpan,
                            },
                          }
                          details.push(d)
                        }
                      }*/
                      });
                      /*details.push({
                      field: 'message',
                      label: '备注',
                      required: true,
                      component: 'InputTextArea',
                      colProps: {span: 24},
                    })*/
                      lookSchema.value = details;

                      const entity = await getDetailOne(className, id);
                      descData.value = entity;

                      //gridOptions.columns = columns
                      //search()
                    });
                    const records = await getApprovalRecords(record.processInstanceId);
                    steps.value = records;
                    current.value = records.length;
                    openModal();
                  }
                });
              },
            },
          ];

          return actions;
        },
      };
    },
  });
</script>
<style scoped>
  .ant-select-disabled {
    color: #303030;
    background: #fff;
    cursor: not-allowed;
  }

  .attachment-container {
    display: flex;
    align-items: center;
  }

  .attachment-container span {
    margin-left: 60px; /* 增加间距 */
    margin-right: 10px; /* 增加间距 */
  }
</style>
