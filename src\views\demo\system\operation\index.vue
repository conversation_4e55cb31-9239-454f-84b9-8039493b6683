<template>
  <erupt-table
    title="运营筹备"
    class-name="Operation"
    :is-process="true"
    :form-dynamic-control="dynamicController"
    :row-dynamic-controller="rowDynamicController"
    :pre-conditions="preCondition"
  >
    <template #isLate="{ row }">
      <div v-if="row.isLate == '是'" style="align-self: center; color: red">
        {{ row.isLate }}
      </div>
      <div v-if="row.isLate == '否'" style="align-self: center; color: #00bb00">
        {{ row.isLate }}
      </div>
    </template>
  </erupt-table>
</template>

<script lang="ts">
  import { computed, defineComponent, onMounted } from 'vue';
  import eruptTable from '../../../../components/EruptTable/eruptTable';
  import { NColorPicker } from 'naive-ui';
  import { useOperationStoreWithOut } from '/src/store/modules/operation';
  import { useWatermark } from '/@/hooks/web/useWatermark';
  import { useUserStore } from '/src/store/modules/user';
  import { useRoute } from 'vue-router';
  import Template from '/@/views/demo/system/quality/template.vue';

  export default defineComponent({
    name: 'Operation',
    components: { Template, eruptTable, NColorPicker },
    setup() {
      const route = useRoute();
      const line = computed(() => route.query.line);
      const dept = computed(() => route.query.dept);
      const state = computed(() => route.query.state);
      const userStore = useUserStore();
      const preCondition = new Map([]);
      const { setWatermark, clear } = useWatermark();
      onMounted(() => {
        setWatermark(userStore.getUserInfo.username);
        if (dept.value != null && dept.value != '' && dept.value != '新线管理部') {
          preCondition.set('mainDeptNames', dept.value);
        } else if (userStore.userInfo.dept != '新线管理部') {
          preCondition.set('mainDeptNames', userStore.userInfo.dept);
        }
        if (state.value != null && state.value != '' && state.value != '总数') {
          preCondition.set('statuName', state.value);
        }
        if (line.value != null && line.value != '') {
          preCondition.set('lineId', line.value);
        }
      });
      const overwriteAction = {
        add: () => {
          console.log('add');
        },
      };
      const operation = useOperationStoreWithOut();
      //三层组件动态change
      const dynamicController = {
        mainDeptId: (form, e) => {
          const { formModel, formActionType } = form;
          console.log('111' + form, e);
          let options = [];
          operation.getPersonOption(e.id).then((res) => {
            console.log(res);
            res.forEach((item) => {
              console.log('item' + item.name + item.id);
              options.push({ label: item.name, value: item.id });
            });
            formActionType.updateSchema({
              field: 'workUserId',
              componentProps: {
                options,
              },
            });
          });
        },
        tags: (form) => {
          console.log('form' + form);
        },
      };
      const rowDynamicController = {
        edit: (row) => {
          return false;
        },
      };
      return {
        overwriteAction,
        dynamicController,
        rowDynamicController,
        preCondition,
      };
    },
  });
</script>

<style scoped lang="less"></style>
