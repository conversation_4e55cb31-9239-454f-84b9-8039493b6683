import { VxeFormItemProps, VxeGridPropTypes } from '/@/components/VxeTable';

export const vxeTableColumns: VxeGridPropTypes.Columns = [
  {
    title: '序号',
    type: 'seq',
    fixed: 'left',
    width: '50',
    align: 'center',
  },
  {
    title: 'ID',
    field: 'id',
    width: 150,
    showOverflow: 'tooltip',
    fixed: 'left',
  },
  {
    title: '流程名称',
    field: 'name',
  },
  {
    title: '流程版本',
    field: 'version',
  },
  {
    width: 160,
    title: '操作',
    align: 'center',
    slots: { default: 'action' },
    fixed: 'right',
  },
];

export const vxeTableFormSchema: VxeFormItemProps[] = [
  {
    field: 'id',
    title: 'ID',
    itemRender: {
      name: 'AInput',
    },
    span: 6,
  },
];
