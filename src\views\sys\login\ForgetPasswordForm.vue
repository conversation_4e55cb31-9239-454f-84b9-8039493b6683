<template>
  <template v-if="getShow">
    <LoginFormTitle class="enter-x" />
    <Form class="p-4 enter-x" :model="formData" :rules="getFormRules" ref="formRef">
      <FormItem name="account" class="enter-x">
        <Input
          size="large"
          v-model:value="formData.account"
          :placeholder="t('sys.login.userName')"
        />
      </FormItem>

      <FormItem name="sms" class="enter-x">
        <CountdownInput
          size="large"
          v-model:value="formData.sms"
          :placeholder="t('sys.login.smsCode')"
          :sendCodeApi="sendSms"
          :account="formData.account"
        />
      </FormItem>

      <FormItem name="passwordNew" class="enter-x">
        <InputPassword
          size="large"
          v-model:value="formData.passwordNew"
          :placeholder="t('sys.login.passwordNew')"
        />
      </FormItem>

      <FormItem name="confirmPassword" class="enter-x">
        <InputPassword
          size="large"
          v-model:value="formData.confirmPassword"
          :placeholder="t('sys.login.confirmPassword')"
        />
      </FormItem>

      <FormItem class="enter-x">
        <Button type="primary" size="large" block @click="handleReset" :loading="loading">
          {{ t('common.resetText') }}
        </Button>
        <Button size="large" block class="mt-4" @click="handleBackLogin">
          {{ t('sys.login.backSignIn') }}
        </Button>
      </FormItem>
    </Form>
  </template>
</template>
<script lang="ts" setup>
  import { reactive, ref, computed, unref } from 'vue';
  import LoginFormTitle from './LoginFormTitle.vue';
  import { Form, Input, Button } from 'ant-design-vue';
  import { CountdownInput } from '/@/components/CountDown';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getSendSms, getResetPassword } from '/@/api/sys/user';
  import { useLoginState, LoginStateEnum } from './useLogin';
  import { useMessage } from '/@/hooks/web/useMessage';

  const FormItem = Form.Item;
  const InputPassword = Input.Password;
  const { t } = useI18n();
  const { handleBackLogin, getLoginState } = useLoginState();
  const { setLoginState } = useLoginState();
  const { notification, createErrorModal } = useMessage();

  const formRef = ref();
  const loading = ref(false);

  const formData = reactive({
    account: '',
    smsKey: '',
    sms: '',
    passwordNew: '',
    confirmPassword: '',
  });

  const getShow = computed(() => unref(getLoginState) === LoginStateEnum.RESET_PASSWORD);

  // 定义表单验证规则
  const getFormRules = reactive({
    account: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    sms: [{ required: true, message: '请输入短信验证码', trigger: 'blur' }],
    passwordNew: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 8, message: '密码长度不能小于8位', trigger: 'change' },
      {
        validator: (rule, value) => {
          if (value && value.toLowerCase().includes('njdt')) {
            return Promise.reject('密码不能包含“njdt”');
          }
          if (value && formData.account && value.includes(formData.account)) {
            return Promise.reject('密码不能包含账号');
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
    confirmPassword: [
      { required: true, message: '请确认密码', trigger: 'change' },
      {
        validator: (rule, value) => {
          if (!value) {
            return Promise.reject('确认密码不能为空');
          }
          if (value !== formData.passwordNew) {
            return Promise.reject('两次输入的密码不一致');
          }
          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
  });

  async function sendSms() {
    try {
      debugger;
      const params = { personNo: formData.account };
      await getSendSms(params);
      return true;
    } catch (error) {
      createErrorModal({
        title: '验证码发送失败！',
        content: (error as unknown as Error).message || t('sys.api.networkExceptionMsg'),
      });
      return false;
    }
  }

  async function handleReset() {
    const form = unref(formRef);
    if (!form) return;
    try {
      loading.value = true;
      await form.validate();

      const params = {
        personNo: formData.account,
        smsCode: formData.sms,
        password: formData.passwordNew,
      };

      const response = await getResetPassword(params);

      setLoginState(LoginStateEnum.LOGIN);
      console.log(response);
      if (response) {
        notification.success({
          message: '密码重置成功！',
          duration: 3,
        });
      }
      await form.resetFields();
    } catch (error) {
      createErrorModal({
        title: '重置密码失败',
        content: (error as unknown as Error).message || t('sys.api.networkExceptionMsg'),
      });
      console.error('重置密码失败:', error);
    } finally {
      loading.value = false;
    }
  }
</script>
