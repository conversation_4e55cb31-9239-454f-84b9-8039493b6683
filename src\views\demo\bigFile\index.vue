<template>
  <PageWrapper title="大文件上传示例">
    <div class="p-4">
      <Card title="大文件上传">
        <BigFileUpload
          beforeUploadUrl="/system/upload/before"
          uploadChunkUrl="/system/upload/chunk"
          uploadProgressUrl="/system/upload/progress"
          uploadMergeUrl="/system/upload/merge"
        />
      </Card>
    </div>
  </PageWrapper>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import { Card } from 'ant-design-vue';
  import { PageWrapper } from '/@/components/Page';
  import BigFileUpload from '/@/components/Upload/src/BigFileUpload.vue';

  export default defineComponent({
    name: 'BigFileUploadDemo',
    components: {
      Card,
      PageWrapper,
      BigFileUpload,
    },
    setup() {
      return {};
    },
  });
</script>
