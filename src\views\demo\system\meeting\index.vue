<template>
  <div>
    <!-- 会议表格 -->
    <erupt-table
      title="会议管理"
      class-name="Meeting"
      :extra-action="extraAction"
      ref="meetingTable"
      :overwrite-action="overwriteAction"
      :row-dynamic-controller="rowDynamicController"
      :button-renders="buttonRenders"
    />
    <BasicModal
      @register="registerPreviewModal"
      title="预览文件"
      :width="1000"
      :height="800"
      :footer="null"
    >
      <iframe
        :key="previewKey"
        :src="previewUrl"
        width="1000"
        height="800"
        frameborder="0"
      ></iframe>
    </BasicModal>
    <!-- 新增/编辑弹窗 -->
    <BasicModal
      @register="register"
      :showFooter="true"
      :title="type"
      width="50%"
      @ok="handleSubmit"
      okText="保存"
    >
      <BasicForm @register="registerForm"></BasicForm>
    </BasicModal>

    <!-- 详情弹窗 -->
    <BasicModal
      @register="registerDetail"
      :showFooter="false"
      width="60%"
      title="会议详情"
      @ok="handleDetailOk"
      @close="handleDetailClose"
    >
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="会议详细信息">
          <div class="detail-container">
            <!-- 基本信息 -->
            <div class="detail-section">
              <h3 class="section-title">基本信息</h3>
              <div class="detail-grid">
                <div class="detail-label">会议内容</div>
                <div class="detail-value">{{ detailVal.title }}</div>
                <div class="detail-label">会议时间</div>
                <div class="detail-value">{{ detailVal.startMeetingDate.substring(0, 16) }}</div>
                <div class="detail-label">地点</div>
                <div class="detail-value">{{ detailVal.location }}</div>
              </div>
            </div>

            <!-- 参与信息 -->
            <div class="detail-section">
              <h3 class="section-title">参与信息</h3>
              <div class="detail-grid">
                <div class="detail-label">所属线路</div>
                <div class="detail-value">{{ detailVal.lineId }}</div>
                <div class="detail-label">会议类别</div>
                <div class="detail-value">{{ detailVal.meetingCategory }}</div>
                <div class="detail-label">会议通知状态</div>
                <div class="detail-value">{{ detailVal.meetingStatus }}</div>
              </div>
            </div>
            <div class="detail-section">
              <h3 class="section-title">会议资料</h3>
              <a-descriptions-item label="附件">
                <div v-if="detailVal.attachment1">
                  <div
                    v-for="(item, index) in detailVal.attachment1.split('|')"
                    :key="index"
                    style="margin: 4px 0"
                  >
                    <a class="file-link" @click="previewShow(item, index)">
                      {{ item.substring(item.lastIndexOf('/') + 1) }}
                    </a>
                    <a
                      style="margin-left: 10px; color: #ff6118"
                      :href="eruptAttachment + item"
                      target="_blank"
                      download
                    >
                      下载
                    </a>
                  </div>
                </div>
                <span v-else style="color: #8b949e">暂无附件</span>
              </a-descriptions-item>
            </div>

            <!--            &lt;!&ndash; 新增会议结果附件展示 &ndash;&gt;
            <div class="detail-section">
              <h3 class="section-title">会议结果附件</h3>
              <a-descriptions-item label="会议结果附件">
                <div v-if="detailVal.attachment">
                  <div
                    v-for="(item, index) in detailVal.attachment.split('|')"
                    :key="'result-' + index"
                    style="margin: 4px 0"
                  >
                    <a class="file-link" @click="previewShow(item, 'result-' + index)">
                      {{ item.substring(item.lastIndexOf('/') + 1) }}
                    </a>
                    <a   style="margin-left: 10px; color: #ff6118" :href="eruptAttachment + item" target="_blank" download>
                      下载
                    </a>
                  </div>
                </div>
                <span v-else style="color: #8b949e">暂无附件</span>
              </a-descriptions-item>
            </div>-->
            <!-- 会议内容 -->
            <div class="detail-section">
              <h3 class="section-title">会议结果</h3>
              <div class="detail-content">{{ detailVal.content }}</div>
            </div>

            <!-- 联络人 -->
            <div class="detail-section">
              <h3 class="section-title">联络人</h3>
              <div class="detail-content">{{ detailVal.participants }}</div>
            </div>

            <!-- 参与部门 -->
            <div class="detail-section">
              <h3 class="section-title">参会单位</h3>
              <div class="detail-content">{{ detailVal.departments }}</div>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </BasicModal>

    <!-- 修改会议通知弹窗 -->
    <BasicModal
      @register="registerStatus"
      :showFooter="true"
      title="修改会议通知"
      @ok="handleSubmitStatus"
      @close="handleCancelStatus"
    >
      <BasicForm style="height: 320px" @register="registerFormStatus"></BasicForm>
    </BasicModal>

    <!-- 会议记录弹窗 -->
    <BasicModal
      @register="registerMinutes"
      :showFooter="true"
      title="会议记录"
      width="50%"
      @ok="handleSubmitMinutes"
      @close="handleCancelMinutes"
    >
      <div>
        <p>会议内容：{{ descData.title }}</p>
        <p>时间：{{ descData.startMeetingDate }}</p>
        <p>地点：{{ descData.location }}</p>
        <p>联络人：{{ descData.participants }}</p>
      </div>
      <BasicForm @register="registerFormMinutes" style="margin-left: -22px"></BasicForm>
    </BasicModal>
  </div>
</template>

<script lang="ts">
  import { defineComponent, onMounted, ref } from 'vue';
  import eruptTable from '../../../../components/EruptTable/eruptTable';
  import { BasicModal, useModal } from '/@/components/Modal';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Base64 } from 'js-base64';
  import { useUserStore } from '/@/store/modules/user';
  import {
    updateMeetingStatusApi,
    updateMeetingContentApi,
    getPersonalMeetingApi,
    detail1,
    detail2,
    detail3,
    detail4,
    refreshMeetingMessage,
  } from './api/meeting';
  import { buildApi, getOne, save, update } from '/@/api/erupt/erupt';
  import TableAdd from '/@/components/Form/src/components/TableAdd';
  import TableSelectAdd from '/@/components/Form/src/components/TableSelectAdd';
  import TreeSeletAdd from '/@/components/Form/src/components/TreeSeletAdd';
  import {
    getComponent,
    getComponentProps,
    getSearchComponent,
    getUpLoadApi,
  } from '/@/components/EruptTable/componets';

  export default defineComponent({
    name: 'Meeting',
    components: {
      BasicModal,
      BasicForm,
      eruptTable,
      TableAdd,
      TableSelectAdd,
      TreeSeletAdd,
    },

    setup: function () {
      const { createMessage } = useMessage();
      const descData = ref<Record<string, any>>({});
      const globSetting = useGlobSetting();
      const eruptAttachment = globSetting?.eruptAttachment;
      const previewUrl = ref('');
      const previewKey = ref(0);
      const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();
      const editSchemas = ref<any[]>([]);
      const activeKey = ref('1');
      const detailVal = ref<Record<string, any>>({});
      const baseUrl = import.meta.env.VITE_GLOB_ERUPT_ATTACHMENT;
      const formatFileLinks = (filePaths) => {
        if (!filePaths) return '<span class="no-data">暂无附件</span>';

        return filePaths
          .split('|')
          .map((filePath) => {
            const fileName = filePath.split('/').pop(); // 更安全的文件名提取
            const encodedPath = encodeURI(filePath); // 处理特殊字符
            return `<a href="${baseUrl}${encodedPath}" class="file-link" target="_blank"> ${fileName}</a>`;
          })
          .join(' <br/> ');
      };
      const previewShow = (downloadUrl: string, index: string | number) => {
        const fullUrl = globSetting.eruptAttachment + downloadUrl;
        const encodedUrl = encodeURIComponent(Base64.encode(fullUrl));
        const userStore = useUserStore();
        const watermark = `${userStore.getUserInfo.username} ${userStore.getUserInfo.realName}`;

        previewUrl.value = `${
          globSetting.previewUrl
        }/onlinePreview?url=${encodedUrl}&watermarkTxt=${encodeURIComponent(watermark)}`;
        previewKey.value = +new Date() + index;
        openPreviewModal(true);
      };
      // 注册弹窗
      const [register, { openModal, closeModal }] = useModal();
      // 注册弹窗
      const [registerDetail, { openModal: openDetailModal, closeModal: closeDetailModal }] =
        useModal();
      const [registerStatus, { openModal: openStatusModal, closeModal: closeStatusModal }] =
        useModal();
      const [registerMinutes, { openModal: openMinutesModal, closeModal: closeMinutesModal }] =
        useModal();

      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        showActionButtonGroup: false,
        wrapperCol: { span: 24 },
        schemas: editSchemas,
      });

      const [
        registerFormStatus,
        {
          resetFields: resetFieldsStatus,
          validate: validateStatus,
          setFieldsValue: setFieldsValueStatus,
        },
      ] = useForm({
        labelWidth: 100,
        schemas: [
          {
            field: 'status',
            label: '会议通知',
            required: true,
            component: 'Select',
            colProps: { span: 16 },
            componentProps: {
              options: [
                { label: '已发布', value: '已发布' },
                { label: '会议完成', value: '会议完成' },
                { label: '取消', value: '取消' },
              ],
              placeholder: '请选择会议通知',
            },
          },
        ],
        showActionButtonGroup: false,
      });

      const [
        registerFormMinutes,
        {
          resetFields: resetFieldsMinutes,
          validate: validateMinutes,
          setFieldsValue: setFieldsValueMinutes,
        },
      ] = useForm({
        labelWidth: 103,
        schemas: [
          {
            field: 'content',
            label: ' 会议结果：',
            required: false,
            component: 'InputTextArea',
            colProps: { span: 24 },
            componentProps: {
              rows: 10,
            },
          },
          {
            field: 'meetingNumber',
            label: ' 会议人数：',
            required: false,
            component: 'Input',
            colProps: { span: 24 },
            componentProps: {
              rows: 10,
            },
          },
          {
            field: 'attachment',
            label: '附件：',
            required: false,
            component: 'Attachment',
            colProps: { span: 12 },
            componentProps: {
              multiple: false,
              maxSize: 20,
              accept: [
                'jpg',
                'jpeg',
                'png',
                'gif',
                'bmp',
                'pdf',
                'doc',
                'docx',
                'xls',
                'xlsx',
                'ppt',
                'pptx',
                'zip',
                'rar',
                'tar',
                'gz',
                'txt',
                'md',
              ],
              api: getUpLoadApi('Meeting', 'attachment'),
            },
          },
        ],
        showActionButtonGroup: false,
      });

      const base = ref({ id: '', version: '' });
      const rowId = ref('');
      const currentStatus = ref('');
      const type = ref('新增');
      const statusOptions1 = ref<any[]>([]); // 修复 statusOptions1 爆红问题

      const rowDynamicController = {
        edit: (rowData: any) => {
          const status = rowData.meetingStatus || '未知状态';
          return status !== '取消';
        },
        /* detail: (rowData: any) => {
          const status = rowData.meetingStatus || '未知状态';
          return status !== '取消';
        },*/
      };

      const overwriteAction = {
        add: () => {
          type.value = '新增';
          setTimeout(() => {
            resetFields();
            setFieldsValue({ meetingStatus: '新建' });
            openModal();
          }, 200);
        },
        edit: async (row: any) => {
          descData.value = await getPersonalMeetingApi();
          type.value = '编辑';
          const res = await getOne('Meeting', row.id);
          base.value = res;
          setTimeout(() => {
            resetFields();
            openModal();
            setTimeout(() => {
              setFieldsValue(res);
            }, 20);
          }, 200);
        },
        detail: async (row: any) => {
          try {
            const res = await getOne('Meeting', row.id);
            const {
              eruptModel: { eruptFieldModels },
            } = await buildApi('Meeting');

            // 处理线路和部门显示
            const departments = await detail1(row.id);
            const participants = await detail2(row.id);
            const lineId = await detail4(row.id);
            if (res.meetingCategory !== undefined) {
              const meetingCategory = await detail3(res.meetingCategory);
              res.meetingCategory = meetingCategory.meetingCategory;
            } else {
              res.meetingCategory = null; // 或者直接使用 undefined
            }
            res.departments = departments.departments;
            res.participants = participants.participants;
            res.lineId = lineId.lineId;
            detailVal.value = res;
            openDetailModal(); // 确保在数据获取后打开详情弹窗
          } catch (error) {
            createMessage.error('获取详情失败');
          }
        },
      };
      const handleDetailOk = () => {
        closeDetailModal(); // 关闭弹窗
      };
      const handleDetailClose = () => {
        closeDetailModal(); // 关闭弹窗
      };
      const handleSubmit = async () => {
        const value = await validate();
        try {
          if (type.value === '新增') {
            const { status, message } = await save('Meeting', value, null);
            if (status === 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              closeModal();
              refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          } else if (type.value === '编辑') {
            const { status, message } = await update('Meeting', { ...base.value, ...value }, null);
            if (status === 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              closeModal();
              refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          }
        } catch (error) {
          createMessage.error('操作失败，请检查数据');
        }
      };

      const handleSubmitStatus = async () => {
        const data = await validateStatus();
        try {
          const res = await updateMeetingStatusApi(rowId.value, data.status);
          if (res) {
            createMessage.success('会议通知更新成功');
            refresh();
          } else {
            createMessage.error('会议通知更新失败');
          }
          closeStatusModal();
          await refreshMeetingMessage(rowId.value);
        } catch (error) {
          createMessage.error('操作失败');
        }
      };

      const handleSubmitMinutes = async () => {
        const data = await validateMinutes();
        try {
          const res = await updateMeetingContentApi(
            rowId.value,
            data.content,
            data.meetingNumber,
            data.attachment,
          );
          if (res) {
            createMessage.success('会议记录保存成功');
            refresh();
          } else {
            createMessage.error('会议记录保存失败');
          }
          closeMinutesModal();
        } catch (error) {
          createMessage.error('操作失败');
        }
      };

      const handleCancelStatus = () => {
        resetFieldsStatus();
        closeStatusModal();
      };

      const handleCancelMinutes = () => {
        resetFieldsMinutes();
        closeMinutesModal();
      };

      const refresh = () => {
        const queryButton = document.querySelector('button[content="查询"]');
        if (queryButton) (queryButton as HTMLElement).click();
      };

      const extraAction = {
        nums: 2,
        actions: (row: any) => {
          const status = row.row.meetingStatus || '未知状态';
          if (status === '取消') {
            return [];
          }
          const actions = [];
          if (status !== '会议完成') {
            actions.push({
              label: '会议通知',
              onClick: () => {
                rowId.value = row.row.id;
                currentStatus.value = status;
                setFieldsValueStatus({ status: currentStatus.value });
                if (['新建', '已发布', '取消'].includes(currentStatus.value)) {
                  openStatusModal();
                }
              },
            });
          }
          actions.push({
            label: '会议记录',
            onClick: async () => {
              try {
                resetFieldsMinutes();
                openMinutesModal();
                rowId.value = row.row.id;
                const meetingDetails = await getOne('Meeting', row.row.id);
                const personalMeetings = await getPersonalMeetingApi();

                if (!meetingDetails || !personalMeetings) {
                  createMessage.error('数据获取失败');
                  return;
                }

                // 处理参与者名称
                const participants =
                  meetingDetails.participants
                    ?.map((id: string) => {
                      const person = personalMeetings.find((p: any) => p.id === id);
                      return person?.name || id;
                    })
                    .join(', ') || '';

                descData.value = { ...meetingDetails, participants };
                setFieldsValueMinutes(meetingDetails);
              } catch (error) {
                createMessage.error('数据获取失败');
              }
            },
          });
          return actions;
        },
      };

      const buttonRenders = [
        {
          content: '导出',
          buttonRender: {
            name: 'AButton',
            props: { type: 'primary' },
            events: {
              click: () =>
                window.open(
                  `${import.meta.env.VITE_REPORT_BASE}/jmreport/view/1039795766499209216`,
                ),
            },
          },
        },
      ];

      onMounted(async () => {
        try {
          const personalMeetings = await getPersonalMeetingApi();
          statusOptions1.value = personalMeetings.map((item: any) => ({
            label: item.name,
            value: item.id,
          }));

          const res = await buildApi('Meeting');
          const { eruptFieldModels } = res.eruptModel;
          const schemas = eruptFieldModels
            .filter(
              (item: any) =>
                item.eruptFieldJson.edit.show.edit_show &&
                item.fieldName !== 'id' &&
                !['TAB_TABLE_REFER', 'TAB_TABLE_ADD', 'TAB_TREE'].includes(
                  item.eruptFieldJson.edit.type,
                ),
            )
            .map((item: any) => ({
              field: item.fieldName,
              label: item.eruptFieldJson.edit.title,
              component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
              componentProps: () => ({
                ...getComponentProps(item, 'Meeting'),
                disabled: item.eruptFieldJson.edit.readOnly.edit,
              }),
              required: item.eruptFieldJson.edit.notNull,
              colProps: { span: item.eruptFieldJson.edit.colSpan },
            }));

          editSchemas.value = [
            ...schemas,
            {
              field: 'meetingStatus',
              label: '会议通知',
              component: 'Input',
              colProps: { span: 24 },
              show: false,
              defaultValue: '新建',
            },
          ];
        } catch (error) {
          createMessage.error('初始化失败');
        }
      });

      return {
        activeKey,
        detailVal,
        register,
        registerDetail,
        registerStatus,
        registerMinutes,
        registerForm,
        formatFileLinks,
        registerFormStatus,
        registerFormMinutes,
        eruptAttachment,
        previewShow,
        registerPreviewModal,
        previewUrl,
        previewKey,
        handleSubmit,
        overwriteAction,
        extraAction,
        buttonRenders,
        type,
        handleSubmitStatus,
        handleCancelStatus,
        handleSubmitMinutes,
        handleCancelMinutes,
        descData,
        rowDynamicController,
        handleDetailOk, // 暴露给模板
        handleDetailClose,
      };
    },
  });
</script>

<style scoped lang="less">
  .desc-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;

    td {
      padding: 12px;
      border: 1px solid #e8e8e8;
    }

    .table-header td {
      background-color: #fafafa;
      font-weight: 500;
    }

    .download-btn {
      margin-left: 12px;
      color: #ff0000 !important; // 添加 !important 覆盖所有情况
      padding: 2px 8px;
      border-radius: 4px;
      background: #fff7f3;
      border: 1px solid #ffddd1;
      transition: all 0.3s;

      &:hover {
        color: #ff0000 !important; // 保持红色
        border-color: #ffb08f;
        background: #fff1eb;
      }
    }
    /* 卡片样式完全一致 */
    :deep(.ant-card-head) {
      border-bottom: 0;
      background: #fafafa !important;
    }

    :deep(.ant-descriptions-item-label) {
      background: #fafafa !important;
    }

    .section-title {
      background-color: #f0f2f5;
      font-weight: bold;
      text-align: center;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
    }

    a {
      color: #ff6118;
      text-decoration: none;
    }
  }

  .detail-container {
    padding: 16px;
  }

  .detail-section {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fafafa;
  }

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
    color: #333;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .detail-label {
    font-weight: bold;
    color: #666;
  }

  .detail-value {
    color: #333;
  }

  .detail-content {
    white-space: pre-wrap; /* 保留换行符 */
    padding: 8px;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    color: #333;
  }

  .file-link {
    color: #1890ff;
    margin-right: 12px;

    &:hover {
      text-decoration: underline;
    }
  }

  .no-data {
    color: #999;
    font-style: italic;
  }
</style>
