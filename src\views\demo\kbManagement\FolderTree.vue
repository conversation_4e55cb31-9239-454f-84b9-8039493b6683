<template>
  <div class="m-4 mr-0 overflow-hidden bg-white">
    <BasicTree
      title="目录列表"
      toolbar
      search
      treeWrapperClassName="h-[calc(100%-35px)] overflow-auto"
      :clickRowToExpand="false"
      :treeData="treeData"
      :fieldNames="{ key: 'id', title: 'folderName' }"
      @select="handleSelect"
      :beforeRightClick="getRightMenuList"
      :passive="true"
      :expandedKeys="expandedKeys"
      @update:expandedKeys="(keys) => (expandedKeys = keys)"
    />
    <a-modal
      v-model:visible="renameModalVisible"
      title="重命名文件夹"
      @ok="confirmRename"
      @cancel="handleCancelRename"
      :maskClosable="false"
      :keyboard="false"
      :width="400"
      :centered="true"
      :destroyOnClose="true"
      class="rename-modal"
    >
      <div class="rename-modal-content">
        <div class="current-folder">
          <span class="current-label">当前名称：</span>
          <span class="current-name">{{
            currentRenameNode?.title || currentRenameNode?.folderName || '未知文件夹'
          }}</span>
        </div>
        <a-form-item
          :validate-status="nameError ? 'error' : ''"
          :help="nameError"
          class="rename-form-item"
        >
          <input type="hidden" :value="currentRenameNode?.id" ref="nodeIdInput" />
          <a-input
            v-model:value="newFolderName"
            placeholder="请输入新的文件夹名称"
            :maxLength="300"
            @keyup.enter="confirmRename"
            ref="renameInput"
            class="rename-input"
            :class="{ 'has-error': nameError }"
          />
          <div class="input-tip">
            <span class="char-count">{{ newFolderName.length }}/300</span>
            <span class="tip-text">支持中文、英文、数字和下划线</span>
          </div>
        </a-form-item>
      </div>
      <template #footer>
        <div class="modal-footer">
          <a-button @click="handleCancelRename">取消</a-button>
          <a-button type="primary" @click="confirmRename" :disabled="!!nameError">确定</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>
<script lang="ts">
  import { defineComponent, onMounted, ref, nextTick } from 'vue';
  import { BasicTree, TreeItem } from '/@/components/Tree';
  import { ContextMenuItem } from '/@/components/ContextMenu';
  import {
    getDocumentationTree,
    addDocumentationFolder,
    deleteDocumentationFolder,
    renameDocumentationFolder,
  } from './documentation';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Modal } from 'ant-design-vue';
  import { FolderOutlined } from '@ant-design/icons-vue';

  interface FolderItem extends TreeItem {
    id: string;
    folderName: string;
    children?: FolderItem[];
  }

  export default defineComponent({
    name: 'kbFolderTree',
    components: { BasicTree, FolderOutlined },

    emits: ['select'],
    setup(_, { emit }) {
      const treeData = ref<FolderItem[]>([]);
      const { createMessage } = useMessage();
      const expandedKeys = ref<string[]>([]);
      const renameModalVisible = ref(false);
      const currentRenameNode = ref<any>(null);
      const newFolderName = ref('');
      const nameError = ref('');
      const renameInput = ref();
      const nodeIdInput = ref();

      function handleSelect(keys) {
        if (keys && keys.length > 0) {
          const selectedNode = findNodeById(treeData.value, keys[0]);
          if (selectedNode) {
            emit('select', selectedNode.id);
          }
        }
      }

      function findNodeById(nodes: FolderItem[], id: string): FolderItem | null {
        for (const node of nodes) {
          if (node.id === id) {
            return node;
          }
          if (node.children) {
            const found = findNodeById(node.children, id);
            if (found) {
              return found;
            }
          }
        }
        return null;
      }

      async function handleAddFolder(node: any) {
        try {
          const params = {
            name: '新建文件夹',
            parentId: node ? Number(node.id) : undefined,
            module: 1,
          };
          const res = await addDocumentationFolder(params);
          // 刷新树数据
          await loadTreeData();
          // 如果是在某个节点下添加，展开该节点
          if (node) {
            expandedKeys.value = [...expandedKeys.value, node.id];
          }
          createMessage.success('新增文件夹成功');
        } catch (error: any) {
          console.error('新增文件夹失败:', error);
          createMessage.error('新增文件夹失败: ' + (error.message || '未知错误'));
        }
      }

      async function handleDeleteFolder(node: any) {
        try {
          const folderName = node.title || node.folderName || '未知文件夹';
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除文件夹"${folderName}"吗？删除后无法恢复。`,
            okText: '确定',
            cancelText: '取消',
            async onOk() {
              const params = {
                id: Number(node.id),
              };
              await deleteDocumentationFolder(params);
              // 刷新树数据
              await loadTreeData();
              createMessage.success(`文件夹"${folderName}"删除成功`);
            },
            onCancel() {
              console.log('取消删除');
            },
          });
        } catch (error: any) {
          console.error('删除文件夹失败:', error);
          const folderName = node.title || node.folderName || '未知文件夹';
          createMessage.error(`删除文件夹"${folderName}"失败: ` + (error.message || '未知错误'));
        }
      }

      async function handleRenameFolder(node: any) {
        if (!node || !node.id) {
          createMessage.error('无效的文件夹节点');
          return;
        }
        nodeIdInput.value = node.id;
        currentRenameNode.value = { ...node };
        newFolderName.value = node.title || node.folderName || '';
        nameError.value = '';
        renameModalVisible.value = true;
        await nextTick();
        renameInput.value?.focus();
      }

      function handleCancelRename() {
        renameModalVisible.value = false;
        nameError.value = '';
        newFolderName.value = '';
      }

      async function confirmRename() {
        try {
          nameError.value = '';

          const nodeId = nodeIdInput.value?.value;
          if (!nodeId) {
            nameError.value = '无效的文件夹节点';
            return;
          }

          // 检查输入名称
          const trimmedName = newFolderName.value.trim();
          if (!trimmedName) {
            nameError.value = '文件夹名称不能为空';
            return;
          }
          if (trimmedName.length > 300) {
            nameError.value = '文件夹名称不能超过300个字符';
            return;
          }
          if (
            trimmedName === (currentRenameNode.value?.title || currentRenameNode.value?.folderName)
          ) {
            nameError.value = '新名称不能与当前名称相同';
            return;
          }

          // 特殊字符检查
          const invalidChars = /[\\/:*?"<>|]/g;
          if (invalidChars.test(trimmedName)) {
            nameError.value = '文件夹名称不能包含特殊字符 \\ / : * ? " < > |';
            return;
          }

          const params = {
            id: Number(nodeId),
            name: trimmedName,
          };

          await renameDocumentationFolder(params);
          await loadTreeData();
          createMessage.success(`文件夹重命名为"${trimmedName}"成功`);
          renameModalVisible.value = false;
          newFolderName.value = '';
          currentRenameNode.value = null;
        } catch (error: any) {
          console.error('重命名文件夹失败:', error);
          nameError.value = error.message || '重命名失败，请稍后重试';
        }
      }

      function getRightMenuList(node: any): ContextMenuItem[] {
        return [
          {
            label: '新增文件夹',
            handler: () => handleAddFolder(node),
            icon: 'ant-design:folder-add-outlined',
          },
          {
            label: '重命名',
            handler: () => handleRenameFolder(node),
            icon: 'ant-design:edit-outlined',
          },
          {
            label: '删除',
            handler: () => handleDeleteFolder(node),
            icon: 'ant-design:delete-outlined',
          },
        ];
      }

      async function loadTreeData() {
        try {
          const res = await getDocumentationTree();
          // 转换数据格式以匹配组件要求
          const transformData = (items: any[]): FolderItem[] => {
            return items.map((item) => ({
              id: item.id.toString(),
              key: item.id.toString(),
              folderName: item.name,
              title: item.name,
              children: item.children ? transformData(item.children) : undefined,
            }));
          };
          treeData.value = transformData(res);
          // 获取所有节点的id
          const getAllNodeIds = (items: FolderItem[]): string[] => {
            return items.reduce((acc: string[], item) => {
              acc.push(item.id);
              if (item.children) {
                acc.push(...getAllNodeIds(item.children));
              }
              return acc;
            }, []);
          };
          // 展开所有节点
          expandedKeys.value = getAllNodeIds(treeData.value);
          // 如果有数据，默认选中第一个节点
          if (treeData.value.length > 0) {
            handleSelect([treeData.value[0].id]);
          }
        } catch (error: any) {
          console.error('加载目录树失败:', error);
          // 显示更详细的错误信息
          if (error.response) {
            console.error('错误状态码:', error.response.status);
            console.error('错误信息:', error.response.data);
          } else if (error.request) {
            console.error('请求未收到响应:', error.request);
          } else {
            console.error('请求配置错误:', error.message);
          }
        }
      }

      onMounted(() => {
        loadTreeData();
        // 默认选中ID为1的节点
        handleSelect(['1']);
      });

      return {
        treeData,
        handleSelect,
        getRightMenuList,
        expandedKeys,
        renameModalVisible,
        currentRenameNode,
        newFolderName,
        confirmRename,
        nameError,
        renameInput,
        nodeIdInput,
        handleCancelRename,
      };
    },
  });
</script>

<style scoped>
  .folder-tree {
    height: 100%;
    overflow: auto;
  }

  .rename-modal-content {
    padding: 16px 24px;
  }

  .current-folder {
    margin-bottom: 24px;
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .current-label {
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
  }

  .current-name {
    color: #1890ff;
    font-weight: 500;
  }

  .rename-form-item {
    margin-bottom: 8px;
  }

  .rename-input {
    border-radius: 4px;
    transition: all 0.3s;
    height: 36px;
  }

  .rename-input:hover,
  .rename-input:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .rename-input.has-error {
    border-color: #ff4d4f;
  }

  .rename-input.has-error:hover,
  .rename-input.has-error:focus {
    border-color: #ff4d4f;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }

  .input-tip {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }

  .char-count {
    color: rgba(0, 0, 0, 0.45);
  }

  .tip-text {
    color: rgba(0, 0, 0, 0.45);
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  :deep(.ant-modal-body) {
    padding: 24px;
  }

  :deep(.ant-modal-header) {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-modal-footer) {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
  }

  :deep(.ant-modal-close) {
    top: 16px;
  }

  :deep(.ant-modal-close-x) {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }

  :deep(.ant-modal-content) {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
</style>
