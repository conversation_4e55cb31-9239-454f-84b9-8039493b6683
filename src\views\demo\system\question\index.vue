<template>
  <div>
    <erupt-table
      title="问题管理"
      ref="eruptTable"
      class-name="Question"
      :seq="true"
      :is-process="true"
      :overwrite-action="overwriteAction"
      :row-dynamic-controller="rowDynamicController"
      :pre-conditions="preCondition"
      :query-sort="querySort"
    >
      <template #level="{ row }">
        <div v-if="row.level == 'A'" style="align-self: center">
          <a-tag color="green" style="width: 50px; text-align: center">
            {{ row.level }}
          </a-tag>
        </div>
        <div v-if="row.level == 'B'" style="align-self: center">
          <a-tag color="blue" style="width: 50px; text-align: center">
            {{ row.level }}
          </a-tag>
        </div>
        <div v-if="row.level == 'C'" style="align-self: center">
          <a-tag color="red" style="width: 50px; text-align: center">
            {{ row.level }}
          </a-tag>
        </div>
      </template>

      <template #issafe="{ row }">
        <div v-if="row.issafe == '是'" style="align-self: center">
          <a-tag color="green" style="width: 50px; text-align: center">
            {{ row.issafe }}
          </a-tag>
        </div>
        <div v-if="row.issafe == '否'" style="align-self: center">
          <a-tag color="red" style="width: 50px; text-align: center">
            {{ row.issafe }}
          </a-tag>
        </div>
      </template>
      <template #approveStatus="{ row }">
        <div v-if="row.approveStatus == '待提报'" style="align-self: center">
          <a-tag color="green" style="width: 50px; text-align: center">
            {{ row.approveStatus }}
          </a-tag>
        </div>
        <div v-if="row.approveStatus == '审核中'" style="align-self: center">
          <a-tag color="blue" style="width: 50px; text-align: center">
            {{ row.approveStatus }}
          </a-tag>
        </div>
        <div v-if="row.approveStatus == '完成'" style="align-self: center">
          <a-tag color="red" style="width: 50px; text-align: center">
            {{ row.approveStatus }}
          </a-tag>
        </div>
        <div v-if="row.approveStatus == '已取消'" style="align-self: center">
          <a-tag color="#8b949e" style="width: 50px; text-align: center">
            {{ row.approveStatus }}
          </a-tag>
        </div>
      </template>
    </erupt-table>
    <BasicModal
      @register="registerModal"
      title="预览文件"
      :width="1000"
      :height="600"
      :footer="null"
    >
      <iframe :key="key" :src="modalUrl" width="1000" height="800"></iframe>
    </BasicModal>

    <BasicModal
      defaultFullscreen
      @register="register"
      :showFooter="true"
      :title="type"
      width="50%"
      @ok="handleSubmit"
      okText="保存"
    >
      <BasicForm @register="registerForm"></BasicForm>
      <template #appendFooter>
        <a-popconfirm title="是否确认提报" @confirm="onConfirm">
          <a-button> 提报</a-button>
        </a-popconfirm>
      </template>
    </BasicModal>

    <BasicModal
      @register="register1"
      :showFooter="true"
      title="请编辑所属位置"
      width="40%"
      @ok="handleSubmit1"
      @cancel="handleCancel1"
    >
      <template #centerFooter>
        <a-button @click="clearLast"> 清除上一条</a-button>
      </template>
      <BasicForm @register="registerForm1" style="height: 350px"></BasicForm>
    </BasicModal>
    <BasicModal
      defaultFullscreen
      v-bind="$attrs"
      @register="register2"
      :footer="null"
      width="80%"
      title="详情"
    >
      <div style="display: flex; flex-direction: row">
        <div style="width: 78%">
          <a-card
            :head-style="headerStyle"
            :body-style="borderStyle"
            :bordered="true"
            title="问题信息"
          >
            <!--          <a-descriptions
                        layout="vertical"
                        :column="4"
                        bordered
                        :label-style="{ fontWeight: 'bold' }"
                      >-->

            <a-descriptions
              layout="vertical"
              :column="4"
              bordered
              :label-style="{ fontWeight: 'bold' }"
            >
              <a-descriptions-item label="问题编码" :contentStyle="contentStyle"
                >{{ detailData['code'] }}
              </a-descriptions-item>
              <a-descriptions-item label="问题类型" :contentStyle="contentStyle"
                >{{ formatTypeName(detailData['questionType']) }}
              </a-descriptions-item>
              <a-descriptions-item label="所属线路" :contentStyle="contentStyle"
                >{{ formatLineName(detailData['lineIds']) }}
              </a-descriptions-item>
              <a-descriptions-item label="所属位置" :contentStyle="contentStyle"
                >{{ detailData['bidSection'] }}
              </a-descriptions-item>

              <a-descriptions-item label="所属专业" :contentStyle="contentStyle"
                >{{ formatTypeId(detailData['typeId']) }}
              </a-descriptions-item>
              <a-descriptions-item label="影响安全" :contentStyle="contentStyle"
                >{{ formatIsSafe(detailData['issafe']) }}
              </a-descriptions-item>
              <a-descriptions-item label="重要程度" :contentStyle="contentStyle"
                >{{ detailData['level'] }}
              </a-descriptions-item>
              <a-descriptions-item label="专业管理单位" :contentStyle="contentStyle"
                >{{ detailData['manageDeptId_name'] }}
              </a-descriptions-item>
              <a-descriptions-item label="当前状态" :contentStyle="contentStyle"
                >{{ detailData['approveStatus'] }}
              </a-descriptions-item>
              <a-descriptions-item label="问题状态"
                >{{ detailData['questionStatus'] }}
              </a-descriptions-item>
              <a-descriptions-item label="问题简述" span="2"
                >{{ detailData['briefDes'] }}
              </a-descriptions-item>
              <a-descriptions-item label="完成状况" :contentStyle="contentStyle"
                >{{ detailData['completeStatus'] }}
              </a-descriptions-item>
              <a-descriptions-item label="问题详述" span="4"
                >{{ detailData['des'] }}
              </a-descriptions-item>
              <a-descriptions-item label="整改建议" span="4"
                >{{ detailData['processDes'] }}
              </a-descriptions-item>
            </a-descriptions>

            <!--



                      </a-descriptions>-->
          </a-card>
          <a-card :head-style="headerStyle" :body-style="borderStyle" :bordered="true" title="附件">
            <a-descriptions
              layout="vertical"
              :column="2"
              bordered
              :label-style="{ fontWeight: 'bold' }"
            >
              <a-descriptions-item label="附件">
                <div
                  v-if="detailData['enclosure']"
                  v-for="(item, index) in detailData['enclosure'].split('|')"
                >
                  <div
                    ><a @click="previewShow(item, index)">{{
                      item.substring(item.lastIndexOf('/') + 1)
                    }}</a>
                    <a
                      style="margin-left: 10px; color: #ff6118"
                      :href="eruptAttachment + item"
                      target="_blank"
                      >下载</a
                    ></div
                  >
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="建议申报表">
                <div
                  v-if="detailData['importantEnclosure']"
                  v-for="(item, index) in detailData['importantEnclosure'].split('|')"
                >
                  <div
                    ><a @click="previewShow(item, index + 'shenbao')">{{
                      item.substring(item.lastIndexOf('/') + 1)
                    }}</a>
                    <a
                      style="margin-left: 10px; color: #ff6118"
                      :href="eruptAttachment + item"
                      target="_blank"
                      >下载</a
                    ></div
                  >
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
          <a-card
            :head-style="headerStyle"
            :body-style="borderStyle"
            :bordered="true"
            title="提报信息"
          >
            <!--          <a-descriptions
                        layout="vertical"
                        :column="4"
                        bordered
                        :label-style="{ fontWeight: 'bold' }"
                      >-->

            <a-descriptions
              layout="vertical"
              :column="4"
              bordered
              :label-style="{ fontWeight: 'bold' }"
            >
              <a-descriptions-item label="提报人" span="1" :contentStyle="contentStyle"
                >{{ detailData['createUserId'] }}
              </a-descriptions-item>
              <a-descriptions-item label="提报时间" span="1" :contentStyle="contentStyle"
                >{{ detailData['createTime'] }}
              </a-descriptions-item>
              <a-descriptions-item label="提报部门" span="1" :contentStyle="contentStyle"
                >{{ detailData['reportDeptId_name'] }}
              </a-descriptions-item>
            </a-descriptions>

            <!--            <a-descriptions-item label="问题编码">{{ detailData['code'] }}</a-descriptions-item>
                        <a-descriptions-item label="问题类型">{{
                          formatTypeName(detailData['questionType'])
                        }}</a-descriptions-item>
                        <a-descriptions-item label="问题简述">{{ detailData['briefDes'] }}</a-descriptions-item>
                        <a-descriptions-item label="重要程度">{{ detailData['level'] }}</a-descriptions-item>
                        <a-descriptions-item label="所属线路">{{
                          formatLineName(detailData['lineIds'])
                        }}</a-descriptions-item>

                        <a-descriptions-item label="所属位置">{{
                          detailData['bidSection']
                        }}</a-descriptions-item>

                        <a-descriptions-item label="所属专业">{{
                          formatTypeId(detailData['typeId'])
                        }}</a-descriptions-item>
                        <a-descriptions-item label="影响安全">{{
                          formatIsSafe(detailData['issafe'])
                        }}</a-descriptions-item>
                        <a-descriptions-item label="专业管理单位">{{
                          detailData['manageDeptId_name']
                        }}</a-descriptions-item>
                        <a-descriptions-item label="当前状态">{{
                          detailData['approveStatus']
                        }}</a-descriptions-item>
                        <a-descriptions-item label="问题详述" span="4">{{
                          detailData['des']
                        }}</a-descriptions-item>
                        <a-descriptions-item label="整改建议" span="4">{{
                          detailData['processDes']
                        }}</a-descriptions-item>
                        <a-descriptions-item label="附件"
                          ><div v-html="formatFileLinks(detailData['enclosure'])"></div
                        ></a-descriptions-item>
                        <a-descriptions-item label="建议申报表"
                          ><div v-html="formatFileLinks(detailData['importantEnclosure'])"></div
                        ></a-descriptions-item>

                      </a-descriptions>-->
          </a-card>
          <a-card
            :head-style="headerStyle"
            :body-style="borderStyle"
            :bordered="true"
            title="整改记录"
          >
            <a-table
              :pagination="false"
              :columns="[
                {
                  name: '',
                  dataIndex: '',
                  key: '',
                },
                {
                  title: '处理人',
                  dataIndex: 'createUserName',
                  key: 'createUserName',
                },
                {
                  title: '问题状态',
                  dataIndex: 'opreation',
                  key: 'opreation',
                },
                {
                  title: '整改意见',
                  dataIndex: 'note',
                  key: 'note',
                },
                {
                  title: '处理时间',
                  dataIndex: 'createDateTime',
                  key: 'createDateTime',
                  width: 600,
                },
              ]"
              :data-source="detailData.questionItems"
            >
            </a-table>
          </a-card>
        </div>
        <div style="width: 20%; height: 100%">
          <a-card :bordered="false" title="流程信息">
            <a-steps direction="vertical" :current="currentStep">
              <a-step
                style="margin: 3%"
                v-for="(item, index) in approveRecords"
                :key="index"
                :status="!item.action ? 'process' : 'finish'"
                :title="item.name"
                :style="!item.action ? { color: 'green' } : {}"
              >
                <template #description>
                  <div :style="{ color: !item.action ? 'rgba(0, 0, 0, 0.45)' : 'black' }">
                    <div> {{ !item.action ? '待办理人' : '办理人' }}：{{ item.person }}</div>
                    <div v-show="item.action">处理方式：{{ item.action }}</div>
                    <div v-show="item.opinion">审批意见：{{ item.opinion }}</div>
                    <p v-show="item.createDateTime">办理时间：{{ item.createDateTime }}</p>
                  </div>
                </template>
              </a-step>
            </a-steps>
          </a-card>
        </div>
      </div>
      <!--        <a-collapse-panel key="2" header="审批历史">

        </a-collapse-panel>-->
    </BasicModal>
  </div>
</template>

<script lang="tsx">
  import eruptTable from '/@/components/EruptTable/eruptTable';
  import { defineComponent, ref, h, onMounted, computed } from 'vue';
  import { NColorPicker } from 'naive-ui';
  import { Card, Table } from 'ant-design-vue';
  import { buildApi, getOne, save, update } from '/@/api/erupt/erupt';
  import { useMessage } from '/@/hooks/web/useMessage';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useForm } from '/@/components/Form';
  import {
    getComponent,
    getComponentProps,
    getSearchComponent,
  } from '/@/components/EruptTable/componets';

  import { GetDeptBySpecical, GetOptions, getNextNode } from '/@/views/demo/system/question/api';
  import { useRoute } from 'vue-router';
  import { getApprovalRecordsByBussiness } from '/@/api/process/process';
  import { useUserStore } from '/@/store/modules/user';
  import Template from '/@/views/demo/system/quality/template.vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Base64 } from 'js-base64';

  export default defineComponent({
    name: 'Question',
    components: {
      Template,
      BasicModal,
      BasicForm,
      eruptTable,
      NColorPicker,
      [Card.name]: Card,
      [Table.name]: Table,
    },
    setup: function () {
      const [register, { openModal, closeModal }] = useModal();
      const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
      const [registerModal, { openModal: openModalPreview }] = useModal();
      //详情modal
      const activeKey = ref('1');
      const detailData = ref({});
      const lineOptions = ref([]);
      const typeOptions = ref([]);
      const currentStep = ref<number>(0);
      const typeIdOptions = ref([]);
      const isSafeOptions = ref([]);
      const approveRecords = ref([]);
      const modalUrl = ref('');
      const key = ref(0);
      const globSetting = useGlobSetting();
      const baseUrl = import.meta.env.VITE_GLOB_ERUPT_ATTACHMENT;
      const eruptAttachment = globSetting?.eruptAttachment;
      const [register2, { openModal: openModal2, closeModal: closeModal2 }] = useModal();
      const eruptFieldModels = ref([]);
      const editSchemas = ref([]);
      const extraSchemas = ref([]);
      const extraTitle = ref('');
      const base = ref({ id: '', version: '' });
      const isqj = ref(false);
      const preCondition = new Map([]);
      const route = useRoute();
      const line = computed(() => route.query.line);
      const dept = computed(() => route.query.dept);
      const state = computed(() => route.query.state);
      const [
        registerForm,
        { resetFields, setFieldsValue, updateSchema, validate, getFieldsValue },
      ] = useForm({
        labelWidth: 100,
        wrapperCol: { span: 24 },
        schemas: editSchemas,
        showActionButtonGroup: false,
      });

      const [
        registerForm1,
        {
          resetFields: resetFields1,
          setFieldsValue: setFieldsValue1,
          updateSchema: updateSchema1,
          validate: validate1,
        },
      ] = useForm({
        labelWidth: 100,
        schemas: extraSchemas,
        showActionButtonGroup: false,
      });
      const eruptTable = ref();
      const type = ref('新增');
      const dynamicController = {
        level: (form, e) => {
          const { formModel, formActionType } = form;
          if (e) {
            console.log(e, formModel);
          }
        },
      };
      const rowDynamicController = {
        edit: (row) => {
          if (row.approveStatus == '待提报') {
            return true;
          } else {
            return false;
          }
        },
        delete: (row) => {
          if (row.approveStatus == '待提报') {
            return true;
          } else {
            return false;
          }
        },
      };
      const overwriteAction = {
        add: () => {
          type.value = '新增';
          setTimeout(() => {
            resetFields();
            openModal();
          }, 200);
        },

        edit: async (row) => {
          type.value = '编辑';
          const res = await getOne('Question', row.id);
          //base.value.id = res.id
          //base.value.version = res.version
          base.value = res;
          setTimeout(() => {
            resetFields();
            openModal();
            setTimeout(() => {
              setFieldsValue(res);
            }, 20);
          }, 200);
        },

        detail: async (row) => {
          const res = await getOne('Question', row.id); // 获取详情数据
          const {
            eruptModel: { eruptFieldModels: fieldModels },
          } = await buildApi('Question');
          eruptFieldModels.value = fieldModels;
          // 提取线路和区域的选择项
          const lineField = fieldModels.find((item) => item.fieldName === 'lineIds');
          const typeField = fieldModels.find((item) => item.fieldName === 'questionType');
          const typeIdField = fieldModels.find((item) => item.fieldName === 'typeId');
          const isSafeField = fieldModels.find((item) => item.fieldName === 'issafe');
          lineOptions.value = lineField?.componentValue || [];
          typeOptions.value = typeField?.componentValue || [];
          typeIdOptions.value = typeIdField?.componentValue || [];
          isSafeOptions.value = isSafeField?.componentValue || [];
          detailData.value = res;

          getApprovalRecordsByBussiness('Question' + '.' + row.id).then((res) => {
            getNextNode(row.id).then((r) => {
              if (r) {
                res.push({ person: r.users, name: r.task });
                approveRecords.value = res;
                currentStep.value = approveRecords.value.length - 2;
              } else {
                approveRecords.value = res;
              }
            });
          });
          setTimeout(() => {
            openModal2();
          }, 200);
        },
      };

      const formatLineName = (lineIds) => {
        const matchedLine = lineOptions.value.find((line) => line.value === lineIds);
        return matchedLine ? matchedLine.label : '-';
      };
      const formatTypeName = (questionType) => {
        const matchedType = typeOptions.value.find((type) => type.value === questionType);
        return matchedType ? matchedType.label : '-';
      };
      const formatTypeId = (typeId) => {
        const matchedTypeId = typeIdOptions.value.find((type) => type.value === typeId);
        return matchedTypeId ? matchedTypeId.label : '-';
      };
      const formatIsSafe = (isSafe) => {
        const matchedIsSafe = isSafeOptions.value.find((safe) => safe.value === isSafe);
        return matchedIsSafe ? matchedIsSafe.label : '-';
      };

      function previewShow(downloadUrl, index) {
        downloadUrl = encodeURIComponent(Base64.encode(globSetting.eruptAttachment + downloadUrl));
        const userStore = useUserStore();
        const watermarkTxt = userStore.userInfo.username + ' ' + userStore.userInfo.realName;
        modalUrl.value = ''; // 清空旧缓存
        modalUrl.value =
          globSetting.previewUrl +
          '/onlinePreview?url=' +
          downloadUrl +
          '&watermarkTxt=' +
          watermarkTxt; // 设置预览 URL
        key.value = index; // 重新加载 iframe
        openModalPreview(true); // 打开 modal 窗口
      }

      const formatFileLinks = (filePaths) => {
        if (!filePaths) return ''; // 如果文件路径为空，返回空字符串

        return filePaths
          .split('|')
          .map((filePath) => {
            const fileName = filePath.substring(filePath.lastIndexOf('/') + 1); // 提取文件名
            const fileUrl = `${baseUrl}${filePath}`; // 拼接完整的URL
            return `<div><a onClick="preview()">${fileName}</a> <a style="margin-left: 10px;color: #ff6118" href="${fileUrl}" download="${fileName}" target="_blank">下载</a></div>`;
          })
          .join(' </br> '); // 用</br>连接多个文件链接
      };
      onMounted(() => {
        const userStore = useUserStore();
        if (state.value != null && state.value != '' && state.value != '总数') {
          preCondition.set('approveStatus', state.value);
        }
        if (line.value != null && line.value != '') {
          preCondition.set('lineIds', line.value);
        }
        if (dept.value != null && dept.value != '' && dept.value != '新线管理部') {
          preCondition.set('mainDeptNames', dept.value);
        } else if (userStore.userInfo.dept != '新线管理部') {
          preCondition.set('mainDeptNames', userStore.userInfo.dept);
        }
        buildApi('Question').then((res) => {
          const {
            eruptModel: { eruptFieldModels, eruptJson },
            tabErupts,
            power,
          } = res;
          let qs = [];
          let edits = [];
          let adds = [];
          eruptFieldModels.forEach((item) => {
            const key = item.fieldName;
            const title = item.eruptFieldJson.edit.title;

            //  formState[key] = null
            if (item.eruptFieldJson.edit.search.value) {
              qs.push(getSearchComponent(key, title, item, 4, 'Question'));
            }
            if (item.eruptFieldJson.edit.show.edit_show && key !== 'id') {
              if (
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
                item.eruptFieldJson.edit.type !== 'TAB_TREE'
              ) {
                const e = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, 'Question'),
                      disabled: item.eruptFieldJson.edit.readOnly.edit,
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };
                console.log(title, getComponentProps(item, 'Question'));
                const a = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, 'Question'),
                      disabled: item.eruptFieldJson.edit.readOnly.add,
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };
                edits.push(e);
                adds.push(a);
                console.log('edits', edits);
              } else {
              }
            }
          });
          qs.push({
            span: 4,
            align: 'right',
            Question: '!pr-0',
            itemRender: {
              name: 'AButtonGroup',
              children: [
                {
                  props: { type: 'primary', content: '查询', htmlType: 'submit' },
                  attrs: { class: 'mr-2' },
                },
                { props: { type: 'default', htmlType: 'reset', content: '重置' } },
              ],
            },
          });
          edits.forEach((item) => {
            if (item.field == 'importantEnclosure') {
              debugger;
              const props = { ...item.componentProps() };
              item.ifShow = ({ values }) => {
                console.log(values);
                return values.level == 'A';
              };

              item.componentProps = ({ formModel }) => {
                return {
                  ...props,
                  required: true,
                  show: false,
                };
              };
              console.log('importantEnclosure', item);
            }
            if (item.field == 'typeId') {
              const props = { ...item.componentProps() };
              item.componentProps = ({ formModel }) => {
                return {
                  ...props,
                  onChange: async (e) => {
                    if (e !== 'qita') {
                      console.log(formModel);
                      updateSchema({
                        field: 'manageDeptId',
                        componentProps: {
                          disabled: true,
                        },
                      });
                    } else {
                      updateSchema({
                        field: 'manageDeptId',
                        componentProps: {
                          disabled: false,
                        },
                      });
                    }
                    const v = await GetDeptBySpecical(e);
                    setFieldsValue({
                      manageDeptId: v,
                    });
                  },
                };
              };
            }
            if (item.field == 'bidSection') {
              const props = { ...item.componentProps() };
              item.componentProps = ({ formModel }) => {
                return {
                  ...props,
                  readOnly: true,
                  onClick: async () => {
                    const arr = await GetOptions(formModel.lineIds);
                    const schemas = [
                      {
                        field: 'isqj',
                        label: '是否区间',
                        component: 'Select',
                        colProps: { span: 14 },
                        componentProps: {
                          onChange: (e) => {
                            isqj.value = e;
                          },
                          options: [
                            { label: '是', value: true },
                            { label: '否', value: false },
                          ],
                        },
                      },
                      {
                        field: 'isContain1',
                        label: '（包含本站）',
                        required: true,
                        component: 'RadioGroup',
                        ifShow: ({ values }) => {
                          return !!values.isqj;
                        },
                        componentProps: {
                          options: [
                            {
                              label: '是',
                              value: true,
                            },
                            {
                              label: '否',
                              value: false,
                            },
                          ],
                        },
                        colProps: { span: 12 },
                      },
                      {
                        field: 'siteName',
                        label: '车站',
                        required: true,
                        component: 'Select',
                        colProps: { span: 12 },
                        componentProps: {
                          options: arr,
                          // not request untill to select
                          placeholder: '请选择车站',
                        },
                      },
                      {
                        field: 'isContain2',
                        label: '（包含本站）',
                        required: true,
                        component: 'RadioGroup',
                        ifShow: ({ values }) => {
                          return !!values.isqj;
                        },
                        componentProps: {
                          options: [
                            {
                              label: '是',
                              value: true,
                            },
                            {
                              label: '否',
                              value: false,
                            },
                          ],
                        },
                        colProps: { span: 12 },
                      },
                      {
                        field: 'siteName1',
                        label: '车站',
                        required: true,
                        component: 'Select',
                        colProps: { span: 12 },
                        ifShow: ({ values }) => {
                          return !!values.isqj;
                        },
                        componentProps: {
                          options: arr,
                          // not request untill to select
                          placeholder: '请选择车站',
                        },
                      },
                      {
                        field: 'direction',
                        label: '上下行',
                        required: true,
                        component: 'Select',
                        colProps: { span: 12 },
                        ifShow: ({ values }) => {
                          return !!values.isqj;
                        },
                        componentProps: {
                          options: [
                            { label: '上行', value: '上行' },
                            { label: '下行', value: '下行' },
                          ],
                          // not request untill to select
                          placeholder: '请选择车站',
                        },
                      },
                    ];
                    extraSchemas.value = schemas;
                    openModal1();
                  },
                };
              };
            }
          });
          editSchemas.value = edits;

          //gridOptions.columns = columns
          //search()
        });
      });
      const { createMessage } = useMessage();

      return {
        onConfirm: async () => {
          debugger;
          const v = await validate();
          const value = { ...base.value, ...v };
          if (!value.version) delete value.version;
          if (!value.id) delete value.id;
          value.approveFlag = 1;
          if (value.id) {
            const { status, message } = await update('Question', value, null);
            if (status == 'SUCCESS') {
              createMessage.success('提报成功');
              closeModal();
              eruptTable.value.refresh();
            } else {
              createMessage.error('提报失败:' + message);
            }
          } else {
            const { status, message } = await save('Question', value, null);
            if (status == 'SUCCESS') {
              createMessage.success('提报成功');
              closeModal();
              eruptTable.value.refresh();
            } else {
              createMessage.error('提报失败:' + message);
            }
          }
        },
        dynamicController,
        overwriteAction,
        rowDynamicController,
        eruptTable,
        extraTitle,
        type,
        base,
        eruptAttachment,
        register,
        openModal,
        closeModal,
        openModal1,
        closeModal1,
        activeKey,
        openModal2,
        closeModal2,
        register2,
        eruptFieldModels,
        openModalPreview,
        detailData,
        approveRecords,
        formatLineName,
        formatTypeName,
        formatTypeId,
        formatIsSafe,
        formatFileLinks,
        register1,
        previewShow,
        registerModal,
        registerForm,
        resetFields,
        validate,
        registerForm1,
        resetFields1,
        validate1,
        setFieldsValue,
        currentStep,
        modalUrl,
        key,
        handleSubmit: async () => {
          const value = await validate();
          if (type.value == '新增') {
            const { status, message } = await save('Question', value, null);
            if (status == 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              closeModal();
              eruptTable.value.refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          }
          if (type.value == '编辑') {
            const { status, message } = await update('Question', { ...base.value, ...value }, null);
            if (status == 'SUCCESS') {
              createMessage.success('保存成功');
              resetFields();
              closeModal();
              eruptTable.value.refresh();
            } else {
              createMessage.error('保存失败:' + message);
            }
          }
        },
        handleSubmit1: async () => {
          if (isqj.value == false) {
            const value = await validate1();
            const v = getFieldsValue();
            if (v.bidSection) {
              setFieldsValue({ bidSection: v.bidSection + '\n\r' + value.siteName });
            } else {
              setFieldsValue({ bidSection: value.siteName });
            }

            resetFields1();
            closeModal1();
          }
          if (isqj.value == true) {
            const value = await validate1();
            debugger;
            const v = getFieldsValue();
            const left = value.siteName + (value.isContain1 ? '（含）' : '');
            const right = value.siteName1 + (value.isContain2 ? '（含）' : '');
            const result = left + '-' + right + '-' + value.direction;
            if (v.bidSection) {
              setFieldsValue({ bidSection: v.bidSection + '\n\r' + result });
            } else {
              setFieldsValue({ bidSection: result });
            }

            resetFields1();
            closeModal1();
          }
        },
        handleCancel1: () => {
          resetFields1();
        },
        clearLast: async () => {
          const value = getFieldsValue();
          const arr = value.bidSection.split('\n\r');
          let nvalue = arr[0];

          for (var i = 0; i < arr.length; i++) {
            if (i != 0 && i != arr.length - 1) nvalue += '\n\r' + arr[i];
          }
          setFieldsValue({ bidSection: nvalue });
          resetFields1();
          closeModal1();
        },
        getFieldsValue,
        preCondition,
        querySort: (querys) => {
          // 线路、提报部门、重要程度、所属专业
          let map = new Map([]);
          map.set('所属线路', 4);
          map.set('提报部门', 3);
          map.set('重要程度', 2);
          map.set('所属专业', 1);
          querys.sort(function (a, b) {
            //callback
            const va = map.get(a.title) ? map.get(a.title) : 0;
            const vb = map.get(b.title) ? map.get(b.title) : 0;
            console.log(a, b, vb - va);
            return vb - va;
          });
          console.log('afterSort', querys);
          return querys;
        },
        borderStyle: {
          border: '1px solid #e8e8e8',
        },
        headerStyle: {
          backgroundColor: '#fafafa',
          color: '#535151',
          fontWeight: 'bold',
          fontSize: '12',
          textAlign: 'left',
        },
        contentStyle: {
          width: '130px',
          wordBreak: 'break-all',
          whiteSpace: 'normal',
          display: 'inline-block',
        },
        /* cellStyle: ({ row, column }) => {
        if (column.field === 'level') {
          return {
            backgroundColor: '#000000', /!* 黑色背景 *!/
          color:' #FFFFFF',           /!* 白色文字 *!/
          borderRadius: '4px',        /!* 圆角 *!/
          fontSize: '14px',
            align: 'center',
            backgroundSize: '50% 50%'
          }
        }
      }*/
      };
    },
  });
</script>

<style>
  .ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: #f0f0f0;
  }

  .ant-card-body {
    padding: 0px;
  }
</style>
