<template>
  <div>
    <erupt-table class-name="OperationTemplate" :extra-action="extraAction"></erupt-table>
    <BasicModal
      @register="register2"
      :showFooter="true"
      title="复制模板"
      @ok="handleSubmit2"
      @close="handleCancel2"
    >
      <BasicForm style="height: 350px" @register="registerForm2"></BasicForm>
    </BasicModal>
    <BasicModal
      @register="register"
      :showFooter="true"
      title="请选择线路"
      @ok="handleSubmit"
      @close="handleCancel"
    >
      <BasicForm style="height: 350px" @register="registerForm"></BasicForm>
    </BasicModal>
    <BasicModal
      @register="register1"
      :showFooter="true"
      title="请上传附件"
      @ok="handleSubmit1"
      @close="handleCancel1"
    >
      <BasicForm style="height: 300px" @register="registerForm1"></BasicForm>
    </BasicModal>
  </div>
</template>
<script setup lang="ts">
import {onMounted, ref} from 'vue';
import eruptTable from '../../../../components/EruptTable/eruptTable';
import {useRouter} from 'vue-router';
import {useModal} from '/@/components/Modal';
import BasicForm from '/@/components/Form/src/BasicForm.vue';
import BasicModal from '/@/components/Modal/src/BasicModal.vue';
import {useForm} from '/@/components/Form';
import {
  copyTemplate,
  getOptionsApi,
  issuedApi,
  persistApi
} from '/@/views/demo/system/operation/api/operation';
import {useMessage} from '/@/hooks/web/useMessage';
import {useUserStore} from '/@/store/modules/user';
import {useWatermark} from '/@/hooks/web/useWatermark';

      const userStore = useUserStore();
      const { setWatermark, clear } = useWatermark();
      const { createConfirm } = useMessage();
      const { notification } = useMessage();
      onMounted(() => {
        setWatermark(userStore.getUserInfo.username);
      });
      const router = useRouter();
      const [register, { openModal, closeModal }] = useModal();
      const schema = ref([
        {
          field: 'file',
          label: '附件',
          required: true,
          component: 'Attachment',
          colProps: { span: 12 },
        },
      ]);
      const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
      const [register2, { openModal: openModal2, closeModal: closeModal2 }] = useModal();
      const { createMessage } = useMessage();
      const rowId = ref();
      const lineName = ref();
      const [registerForm, { resetFields, validate }] = useForm({
        labelWidth: 100,
        schemas: [
          {
            field: 'lineName',
            label: '线路',
            required: true,
            component: 'ApiSelect',
            colProps: { span: 16 },
            componentProps: {
              api: getOptionsApi,
              resultField: 'list',
              labelField: 'label',
              valueField: 'value',
              placeholder: '请选择线路',
            },
          },
        ],
        showActionButtonGroup: false,
      });

      const [
        registerForm1,
        { resetFields: resetFields1, setFieldsValue: setFieldsValue1, validate: validate1 },
      ] = useForm({
        labelWidth: 100,
        schemas: schema,
        showActionButtonGroup: false,
      });

      const [registerForm2, { resetFields: resetFields2, setFieldsValue: setFieldsValue2, validate: validate2 },] = useForm({
        labelWidth: 100,
        schemas: [
          {
            field: 'code',
            label: '编码',
            component: 'Input',
            colProps: { span: 21 },
            componentProps: {
              placeholder: '请输入编码',
            },
          },
          {
            field: 'name',
            label: '名称',
            component: 'Input',
            colProps: { span: 21 },
            componentProps: {
              placeholder: '请输入名称',
            },
          },
          {
            field: 'lineId',
            label: '线路',
            component: 'ApiSelect',
            colProps: { span: 21 },
            componentProps: {
              api: getOptionsApi,
              resultField: 'list',
              labelField: 'label',
              valueField: 'value',
              placeholder: '请选择线路',
            },
          },
        ],
        showActionButtonGroup: false,
      });

      const handleSubmit = async () => {
        if(lineName.value==undefined || lineName.value=='' || lineName.value==null){
          createMessage.error('请先配置线路再下发')
        }else{
        createMessage.success('正在下发')
        issuedApi(rowId.value).then((res) => {
          if (res) {
            createMessage.success('下发完成');
          } else {
            createMessage.error('下发失败');
          }
          closeModal();
        });
        }
      };

      const handleSubmit1 = () => {
        persistApi('quaTpl_' + rowId.value).then((res) => {
          if (res) {
            createMessage.success('导入完成');
          } else {
            createMessage.success('导入完成');
          }
          closeModal1();
        });
      };
      function refresh() {
        document.querySelector('button[content="查询"]').click();
      }

      const handleSubmit2 = async () => {
        const data = await validate2();
        createMessage.success('正在复制')
        closeModal2();
        copyTemplate(rowId.value, data).then((res) => {
          if (res) {
            createMessage.success('复制成功');
          } else {
            createMessage.error('复制失败');
          }
          resetFields2();
          refresh();
        });
      }
      const handleCancel = () => {};
      const handleCancel1 = () => {};
      const handleCancel2 = () => {};

      const extraAction = {
        nums: 3,
        actions: (row) => {
          return [
            {
              label: '明细',
              onClick: () => {
                router.push({ path: 'operation/tpl/items/' + row.row.id, query: row.row });
              },
            },
            {
              label: '下发',
              onClick: () => {
                createConfirm({
                  title: '确认下发',
                  content: '确定要下发吗？',
                  onOk() {
                    rowId.value = row.row.id;
                    lineName.value = row.row.lineId;
                    handleSubmit();
                  },
                  iconType: 'warning',
                });
              },
            },
            {
              label: '复制模板',
              onClick: () => {
                rowId.value = row.row.id;
                openModal2();
              },
            },
            /*{
              label: '导入',
              onClick: () => {
                rowId.value = row.row.id;
                schema.value = [
                  {
                    field: 'file',
                    label: '附件',
                    required: true,
                    component: 'Attachment',
                    colProps: { span: 12 },
                    componentProps: {
                      multiple: false,
                      maxNumber: 1,
                      accept: ['xlsx,xls'],
                      maxSize: 50,
                      api: getUpLoadApi(row.row.id),
                    },
                  },
                ];
                openModal1();
              },
            },*/
          ];
        },
      };
</script>
