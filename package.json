{"name": "newline", "version": "2.9.0_03", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "scripts": {"commit": "czg", "bootstrap": "pnpm install", "serve": "npm run dev", "dev": "vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build:release": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode release && esno ./build/script/postBuild.ts", "build:test": "cross-env vite build --mode test && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "test:unit": "jest", "test:gzip": "npx http-server dist --cors --gzip -c-1", "test:br": "npx http-server dist --cors --brotli -c-1", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "prepare": "husky install", "gen:icon": "esno ./build/generate/icon/index.ts"}, "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-vue": "^6.1.0", "@bpmn-io/properties-panel": "^0.19.0", "@iconify/iconify": "^2.2.1", "@logicflow/core": "^1.1.31", "@logicflow/extension": "^1.1.31", "@vue/runtime-core": "3.2.20", "@vue/shared": "3.2.20", "@vueuse/core": "^8.9.4", "@vueuse/shared": "^8.9.4", "@zxcvbn-ts/core": "^2.2.1", "ant-design-vue": "^3.2.15", "axios": "^0.26.1", "bpmn-js": "^9.4.0", "bpmn-js-properties-panel": "^1.5.0", "camunda-bpmn-moddle": "^6.1.2", "codemirror": "^5.65.11", "cropperjs": "^1.5.13", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "diagram-js": "^14.4.0", "echarts": "^5.4.1", "element-plus": "^2.8.0", "exceljs": "^4.3.0", "inherits": "^2.0.4", "intro.js": "^5.1.0", "js-base64": "^3.7.7", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "moment": "^2.30.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "2.0.12", "print-js": "^1.6.0", "qrcode": "^1.5.1", "qs": "^6.11.0", "raw-loader": "^4.0.2", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "sortablejs": "^1.15.0", "spark-md5": "^3.0.2", "tinymce": "^5.10.7", "vditor": "^3.9.0", "vue": "3.2.20", "vue-i18n": "^9.2.2", "vue-json-pretty": "^2.2.3", "vue-router": "^4.1.6", "vue-types": "^4.2.1", "vuedraggable": "^4.1.0", "vxe-table": "^4.3.9", "vxe-table-plugin-export-xlsx": "^3.0.4", "xe-utils": "^3.5.7", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^16.3.0", "@commitlint/config-conventional": "^16.2.4", "@iconify/json": "^2.2.19", "@purge-icons/generated": "^0.8.1", "@types/codemirror": "^5.60.7", "@types/crypto-js": "^4.1.1", "@types/fs-extra": "^9.0.13", "@types/inquirer": "^8.2.5", "@types/intro.js": "^3.0.2", "@types/lodash-es": "^4.17.6", "@types/mockjs": "^1.0.7", "@types/node": "^17.0.45", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.0", "@types/qs": "^6.9.7", "@types/showdown": "^1.9.4", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "@vitejs/plugin-legacy": "^1.8.2", "@vitejs/plugin-vue": "^2.3.4", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue/compiler-sfc": "3.2.20", "@vue/test-utils": "^2.2.10", "autoprefixer": "^10.4.13", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "cz-git": "^1.4.1", "czg": "^1.4.1", "dotenv": "^16.0.3", "eslint": "^8.33.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^8.7.1", "esno": "^0.14.1", "fs-extra": "^10.1.0", "husky": "^7.0.4", "inquirer": "^8.2.5", "less": "^4.1.3", "lint-staged": "12.3.7", "naive-ui": "^2.38.1", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "postcss": "^8.4.21", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.4", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.58.0", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.4", "stylelint-config-recommended": "^7.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^4.9.5", "vite": "^2.9.15", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-mkcert": "^1.13.0", "vite-plugin-mock": "^2.9.6", "vite-plugin-purge-icons": "^0.8.2", "vite-plugin-pwa": "^0.11.13", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-theme": "^0.8.6", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-plugin-windicss": "^1.8.10", "vue-eslint-parser": "^8.3.0", "vue-tsc": "^1.0.24"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}, "repository": {"type": "git", "url": "git+https://github.com/anncwb/vue-vben-admin.git"}, "license": "MIT", "bugs": {"url": "https://github.com/anncwb/vue-vben-admin/issues"}, "homepage": "https://github.com/anncwb/vue-vben-admin", "engines": {"node": "^12 || >=14"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}