<script setup lang="ts">
const { date, title, desc } = defineProps({
  date: {
    default: ''
  },
  title: {
    default: ''
  },
  desc: {
    default: ''
  },
  color: {
    default: '#1677ff'
  },
  subColor: {
    default: '#CCDEFF'
  },
  reverse: {
    default: false
  }
})
</script>

<template>
  <div style="display: flex; width: fit-content; justify-content: flex-end" :style="{flexDirection: reverse ? 'row-reverse' : 'row', textAlign: reverse ? 'right' : 'left'}">
    <div style="display: flex; flex-direction: column; align-items: center; padding: 8px 0 0">
      <div style="width: 16px; height: 16px; display: flex; justify-content: center; align-items: center; border-radius: 50%" :style="{background: subColor}">
        <div :style="{ width: '10px', height: '10px', borderRadius: '50%', background: color }"></div>
      </div>

      <div :style="{ width: '2px', backgroundColor: color }" style="flex: 1"></div>
    </div>
    <div style="margin: 0 10px; padding: 10px 0">
      <div style="font-size: 18px; font-weight: bold" :style="{ color: color }">{{ date }}</div>
      <div style="font-weight: bold">{{ title }}</div>
      <div style="max-width: 300px; text-align: left">{{ desc }}</div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
