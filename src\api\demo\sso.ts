import {
  SysClientPageParams,
  SysClientInfo,
  SysClientVisitPageParams,
  SysClientVisitInfo,
  SysClientListGetResultModel,
  SysClientVisitListGetResultModel,
} from './model/ssoModel';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  SysClientPageList = '/system/getSysClientPageList',
  SysClientVisitPageList = '/system/getSysClientVisitPageList',
  SysClientSaveOrUpdate = '/system/sysclient/saveOrUpdate',
  SysClientDelete = '/system/sysclient/delete',
  SysClientVisitSaveOrUpdate = '/system/sysclientvisit/saveOrUpdate',
  SysClientVisitDelete = '/system/sysclientvisit/delete',
}

export const getSysClientListByPage = (params: SysClientPageParams) =>
  defHttp.post<SysClientListGetResultModel>({ url: Api.SysClientPageList, params });

export const getSysClientVisitListByPage = (params?: SysClientVisitPageParams) =>
  defHttp.post<SysClientVisitListGetResultModel>({ url: Api.SysClientVisitPageList, params });

export const sysClientSaveOrUpdate = (params?: SysClientInfo) =>
  defHttp.post({ url: Api.SysClientSaveOrUpdate, params });

export const sysClientDeleteByIds = (params?: number) =>
  defHttp.post({ url: Api.SysClientDelete + '/', params });

export const sysClientVisitSaveOrUpdate = (params?: SysClientVisitInfo) =>
  defHttp.post({ url: Api.SysClientVisitSaveOrUpdate, params });

export const sysClientVisitDeleteByIds = (params?: number) =>
  defHttp.post({ url: Api.SysClientVisitDelete + '/', params });
