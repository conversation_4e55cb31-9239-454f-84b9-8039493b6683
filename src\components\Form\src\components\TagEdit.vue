<script lang="ts">
import {Tag} from 'ant-design-vue'
import {defineComponent, watch, ref, nextTick} from 'vue';
export default defineComponent({
  name: "TagEdit",
  components: {ATag: Tag},
  props: { value:{type:String}, joinSeparator: {type:String}},
  emits: ['change', 'update:value'],
  setup(props, { emit }) {
    const {joinSeparator} = props
    const tagsRef = ref([])
    const inputVisible =ref(false)
    const inputValue = ref('')
    const inputRef = ref();

    watch(
      () => props.value,
      (val) => {
        if (val) {
          tagsRef.value = val.split(joinSeparator)
        } else {
          tagsRef.value = []
        }
      },
      { deep: true },
    );

    return {
      tagsRef,
      handleClose: (removedTag) => {
        const tags = tagsRef.value.filter(tag => tag !== removedTag);
        console.log(tags);
        tagsRef.value = tags;
        const join = tags.join(joinSeparator)
        emit('update:value', join);
        emit('change', join);
      },
      handleInputConfirm: () => {
        debugger
        const inValue = inputValue.value;
        let tags = tagsRef.value;
        if (inValue && tags.indexOf(inValue) === -1) {
          tags = [...tags, inputValue.value];
        }
        inputVisible.value = false
        inputValue.value = ''
        tagsRef.value = tags
        const join = tags.join(joinSeparator)
        emit('update:value', join);
        emit('change', join);
        console.log(tags);
      },
      showInput: () => {
        inputVisible.value = true
        nextTick(() => {
          inputRef.value.focus();
        })
      },
      inputVisible,
      inputValue,
      inputRef
    }
  }
})
</script>

<template>
  <template v-for="(tag, index) in tagsRef" :key="tag">
    <a-tooltip v-if="tag.length > 20" :title="tag">
      <a-tag :closable="true" @close="handleClose(tag)">
        {{ `${tag.slice(0, 20)}...` }}
      </a-tag>
    </a-tooltip>
    <a-tag v-else :closable="true" @close="handleClose(tag)">
      {{ tag }}
    </a-tag>
  </template>
  <a-input
    v-if="inputVisible"
    ref="inputRef"
    v-model:value="inputValue"
    type="text"
    size="small"
    :style="{ width: '78px' }"
    @blur="handleInputConfirm"
    @keyup.enter="handleInputConfirm"
  />
  <a-tag v-else style="background: #fff; border-style: dashed" @click="showInput">
    <vxe-icon-add />
    New Tag
  </a-tag>
</template>

<style scoped lang="less">

</style>
