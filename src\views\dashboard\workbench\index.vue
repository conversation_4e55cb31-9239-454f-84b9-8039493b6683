<template>
  <div>
    <PageWrapper>
      <template #headerContent> <WorkbenchHeader /> </template>

      <!-- 非新线管理部的布局 -->
      <template v-if="!isNewLineManagement">
        <div class="w-full enter-y">
          <Meeting
            :loading="loading"
            :options="lineOptions"
            class="enter-y"
            @openModal="fetchData"
          />
        </div>

        <!-- 非新线管理部的三个组件 -->
        <div class="lg:flex">
          <div class="lg:w-1/2 w-full enter-y">
            <AssemblyByLine
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
          <div class="lg:w-1/2 w-full enter-y">
            <OperationByLine
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
        </div>

        <div class="lg:flex">
          <div class="lg:w-1/2 w-full enter-y">
            <QuestionByLine
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
        </div>
      </template>

      <!-- 新线管理部的布局 -->
      <template v-else>
        <div class="w-full enter-y">
          <Meeting
            :loading="loading"
            :options="lineOptions"
            class="enter-y"
            @openModal="fetchData"
          />
        </div>

        <!-- 新线管理部的所有组件 -->
        <div class="lg:flex">
          <div class="lg:w-1/2 w-full !mr-4 enter-y">
            <AssemblyByLine
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
          <div class="lg:w-1/2 w-full enter-y">
            <AssemblyByDept
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
        </div>

        <div class="lg:flex">
          <div class="lg:w-1/2 w-full !mr-4 enter-y">
            <AssemblyByDeptAndLine
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
          <div class="lg:w-1/2 w-full enter-y">
            <OperationByLine
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
        </div>

        <div class="lg:flex">
          <div class="lg:w-1/2 w-full !mr-4 enter-y">
            <OperationByDept
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
          <div class="lg:w-1/2 w-full enter-y">
            <OperationByDeptAndLine
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
        </div>

        <div class="lg:flex">
          <div class="lg:w-1/2 w-full !mr-4 enter-y">
            <QuestionByLine
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
          <div class="lg:w-1/2 w-full enter-y">
            <QuestionByDept
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
        </div>

        <div class="lg:flex">
          <div class="lg:w-1/2 w-full enter-y">
            <QuestionByDeptAndLine
              :loading="loading"
              :line-opt="lineOptions"
              :dept-opt="deptOptions"
              class="enter-y"
            />
          </div>
          <div class="lg:w-1/2 hidden lg:block"></div>
        </div>
      </template>
    </PageWrapper>

    <!-- 修改后的表格样式模态框 -->
    <a-modal
      v-if="isModalVisible && modalData.length > 0"
      v-model:visible="isModalVisible"
      title="本周会议列表"
      width="1200px"
      :closable="false"
      :mask-closable="false"
      :keyboard="false"
      class="custom-meeting-modal"
    >
      <div class="table-container">
        <table class="meeting-table">
          <thead>
            <tr>
              <th class="title-col">会议内容</th>
              <th class="location-col">会议地点</th>
              <th class="time-col">开始时间</th>
              <th class="participants-col">联络人</th>
              <th class="status-col">状态</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in modalData"
              :key="index"
              class="meeting-row"
              :class="{
                'past-time': isPastTime(item.startMeetingDate),
                'modified-row': item.isModified && !isPastTime(item.startMeetingDate),
                'new-row':
                  !item.isModified &&
                  !isPastTime(item.startMeetingDate) &&
                  isNewThisWeek(item.createDateTime),
              }"
            >
              <td class="title-col">
                <span class="cell-content">{{ item.title }}</span>
              </td>
              <td class="location-col">
                <span class="cell-content">{{ item.location }}</span>
              </td>
              <td class="time-col">
                <span class="cell-content">{{ formatTime(item.startMeetingDate) }}</span>
              </td>
              <td class="participants-col">
                <span class="cell-content">{{ item.participants?.join(', ') }}</span>
              </td>
              <td class="status-col">
                <a-tag v-if="isPastTime(item.startMeetingDate)" color="default">已过期</a-tag>
                <a-tag v-else-if="item.isModified" color="yellow">会议变更</a-tag>
                <a-tag v-else-if="isNewThisWeek(item.createDateTime)" color="green">本周新建</a-tag>
                <a-tag v-else color="blue">正常</a-tag>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <template #footer>
        <div class="modal-footer">
          <a-button type="primary" @click="handleOk">确定</a-button>
          <a-button type="primary" @click="goToMeetingList">会议菜单</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, ref } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import WorkbenchHeader from './components/WorkbenchHeader.vue';
  import { useLineStoreWithOut } from '/@/store/modules/line';
  import { useRouter } from 'vue-router';
  import { getDataApi } from '/@/views/dashboard/analysis/components/api/analysis';
  import Meeting from '/@/views/dashboard/workbench/components/Meeting.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { useDeptStoreWithOut } from '/@/store/modules/dept';
  import AssemblyByLine from './components/AssemblyByLine.vue';
  import OperationByLine from './components/OperationByLine.vue';
  import QuestionByLine from './components/QuestionByLine.vue';
  import AssemblyByDept from './components/AssemblyByDept.vue';
  import AssemblyByDeptAndLine from './components/AssemblyByDeptAndLine.vue';
  import OperationByDept from './components/OperationByDept.vue';
  import OperationByDeptAndLine from './components/OperationByDeptAndLine.vue';
  import QuestionByDept from './components/QuestionByDept.vue';
  import QuestionByDeptAndLine from './components/QuestionByDeptAndLine.vue';

  const loading = ref(true);
  const lineOptions = ref([]);
  const deptOptions = ref([]);
  const lineStore = useLineStoreWithOut();
  const deptStore = useDeptStoreWithOut();
  const userStore = useUserStore();
  const router = useRouter();
  const isModalVisible = ref(false);
  const modalData = ref<MeetingItem[]>([]);

  onMounted(() => {
    Promise.all([deptStore.getDeptOptions(), lineStore.getLineOptions()]).then(
      ([deptRes, lineRes]) => {
        deptOptions.value = deptRes;
        lineOptions.value = lineRes;
        loading.value = false;
      },
    );
    fetchData();
  });

  const isNewLineManagement = computed(() => {
    return userStore.getUserInfo.dept === '新线管理部';
  });

  interface MeetingItem {
    id?: number;
    title: string;
    meetingDate: string;
    startMeetingDate: string;
    location: string;
    participants?: string[];
    departments?: string[];
    isModified: boolean;
    updateDateTime?: string;
    createDateTime?: string;
  }

  const fetchData = async () => {
    try {
      const userId = userStore.getUserInfo.userId;
      const data = await getDataApi(userId);
      modalData.value = data.map((item) => ({
        ...item,
        isModified: !!item.updateDateTime,
      }));
      setTimeout(() => {
        isModalVisible.value = true;
      }, 2900);
    } catch (error) {
      console.error('Failed to fetch data:', error);
    }
  };

  const isPastTime = (timeString: string) => {
    const meetingTime = new Date(timeString).getTime();
    const currentTime = new Date().getTime();
    return meetingTime < currentTime;
  };

  const isNewThisWeek = (createDateTime?: string) => {
    if (!createDateTime) return false;

    const createDate = new Date(createDateTime);
    const now = new Date();

    // 获取本周的第一天（周一）
    const firstDayOfWeek = new Date(now);
    firstDayOfWeek.setDate(now.getDate() - now.getDay() + (now.getDay() === 0 ? -6 : 1));
    firstDayOfWeek.setHours(0, 0, 0, 0);

    // 获取本周的最后一天（周日）
    const lastDayOfWeek = new Date(firstDayOfWeek);
    lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6);
    lastDayOfWeek.setHours(23, 59, 59, 999);

    return createDate >= firstDayOfWeek && createDate <= lastDayOfWeek;
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleOk = () => {
    isModalVisible.value = false;
  };

  const handleCancel = () => {
    isModalVisible.value = false;
  };

  const goToMeetingList = () => {
    if (isNewLineManagement.value) {
      router.push('/newLine/meetings/meeting');
    } else {
      router.push('/newLine/meetings/meeting1');
    }
  };
</script>
<style scoped lang="less">
  .table-container {
    max-height: 60vh;
    overflow-y: auto;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  }

  .meeting-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    border-left: 3px solid #e8e8e8;
    border-right: 3px solid #e8e8e8;

    th,
    td {
      padding: 12px 16px;
      border: 1px solid #e8e8e8;
      text-align: left;
      background-color: white;
    }

    th {
      background-color: #fafafa;
      font-weight: 600;
      position: sticky;
      top: 0;
      z-index: 1;
      color: rgba(0, 0, 0, 0.85);
    }

    tbody {
      display: block;
      max-height: 50vh;
      overflow-y: auto;
    }

    thead,
    tbody tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }
  }

  .cell-content {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: rgba(0, 0, 0, 0.85);
  }

  // 列宽设置保持原样
  .title-col {
    width: 100px;
    max-width: 150px;
  }

  .location-col {
    width: 100px;
    max-width: 150px;
  }

  .time-col {
    width: 100px;
    max-width: 150px;
  }

  .participants-col {
    width: 120px;
    max-width: 200px;
  }

  .status-col {
    width: 60px;
    max-width: 100px;
  }

  .past-time {
    td {
      background-color: #fafafa !important;

      .cell-content {
        color: #bfbfbf !important;
      }
    }

    .ant-tag {
      background: #f5f5f5 !important;
      border-color: #d9d9d9 !important;
      color: #bfbfbf !important;
    }
  }

  .new-row {
    td {
      background-color: #f6ffed !important;
    }
  }

  .modified-row {
    td {
      background-color: #fffbe6 !important;
    }
  }

  /* 滚动条优化 */
  .table-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .table-container::-webkit-scrollbar-track {
    background: #f8f8f8;
    border-radius: 4px;
  }

  .table-container::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 4px;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0 0;

    .ant-btn {
      min-width: 80px;
      border-radius: 4px;
      font-weight: 500;
    }
  }
</style>
