<template>
  <div>
    <Space>
      <a-button
        v-if="!$props.disabled && !$props.readonly"
        type="primary"
        @click="openUploadModal"
        preIcon="carbon:cloud-upload"
      >
        {{ t('component.upload.upload') }}
      </a-button>
      <Tooltip placement="bottom" v-if="showPreview">
        <template #title>
          {{ t('component.upload.uploaded') }}
          <template v-if="fileList.length">
            {{ fileList.length }}
          </template>
        </template>
        <a-button
          @click="preview"
          :style="{ backgroundColor: fileList.length > 0 ? '#fdd503' : 'white' }"
        >
          <Icon icon="bi:eye" />
          <template v-if="fileList.length && showPreviewNumber">
            {{ fileList.length }}
          </template>
        </a-button>
      </Tooltip>
    </Space>
    <!--    <div class="ant-upload-list ant-upload-list-text" v-for="file in fileList"
      ><div class="ant-upload-list-item-container"
        ><div class="ant-upload-list-item ant-upload-list-item-done"
          ><div class="ant-upload-text-icon"
            ><span role="img" aria-label="paper-clip" class="anticon anticon-paper-clip">
              <svg
                focusable="false"
                data-icon="paper-clip"
                width="1em"
                height="1em"
                fill="currentColor"
                aria-hidden="true"
                viewBox="64 64 896 896"
              >
                <path
                  d="M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z"
                ></path></svg
              >&lt;!&ndash;&ndash;&gt;</span
            >
            <a
              target="_blank"
              rel="noopener noreferrer"
              class="ant-upload-list-item-name"
              :title="getFileName(file)"
              style="display: inline"
              >{{ getFileName(file) }}</a
            ></div
          ><span class="ant-upload-list-item-actions">&lt;!&ndash;&ndash;&gt; </span>
        </div></div
      ></div
    >-->
    <UploadModal
      v-bind="bindValue"
      :previewFileList="fileList"
      @register="registerUploadModal"
      @change="handleChange"
      @delete="handleDelete"
    />

    <UploadPreviewModal
      :readOnly="!$props.disabled"
      :value="fileList"
      @register="registerPreviewModal"
      @list-change="handlePreviewChange"
      @delete="handlePreviewDelete"
    />
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, watch, unref, computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { Tooltip, Space } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import { uploadContainerProps } from '/@/components/Upload/src/props';
  import { omit } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { isArray } from '/@/utils/is';
  import EruptUploadModal from '/@/components/Form/src/components/EruptUploadModal.vue';
  import EruptUploadPreviewModal from '/@/components/Form/src/components/EruptUploadPreviewModal.vue';

  export default defineComponent({
    name: 'EruptUpload',
    components: {
      UploadModal: EruptUploadModal,
      Space,
      UploadPreviewModal: EruptUploadPreviewModal,
      Icon,
      Tooltip,
    },
    props: uploadContainerProps,
    emits: ['change', 'delete', 'preview-delete', 'update:value'],

    setup(props, { emit, attrs }) {
      const { t } = useI18n();
      // 上传modal
      const [registerUploadModal, { openModal: openUploadModal }] = useModal();

      //   预览modal
      const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();

      const fileList = ref<string[]>([]);

      const showPreview = computed(() => {
        const { emptyHidePreview } = props;
        if (!emptyHidePreview) return true;
        return emptyHidePreview ? fileList.value.length > 0 : true;
      });

      const bindValue = computed(() => {
        const value = { ...attrs, ...props };
        return omit(value, 'onChange');
      });

      watch(
        () => props.value,
        (value) => {
          fileList.value = value ? value.split('|') : [];
        },
        { immediate: true },
      );
      function getFileName(file) {
        const index = file.split('/').length - 1;
        return file.split('/')[index];
      }
      function preview() {
        console.log(fileList);
        openPreviewModal();
      }
      // 上传modal保存操作
      function handleChange(urls: string[]) {
        fileList.value = [...unref(fileList), ...(urls || [])];
        console.log(fileList);
        const fileJoin = fileList.value.join('|');
        emit('update:value', fileJoin);
        emit('change', fileJoin);
      }

      // 预览modal保存操作
      function handlePreviewChange(urls: string[]) {
        fileList.value = [...(urls || [])];
        const fileJoin = fileList.value.join('|');
        emit('update:value', fileJoin);
        emit('change', fileJoin);
      }

      function handleDelete(record: Recordable) {
        emit('delete', record);
      }

      function handlePreviewDelete(url: string) {
        emit('preview-delete', url);
      }

      return {
        registerUploadModal,
        openUploadModal,
        handleChange,
        handlePreviewChange,
        registerPreviewModal,
        openPreviewModal,
        fileList,
        showPreview,
        bindValue,
        handleDelete,
        handlePreviewDelete,
        t,
        getFileName,
        preview,
      };
    },
  });
</script>
