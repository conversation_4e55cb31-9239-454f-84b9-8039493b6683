<script lang="tsx">
  import { defineComponent, onMounted, ref } from 'vue';
  import { getAllApi } from '/@/views/demo/event/api';
  import { formatDate } from '@vueuse/shared';

  export default defineComponent({
    methods: { formatDate },
    setup(props, { attrs, emit }) {
      const width = ref(1920);
      const cycle = ref(1);
      const all = ref([]);
      onMounted(() => {
        getAllApi().then((res) => {
          debugger;
          cycle.value = Math.ceil(res.length / 10);
          console.log(res.length, res.length / 10);
          width.value =
            res.length % 10 == 0 ? (res.length / 10) * 2080 : Math.ceil(res.length / 10) * 2080;

          all.value = res;
          console.log(all.value[0].year);
        });
      });

      return {
        cycle,
        width,
        all,
      };
    },
  });
</script>

<template>
  <div :style="{ width: '1620px', display: 'flex', flexDirection: 'row', overflowX: 'auto' }">
    <div style="width: 1620px" v-for="i in cycle">
      <div class="background" :style="{ width: 1620 + 'px' }">
        <div class="event" :style="{ width: 1620 + 'px' }">
          <div>
            <div v-if="all[(i - 1) * 10] !== undefined">
              <div class="year-05" :style="{ marginLeft: 88 + 'px', marginTop: '-30px' }"
                >{{ all[(i - 1) * 10].year.substring(0, 4) }}年</div
              >
              <p class="year-05-note-line" :style="{ marginLeft: 88 + 'px', marginTop: '10px' }">{{
                all[(i - 1) * 10].description
              }}</p>
              <p class="year-05-note-km" :style="{ marginLeft: 88 + 'px', marginTop: '-20px' }"
                >开通 <strong>{{ all[(i - 1) * 10].mileage }}</strong> 公里</p
              >
              <div
                class="year-05-line"
                :style="{ marginLeft: 68 + 'px', marginTop: '-140px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 1] !== undefined">
              <div class="year-10" :style="{ marginLeft: 238 + 'px', marginTop: '-380px' }"
                >{{ all[(i - 1) * 10 + 1].year.substring(0, 4) }}年</div
              >
              <p class="year-10-note-line" :style="{ marginLeft: 238 + 'px', marginTop: '10px' }">{{
                all[(i - 1) * 10 + 1].description
              }}</p>
              <p class="year-10-note-km" :style="{ marginLeft: 238 + 'px', marginTop: '-20px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 1].mileage }}</strong> 公里</p
              >
              <div
                class="year-10-line"
                :style="{ marginLeft: 218 + 'px', marginTop: '-80px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 2] !== undefined">
              <div class="year-14" :style="{ marginLeft: 388 + 'px', marginTop: '-20px' }"
                >{{ all[(i - 1) * 10 + 2].year.substring(0, 4) }}年</div
              >
              <p class="year-14-note-line" :style="{ marginLeft: 388 + 'px', marginTop: '10px' }">{{
                all[(i - 1) * 10 + 2].description
              }}</p>
              <p class="year-14-note-km" :style="{ marginLeft: 388 + 'px', marginTop: '-20px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 2].mileage }}</strong> 公里</p
              >
              <div
                class="year-14-line"
                :style="{ marginLeft: 368 + 'px', marginTop: '-280px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 3] !== undefined">
              <div class="year-15" :style="{ marginLeft: 548 + 'px', marginTop: '-520px' }"
                >{{ all[(i - 1) * 10 + 3].year.substring(0, 4) }}年</div
              >
              <p class="year-15-note-line" :style="{ marginLeft: 548 + 'px', marginTop: '10px' }">{{
                all[(i - 1) * 10 + 3].description
              }}</p>
              <p class="year-15-note-km" :style="{ marginLeft: 548 + 'px', marginTop: '-20px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 3].mileage }}</strong> 公里</p
              >
              <div
                class="year-15-line"
                :style="{ marginLeft: 528 + 'px', marginTop: '-140px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 4] !== undefined">
              <div class="year-17" :style="{ marginLeft: 698 + 'px', marginTop: '-170px' }"
                >{{ all[(i - 1) * 10 + 4].year.substring(0, 4) }}年</div
              >
              <p class="year-17-note-line" :style="{ marginLeft: 698 + 'px', marginTop: '10px' }">{{
                all[(i - 1) * 10 + 4].description
              }}</p>
              <p class="year-17-note-km" :style="{ marginLeft: 698 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 4].mileage }}</strong> 公里</p
              >
              <div
                class="year-17-line"
                :style="{ marginLeft: 678 + 'px', marginTop: '-270px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 5] !== undefined">
              <div class="year-18" :style="{ marginLeft: 848 + 'px', marginTop: '-480px' }"
                >{{ all[(i - 1) * 10 + 5].year.substring(0, 4) }}年</div
              >
              <p class="year-18-note-line" :style="{ marginLeft: 848 + 'px', marginTop: '20px' }">{{
                all[(i - 1) * 10 + 5].description
              }}</p>
              <p class="year-18-note-km" :style="{ marginLeft: 848 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 5].mileage }}</strong> 公里</p
              >
              <div
                class="year-18-line"
                :style="{ marginLeft: 828 + 'px', marginTop: '-100px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 6] !== undefined">
              <div class="year-21" :style="{ marginLeft: 998 + 'px', marginTop: '-120px' }">{{
                all[(i - 1) * 10 + 6].year.substring(0, 4)
              }}</div>
              <p class="year-21-note-line" :style="{ marginLeft: 998 + 'px', marginTop: '20px' }">{{
                all[(i - 1) * 10 + 6].description
              }}</p>
              <p class="year-21-note-km" :style="{ marginLeft: 998 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 6].mileage }}</strong> 公里</p
              >
              <div
                class="year-21-line"
                :style="{ marginLeft: 978 + 'px', marginTop: '-275px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 7] !== undefined">
              <div class="year-22" :style="{ marginLeft: 1148 + 'px', marginTop: '-620px' }">{{
                all[(i - 1) * 10 + 7].year.substring(0, 4)
              }}</div>
              <p
                class="year-22-note-line"
                :style="{ marginLeft: 1148 + 'px', marginTop: '20px' }"
                >{{ all[(i - 1) * 10 + 7].description }}</p
              >
              <p class="year-22-note-km" :style="{ marginLeft: 1148 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 7].mileage }}</strong> 公里</p
              >
              <div
                class="year-22-line"
                :style="{ marginLeft: 1128 + 'px', marginTop: '-210px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 8] !== undefined">
              <div class="year-23" :style="{ marginLeft: 1298 + 'px', marginTop: '-160px' }">{{
                all[(i - 1) * 10 + 8].year.substring(0, 4)
              }}</div>
              <p
                class="year-23-note-line"
                :style="{ marginLeft: 1298 + 'px', marginTop: '20px' }"
                >{{ all[(i - 1) * 10 + 8].description }}</p
              >
              <p class="year-23-note-km" :style="{ marginLeft: 1298 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 8].mileage }}</strong> 公里</p
              >
              <div
                class="year-23-line"
                :style="{ marginLeft: 1278 + 'px', marginTop: '-280px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 9] !== undefined">
              <div class="year-24" :style="{ marginLeft: 1448 + 'px', marginTop: '-380px' }">{{
                all[(i - 1) * 10 + 9].year.substring(0, 4)
              }}</div>
              <p
                class="year-24-note-line"
                :style="{ marginLeft: 1448 + 'px', marginTop: '20px' }"
                >{{ all[(i - 1) * 10 + 9].description }}</p
              >
              <p class="year-24-note-km" :style="{ marginLeft: 1448 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 9].mileage }}</strong> 公里</p
              >
              <div
                class="year-24-line"
                :style="{ marginLeft: 1428 + 'px', marginTop: '-240px' }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .background {
    background-image: url('../../../assets/images/bg.png');
    background-repeat: repeat-x;
    height: 990px;
    border-radius: 0px 0px 0px 0px;
    display: flex;
    flex-direction: row;
  }
</style>
