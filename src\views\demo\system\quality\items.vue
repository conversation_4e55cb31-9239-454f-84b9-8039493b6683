<template>
  <div style="padding: 16px 24px; background: white">
    <vxe-toolbar ref="toolbarRef">
      <template #buttons>
        <vxe-button type="primary" @click="goBack">返回</vxe-button>
        <vxe-button type="primary" status="primary" @click="add">新增</vxe-button>
      </template>
    </vxe-toolbar>
    <vxe-grid v-bind="gridOptions">
      <template #active="{ row }">
        <vxe-button v-show="row.level > 1" type="text" status="primary" @click="edit(row)"
          >编辑</vxe-button
        >
        <a-popconfirm title="是否确认删除" placement="left" @confirm="remove(row)">
          <vxe-button v-show="row.level > 1" type="text" status="danger">删除</vxe-button>
        </a-popconfirm>
      </template>
    </vxe-grid>
    <BasicDrawer
      width="600px"
      @register="registerDrawer"
      :showFooter="true"
      :title="!isEdit ? '新增' : '编辑'"
      @ok="handleSubmit"
    >
      <BasicForm style="height: 300px" @register="registerForm"></BasicForm>
    </BasicDrawer>
  </div>
</template>
<script setup lang="ts">
  import { onMounted, ref, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    getItemsApi,
    getSystemItemsApi,
    itemAdd,
    itemEdit,
    itemRemove,
  } from '/@/views/demo/system/quality/api/quality';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDrawer } from '/@/components/Drawer';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { useForm } from '/@/components/Form';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  const route = useRoute();
  const router = useRouter();
  let isEdit = ref(false);
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawer();
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: [
      {
        field: 'id',
        label: 'id',
        required: false,
        show: false,
        component: 'Input',
        colProps: { span: 24 },
      },
      {
        field: 'chapter',
        label: '编号',
        required: true,
        component: 'Input',
        colProps: { span: 24 },
        componentProps: {
          placeholder: '请输入编号',
          disabled: isEdit,
        },
        rules: [
          { required: true, message: '编号为必填项' },
          { pattern: /^(?!\.)[0-9.]*$/, message: '编号只能包含数字和点' },
        ],
      },
      {
        field: 'systemName',
        label: '系统',
        required: true,
        component: 'ApiSelect',
        colProps: { span: 24 },
        componentProps: {
          api: getSystemItemsApi,
          placeholder: '请选择系统',
          disabled: isEdit,
        },
      },
      {
        field: 'purchaseContract',
        label: '采购合同',
        required: false,
        component: 'Input',
        colProps: { span: 24 },
        componentProps: {
          placeholder: '请输入采购合同',
        },
      },
      {
        field: 'supplier',
        label: '供货商',
        required: false,
        component: 'Input',
        colProps: { span: 24 },
        componentProps: {
          placeholder: '请输入供货商',
        },
      },
      {
        field: 'responsibleUnit',
        label: '责任单位',
        required: false,
        component: 'Input',
        colProps: { span: 24 },
        componentProps: {
          placeholder: '请输入责任单位',
        },
      },
    ],
    showActionButtonGroup: false,
  });
  const id = route.params?.id ?? -1;
  const row = route.query;
  delete row._X_ROW_KEY;
  delete row.key;
  const loading = ref(false);
  const tableData = ref([]);
  const { createMessage } = useMessage();
  let globalIndex = 0;
  const gridOptions = ref({
    border: true,
    showFooter: true,
    treeConfig: {
      rowField: 'id',
      childrenField: 'children',
    },
    columns: [
      {
        field: 'seq',
        title: '序号',
        width: 100,
        formatter: ({ row }) => row.seq || '',
      },
      { field: 'chapter', title: '编号', treeNode: true },
      { field: 'systemName', title: '系统' },
      { field: 'purchaseContract', title: '采购合同' },
      { field: 'supplier', title: '供货商' },
      { field: 'responsibleUnit.name', title: '责任单位' },
      { title: '操作', slots: { default: 'active' } },
    ],
    data: tableData,
    footerMethod: () => {
      return [
        [
          '合计:' + globalIndex, // 显示全局序号
          '', // 显示合计
          '', // 其他列留空
          '',
          '',
          '',
        ],
      ];
    },
  });
  onMounted(() => {
    getList();
  });
  function goBack() {
    router.push('/newLine/quality/qualityTpl');
  }

  async function getList() {
    const res = await getItemsApi(id);
    tableData.value = res.map((item) => {
      if (item.children && item.children.length > 0) {
        // 处理一级 children 数组，为每个子节点设置 req
        item.children = item.children.map((child) => {
          child.seq = ++globalIndex; // 为每个子节点设置 req
          return child; // 返回修改后的子节点
        });
      }
      return item;
    });
    console.log(tableData.value);
  }

  const add = () => {
    isEdit.value = false;
    resetFields();
    openDrawer();
  };

  const edit = (row) => {
    isEdit.value = true;
    openDrawer();
    nextTick(() => {
      setFieldsValue(row);
    });
  };

  const remove = (row) => {
    itemRemove(row.id).then((res) => {
      if (res) {
        createMessage.success('删除成功');
      } else {
        createMessage.error('删除失败');
      }
      getList();
    });
  };
  const handleSubmit = async () => {
    try {
      let values = await validate();
      let res;
      if (isEdit.value) {
        res = await itemEdit(values);
      } else {
        res = await itemAdd(id, values);
      }
      if (res) {
        createMessage.success('保存成功');
      } else {
        createMessage.error('保存失败');
      }
    } catch (error) {
      createMessage.error('保存失败');
    } finally {
      closeDrawer();
      getList();
    }
  };
</script>
