<template>
  <div>
    <vxe-table :data="tableData">
      <vxe-column type="seq" width="60" />
      <vxe-column field="name" title="Name" />
      <vxe-column field="sex" title="Sex" />
      <vxe-column field="age" title="Age" />
    </vxe-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';

  interface RowVO {
    id: number;
    name: string;
    role: string;
    sex: string;
    age: number;
    address: string;
  }

  const tableData = ref<RowVO[]>([
    { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
    { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
    { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
    { id: 10004, name: 'Test<PERSON>', role: 'Designer', sex: 'Women', age: 24, address: 'Shanghai' },
  ]);
</script>
