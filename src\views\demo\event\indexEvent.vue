<template>
  <div class="min-h-screen bg-black text-white">
    <div class="container mx-auto px-4 py-8 max-w-[1440px]">
      <div class="flex justify-center items-center h-screen"> </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  const openEvent = () => {
    window.open(import.meta.env.VITE_GLOB_FULL_SCREEN_URL + '#/events', '_blank');
  };
  openEvent();
</script>

<style scoped></style>
