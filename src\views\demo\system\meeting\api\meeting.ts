import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';
import { UploadApiResult } from '/@/api/sys/model/uploadModel';
import { useGlobSetting } from '/@/hooks/setting';

const globSetting = useGlobSetting();

enum Api {
  UpdateMeetingStatus = '/newline/meeting/update/status',
  refreshMeetingMessage = '/newline/meeting/update/refreshMeetingMessage',
  UpdateMeetingContent = '/newline/meeting/update/content',
  UploadMeetingFile = '/newline/meeting/upload/file',
  getPersonalMeeting = '/newline/meeting/dto',
  Detail = '/newline/meeting/detail1/',
  Detail2 = '/newline/meeting/detail2/',
  Detail3 = '/newline/meeting/detail3/',
  Detail4 = '/newline/meeting/detail4/',
}

export const detail1 = async (id) => {
  return await defHttp.get({ url: Api.Detail + id });
};
export const detail2 = async (id) => {
  return await defHttp.get({ url: Api.Detail2 + id });
};

export const detail3 = async (mcid) => {
  return await defHttp.get({ url: Api.Detail3 + mcid });
};

export const detail4 = async (id) => {
  return await defHttp.get({ url: Api.Detail4 + id });
};

export const getPersonalMeetingApi = async () => {
  return await defHttp.get({ url: Api.getPersonalMeeting });
};
/**
 * @description: 更新会议状态
 */
export const updateMeetingStatusApi = async (id, meetingStatus) => {
  return await defHttp.post({ url: Api.UpdateMeetingStatus + `/${id}`, data: { meetingStatus } });
};

export const refreshMeetingMessage = async (id) => {
  return await defHttp.post({ url: Api.refreshMeetingMessage + `/${id}` });
};

/**
 * @description: 更新会议内容
 */
export const updateMeetingContentApi = async (id, content, meetingNumber, attachment) => {
  return await defHttp.post({
    url: Api.UpdateMeetingContent + `/${id}`,
    data: { content, meetingNumber, attachment },
  });
};

/**
 * @description: 上传会议文件
 */
export const uploadMeetingFileApi = (meetingId) => {
  return (params: UploadFileParams, onUploadProgress: (ProgressEvent) => void) => {
    return defHttp.uploadFile<UploadApiResult>(
      {
        url: `${globSetting.apiUrl}${Api.UploadMeetingFile}/${meetingId}`,
        onUploadProgress,
      },
      params,
    );
  };
};
