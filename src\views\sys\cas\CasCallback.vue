<template>
  <div class="cas-callback-container">
    <div class="cas-callback-loading">
      <a-spin tip="登录验证中..." />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { PageEnum } from '/@/enums/pageEnum';

  const { t } = useI18n();
  const { notification, createErrorModal } = useMessage();

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();

  const handleCasCallback = async () => {
    const ticket = Array.isArray(route.query.ticket) ? route.query.ticket[0] : route.query.ticket;

    if (ticket) {
      const userInfo = userStore.casLogin({
        ticket,
        mode: 'none', //不要默认的错误提示
      });

      if (userInfo) {
        router.push(PageEnum.BASE_HOME);
      } else {
        router.push(PageEnum.BASE_LOGIN);
      }
    } else {
      window.location.href =
        'http://***************:8080/cas/login?service=http://**************/cas-callback';
    }
  };

  onMounted(() => {
    handleCasCallback();
  });
</script>

<style lang="less" scoped>
  .cas-callback {
    &-container {
      width: 100%;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f0f2f5;
    }

    &-loading {
      text-align: center;
    }
  }
</style>
