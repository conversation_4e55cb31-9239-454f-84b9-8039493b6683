import { defineComponent, reactive, watch } from 'vue';
import { computed, ref } from 'vue';
import { BasicTableProps, VxeBasicTable, VxeGridInstance } from '/@/components/VxeTable';
import { ActionItem, TableAction } from '/@/components/Table';
const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();
import { useMessage } from '/@/hooks/web/useMessage';
import { BasicForm, useForm } from '/@/components/Form/index';

import {
  tableQueryApi,
  refBuildApi,
  remove,
  batchRemove,
  save,
  getOne,
  update,
  tabAdd,
  tabUpdate,
  buildApi,
  refQueryApi,
} from '/@/api/erupt/erupt';
import {
  getColFomatter,
  getColFomatterTab,
  getComponent,
  getComponentProps,
  getSearchComponent,
  getSearchRefComponent,
} from '/@/components/EruptTable/componets';
import { Tooltip, Modal, Tag } from 'ant-design-vue';
import EruptUploadPreviewModal from '/@/components/Form/src/components/EruptUploadPreviewModal.vue';
import { Icon } from '/@/components/Icon';
import { BasicModal, useModal } from '/@/components/Modal';
import { isArray } from '/@/utils/is';
//import '../css/tableTab.module.scss'

export default defineComponent({
  name: 'eruptTable',
  props: {
    value: { type: Array },
    className: {},
    models: { type: Array },
    tabName: { type: String },
    readOnly: { type: Boolean },
  },
  emits: ['update:value', 'register', 'change'],
  setup(props, { emit, attrs }) {
    const { className, models, tabName, readOnly } = props;
    const listRef = ref([] as any[]);
    const row_key = ref('');
    const tableRef = ref<VxeGridInstance>();
    const selectRef = ref<VxeGridInstance>();
    const uploadModalRef = ref();
    const columns = ref([]);
    const modalTitle = ref('');
    const modalColumns = ref([]);
    const modalQuerys = ref([]);
    const showFooter = ref(true);
    const base = ref({ id: '', version: '' });
    const [register, { openModal, closeModal }] = useModal();
    const formSchema = ref();
    const defaultSpaceRows = ref([]);
    const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
      labelWidth: 100,
      baseColProps: { span: 16 },
      schemas: formSchema,
      showActionButtonGroup: false,
    });

    let querys = ref([]);
    const toolbarConfig = ref({
      refresh: false, // 显示刷新按钮
      import: false, // 显示导入按钮
      export: false, // 显示导出按钮
      print: false, // 显示打印按钮
      zoom: false, // 显示全屏按钮
      custom: true,
    });
    const { createMessage } = useMessage();
    watch(
      () => props.value,
      (value) => {
        //tableRef.value.reloadData(value)
        listRef.value = value;
      },
      { immediate: true },
    );

    watch(
      () => listRef.value,
      (v) => {
        tableRef.value.reloadData(v);
        emit('update:value', v);
        return emit('change', v);
      },
    );
    watch(
      () => props.readOnly,
      (value) => {
        console.log('readOnly', value);
        const cols = JSON.parse(JSON.stringify(columns.value));
        if (!value) {
          toolbarConfig.value.buttons = [
            {
              content: '新增',
              buttonRender: {
                name: 'AButton',
                props: {
                  type: 'primary',
                },
                events: {
                  click: () => {
                    modalTitle.value = '新增';
                    showFooter.value = true;
                    openModal();
                    base.value.id = '';
                    base.value.version = '';
                    updateSchema(formSchema.value);
                    resetFields();
                  },
                },
              },
            },
            {
              content: '删除',
              buttonRender: {
                name: 'AButton',
                props: {
                  type: 'warning',
                },
                events: {
                  click: () => {
                    Modal.confirm({
                      title: '提示',
                      content: '是否确认删除',
                      okText: '确认',
                      cancelText: '取消',
                      onOk() {
                        handleBatchRemove();
                      },
                    });
                  },
                },
              },
            },
          ];
          toolbarConfig.value.enabled = true;
          cols.push({
            width: 160,
            title: '操作',
            align: 'center',
            slots: { default: 'action' },
            fixed: 'right',
          });
        } else {
          toolbarConfig.value.buttons = [];
          toolbarConfig.value.enabled = false;
          const index = cols.findIndex((item) => item.title == '操作');
          cols.splice(index, 1);
        }
        columns.value = cols;
      },
      { immediate: true },
    );

    refBuildApi(className, tabName).then((res) => {
      const {
        eruptModel: { eruptFieldModels },
        tabErupts,
      } = res;
      let cols = [{ type: 'checkbox' }];
      let qs = [];
      let edits = [];
      let tabItems = [];
      eruptFieldModels.forEach((item) => {
        const key = item.fieldName;
        const title = item.eruptFieldJson.edit.title;

        item.eruptFieldJson.views.forEach((v) => {
          if (v.show) {
            cols.push(getColFomatterTab(key, v, item.eruptFieldJson.edit));
          }
        });
        //  formState[key] = null
        if (item.eruptFieldJson.edit.search.value) {
          qs.push(getSearchRefComponent(key, title, item, 4, className, tabName));
        }
        if (item.eruptFieldJson.edit.show.edit_show && key !== 'id') {
          if (
            item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
            item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD'
          ) {
            const e = {
              field: key,
              label: title,
              component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
              componentProps: { ...getComponentProps(item, className), disabled: false },
              required: item.eruptFieldJson.edit.notNull,
            };
            edits.push(e);
          }
        }
      });
      qs.push({
        span: 4,
        align: 'right',
        className: '!pr-0',
        itemRender: {
          name: 'AButtonGroup',
          children: [
            {
              props: { type: 'primary', content: '查询', htmlType: 'submit' },
              attrs: { class: 'mr-2' },
            },
            { props: { type: 'default', htmlType: 'reset', content: '重置' } },
          ],
        },
      });

      modalQuerys.value = qs;
      modalColumns.value = cols;
      formSchema.value = edits;
      //gridOptions.columns = columns
      //search()
    });

    function setList(value) {
      if (!isArray(value)) value = [];
      listRef.value = value;
      tableRef.value.reloadData(value);
    }
    function refresh() {
      document.querySelector('button[content="查询"]').click();
    }

    function handleCancel() {
      resetFields();
    }
    async function handleBatchRemove() {
      const selectedRows = tableRef.value.getCheckboxRecords();
      let dataList = tableRef.value.getTableData().tableData;
      let row_keys = [];
      selectedRows.forEach((item) => row_keys.push(item._X_ROW_KEY));
      for (let rowKey of row_keys) {
        const index = dataList.findIndex((item) => item._X_ROW_KEY == rowKey);
        dataList.splice(index, 1);
      }
      listRef.value = dataList;
      // tableRef.value.reloadData(dataList)
    }
    async function handleSubmit() {
      const records = selectRef.value.getCheckboxRecords();
      let old = [];
      listRef.value.forEach((item) => old.push(item));
      records.forEach((item) => old.push(item));
      listRef.value = old;
      tableRef.value.reloadData(old);

      closeModal();
    }

    function search(page, form) {
      let condition = [];
      for (let key in form) {
        if (form[key]) {
          condition.push({ key, value: form[key] });
        }
      }
      const query = {
        pageIndex: page.currentPage,
        pageSize: page.pageSize,
        condition,
      };
      return refQueryApi(className, tabName, query);
    }

    async function edit(row) {
      modalTitle.value = '编辑';
      showFooter.value = true;
      openModal();
      updateSchema(formSchema.value);
      row_key.value = row._X_ROW_KEY;
      setFieldsValue(row);
    }

    async function deleted(row) {
      let dataList = tableRef.value.getTableData().tableData;
      const index = dataList.findIndex((item) => item._X_ROW_KEY == row._X_ROW_KEY);
      dataList.splice(index, 1);
      listRef.value = dataList;
      //tableRef.value.reloadData(dataList)
    }

    let cols = [{ type: 'checkbox' }];
    let qs = [];
    let edits = [];
    models.forEach((item) => {
      const key = item.fieldName;
      const title = item.eruptFieldJson.edit.title;

      item.eruptFieldJson.views.forEach((v) => {
        if (v.show) {
          cols.push(getColFomatter(key, v, item.eruptFieldJson.edit));
        }
      });
      //  formState[key] = null
      if (item.eruptFieldJson.edit.search.value) {
        qs.push(getSearchComponent(key, title, item, 4, className));
      }
      if (
        item.eruptFieldJson.edit.show &&
        item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
        item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
        key !== 'id'
      ) {
        const e = {
          field: key,
          label: title,
          component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
          componentProps: { ...getComponentProps(item, className), disabled: false },
          required: item.eruptFieldJson.edit.notNull,
        };
        edits.push(e);
      }
    });
    qs.push({
      span: 4,
      align: 'right',
      className: '!pr-0',
      itemRender: {
        name: 'AButtonGroup',
        children: [
          {
            props: { type: 'primary', content: '查询', htmlType: 'submit' },
            attrs: { class: 'mr-2' },
          },
          { props: { type: 'default', htmlType: 'reset', content: '重置' } },
        ],
      },
    });

    if (!readOnly) {
      cols.push({
        width: 160,
        title: '操作',
        align: 'center',
        slots: { default: 'action' },
        fixed: 'right',
      });
    }
    querys.value = qs;
    columns.value = cols;
    formSchema.value = edits;
    //gridOptions.columns = columns
    //search()

    return {
      tableRef,
      selectRef,
      readOnly,
      //createActions,
      columns,
      querys,
      toolbarConfig,
      search,
      registerForm,
      modalTitle,
      showFooter,
      handleSubmit,
      edit,
      deleted,
      handleCancel,
      handleBatchRemove,
      defaultSpaceRows,
      uploadModalRef,
      register,
      openModal,
      closeModal,
      modalColumns,
      modalQuerys,
    };
  },
  render() {
    return (
      <div>
        <VxeBasicTable
          ref="tableRef"
          columns={this.modalColumns}
          toolbarConfig={this.toolbarConfig}
          columnConfig={{ isCurrent: false, isHover: false }}
          rowConfig={{ isCurrent: false, isHover: false }}
          // checked 为控制勾选的变量；
          // defaultSpaceRows 为默认需要勾选的行；
          v-slots={{
            action: (row) => {
              return (
                <TableAction
                  outside
                  actions={[
                    {
                      label: '删除',
                      color: 'error',
                      popConfirm: {
                        title: '是否确认删除',
                        confirm: () => {
                          this.deleted(row.row);
                          // tableRef.value?.remove(record);
                        },
                      },
                    },
                  ]}
                />
              );
            },
            upload: (row, key) => {
              return (
                <div>
                  <Tooltip placement={'bottom'}>
                    <a-button
                      onClick={() => {
                        const files = row.row[key] ? row.row[key].split('|') : [];
                        this.uploadModalRef.setFiles(files);
                        openPreviewModal();
                      }}
                    >
                      <Icon icon="bi:eye" />
                    </a-button>
                  </Tooltip>
                </div>
              );
            },
            tags: (row, key) => {
              console.log('tag', row, key);
              return (
                <div>
                  {row.row[key]
                    ? row.row[key].split('|').map((item) => {
                        return <Tag>{item}</Tag>;
                      })
                    : ''}
                </div>
              );
            },
          }}
        ></VxeBasicTable>
        <BasicModal
          onRegister={this.register}
          showFooter={this.showFooter}
          title={this.modalTitle}
          width={'50%'}
          onOk={this.handleSubmit}
          onCancle={this.handleCancel}
        >
          <VxeBasicTable
            ref="selectRef"
            columns={this.modalColumns}
            formConfig={{
              enabled: true,
              items: this.modalQuerys,
            }}
            columnConfig={{ isCurrent: false, isHover: false }}
            rowConfig={{ isCurrent: false, isHover: false }}
            proxyConfig={{
              ajax: {
                query: async ({ page, form }) => {
                  return this.search(page, form);
                },
              },
            }}
            // checked 为控制勾选的变量；
            // defaultSpaceRows 为默认需要勾选的行；
            v-slots={{
              upload: (row, key) => {
                return (
                  <div>
                    <Tooltip placement={'bottom'}>
                      <a-button
                        onClick={() => {
                          const files = row.row[key] ? row.row[key].split('|') : [];
                          this.uploadModalRef.setFiles(files);
                          openPreviewModal();
                        }}
                      >
                        <Icon icon="bi:eye" />
                      </a-button>
                    </Tooltip>
                  </div>
                );
              },
            }}
          ></VxeBasicTable>
        </BasicModal>
        <EruptUploadPreviewModal readOnly ref="uploadModalRef" onRegister={registerPreviewModal} />
      </div>
    );
  },
});
