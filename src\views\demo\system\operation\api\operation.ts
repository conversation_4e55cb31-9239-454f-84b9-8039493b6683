import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';
import { UploadApiResult } from '/@/api/sys/model/uploadModel';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  GetItems = '/newline/operation/items/',
  GetOptions = '/newline/line/operationUnissued',
  Issued = '/newline/operation/issued',
  Upload = '/newline/operation/upload/',
  GetDeptOptions = '/newline/operation/getDept',
  GetGrandParentNameOptions = '/newline/operation/getGrandParentName',
  Persist = '/newline/operation/save/temp/',
  ItemRemove = '/newline/operation/item/remove',
  ItemEdit = '/newline/operation/item/edit',
  ItemAdd = '/newline/operation/item/add',
  SaveJson = '/newline/operation/add/json',
  CopyTemplate = '/newline/operation/copyTemplate',
}

const globSetting = useGlobSetting();

export const getItemsApi = async (id) => {
  return await defHttp.get({ url: Api.GetItems + id });
};

export const getOptionsApi = async () => {
  return await defHttp.get({ url: Api.GetOptions });
};

export const getDeptOptionsApi = async () => {
  return await defHttp.get({ url: Api.GetDeptOptions });
};

export const getGrandParentNameOptionsApi = async () => {
  return await defHttp.get({ url: Api.GetGrandParentNameOptions });
};

export const addOperationJson = async (username) => {
  return await defHttp.get({ url: Api.SaveJson + `/${username}` });
};

export const issuedApi = async (id) => {
  return await defHttp.post({ url: Api.Issued + `/${id}`});
};

export const persistApi = async (key) => {
  return await defHttp.post({ url: Api.Persist + `${key}` });
};

export const itemRemove = async (id) => {
  return await defHttp.post({ url: Api.ItemRemove + `/${id}` });
};

export const itemEdit = async (params) => {
  return await defHttp.post({ url: Api.ItemEdit, params });
};

export const itemAdd = async (id, params) => {
  return await defHttp.post({ url: Api.ItemAdd + `/${id}`, params });
};

export const copyTemplate = async (id, params) => {
  return await defHttp.post({ url: Api.CopyTemplate+ `/${id}`, params });
};

export const getUpLoadApi = (tplid) => {
  debugger;
  return (params: UploadFileParams, onUploadProgress: (progressEvent: ProgressEvent) => void) => {
    return defHttp.uploadFile<UploadApiResult>(
      {
        url: `${globSetting.apiUrl}${Api.Upload}${tplid}`,
        onUploadProgress,
      },
      params,
    );
  };
};
