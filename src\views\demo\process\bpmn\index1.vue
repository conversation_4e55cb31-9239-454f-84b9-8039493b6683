<template>
  <div class="containers">
    <div class="canvas" ref="canvas" style="height: 30vh"></div>
  </div>
</template>

<script>
  // 引入自定义 Viewer
  import { CustomViewer } from '/@/utils/custom-bpmn';
  import xml from '/@/bpmns/testxml.js';

  export default {
    name: 'BpmnViewer',
    data() {
      return {
        bpmnViewer: null,
        container: null,
        canvas: null,
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        const canvas = this.$refs.canvas;
        this.bpmnViewer = new CustomViewer({
          container: canvas,
        });
        this.createNewDiagram();
      },
      async createNewDiagram() {
        try {
          // 本地 .bpmn 文件地址
          const result = await this.bpmnViewer.importXML(xml);
          const { warnings } = result;
          console.log(warnings);
          this.addMarker();
        } catch (err) {
          console.log(err.message, err.warnings);
        }
      },
    },
  };
</script>
