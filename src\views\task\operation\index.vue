<template>
  <div style="height: 900px">
    <VxeBasicTable ref="tableRef" v-bind="gridOptions">
      <template #action="{ row }">
        <TableAction outside :actions="createActions(row)" />
      </template>
    </VxeBasicTable>
    <PersonSelect
      @register="register1"
      :showFooter="true"
      title="请选择转办人"
      @ok="handleSubmit"
      @close="handleCancel"
      v-model:value="personNo"
      :query-contions="queryContions"
      :pre-conditions="preCondition"
    >
    </PersonSelect>
    <BasicModal @register="register" :showFooter="true" defaultFullscreen title="请办理">
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="详细信息">
          <Description
            class="mt-4"
            layout="vertical"
            :column="4"
            :data="descData"
            :schema="lookSchema"
          />
        </a-collapse-panel>
        <a-collapse-panel key="2" header="审批历史">
          <a-steps status="finish" direction="vertical" size="small" v-for="step in steps">
            <a-step :title="step.name">
              <template #description>
                <div>办理人：{{ step.person }}</div>
                <div>处理方式：{{ step.action }}</div>
                <div>审批意见：{{ step.opinion }}</div>
                <p>办理时间：{{ step.createDateTime }}</p>
              </template>
            </a-step>
          </a-steps>
        </a-collapse-panel>
      </a-collapse>
      <a-card style="width: 100%; margin-top: 1%" title="审批处理">
        <BasicForm style="height: 100%" @register="registerForm"></BasicForm>
      </a-card>
      <EruptUploadPreviewModal readOnly ref="uploadModalRef" @register="registerPreviewModal" />
      <template #footer>
        <VxeButton type="primary" @click="approve">确定</VxeButton>
        <VxeButton type="primary" @click="cancel">取消</VxeButton>
      </template>
    </BasicModal>

    <BasicModal @register="register2" :showFooter="true" defaultFullscreen title="请处理">
      <a-card style="width: 100%; margin-top: 1%" title="审批处理">
        <BasicForm style="height: 100%" @register="registerForm2"></BasicForm>
      </a-card>

      <template #footer>
        <VxeButton type="primary" @click="approve1">确定</VxeButton>
        <VxeButton type="primary" @click="cancel1">取消</VxeButton>
      </template>
    </BasicModal>
  </div>
</template>

<script lang="tsx">
  import { defineComponent, onMounted, ref } from 'vue';
  import { VXETable } from 'vxe-table';
  import Icon from '/@/components/Icon';
  import BpmnViewer from '/@/views/process/bpmn/index1.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import EntitySelector from '/@/components/Form/src/components/EntitySelector.vue';
  import {
    complete,
    getApprovalRecords,
    getClassName,
    getProcessComponent,
    getProcessComponentOnChange,
  } from '/@/api/process/process';
  import { ActionItem, TableAction } from '/@/components/Table';
  import { BasicTableProps, VxeBasicTable } from '/@/components/VxeTable';
  import {
    Card,
    Collapse,
    CollapsePanel,
    Steps,
    TabPane,
    Tabs,
    Tag,
    Tooltip,
  } from 'ant-design-vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useForm } from '/@/components/Form';
  import {
    getDeptOptionsApi,
    getGrandParentNameOptionsApi,
    getOptionsApi,
  } from '/@/views/demo/system/operation/api/operation';
  import { buildApi, getDetailOne } from '/@/api/erupt/erupt';
  import { getUpLoadApi } from '/@/components/EruptTable/componets';
  import { useTaskStore } from '/@/store/modules/task';
  import { Description } from '/@/components/Description';
  import { delegate, getFormTaskList } from '/@/views/task/operation/api/operation';
  import { useUserStore } from '/@/store/modules/user';
  import { useWatermark } from '/@/hooks/web/useWatermark';
  import UploadModal from '/@/components/Upload/src/UploadModal.vue';
  import BasicUpload from '/@/components/Upload/src/BasicUpload.vue';
  import EruptUploadPreviewModal from '/@/components/Form/src/components/EruptUploadPreviewModal.vue';
  import PersonSelect from '/@/views/task/components/personSelect.vue';

  export default defineComponent({
    components: {
      PersonSelect,
      EruptUploadPreviewModal,
      BasicUpload,
      UploadModal,
      EntitySelector,
      Description,
      Tooltip,
      BpmnViewer,
      Tag,
      Tabs,
      TabPane,
      [Steps.name]: Steps,
      [Steps.Step.name]: Steps.Step,
      [Card.name]: Card,
      [Collapse.name]: Collapse,
      [CollapsePanel.name]: CollapsePanel,
      BasicModal,
      VxeBasicTable,
      TableAction,
      BasicForm,
      BasicDrawer,
      VXETable,
    },
    setup(props, { attrs, emit }) {
      const tableRef = ref();
      const batchAssign = ref(false);
      const pid = ref();
      const processInstanceId = ref('');
      const personNo = ref();
      const batchIds = ref([]);
      const { createMessage } = useMessage();
      const taskStore = useTaskStore();
      const lookSchema = ref([]);
      const taskSchema = ref([]);
      const [register, { openModal, closeModal }] = useModal();
      const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
      const [register2, { openModal: openModal2, closeModal: closeModal2 }] = useModal();
      const activeKey = ref('1');
      const acts = ref([]);
      const cName = ref('');
      const step = ref('');
      const steps = ref([]);
      const actionStep = ref('');
      const descData = ref([]);
      const current = ref(0);
      const uploadModalRef = ref();
      const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();
      const bussinesKey = ref('');
      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        schemas: taskSchema,
        showActionButtonGroup: false,
      });

      const userStore = useUserStore();
      const { setWatermark, clear } = useWatermark();
      onMounted(() => {
        setWatermark(userStore.getUserInfo.username);
      });

      const [
        registerForm2,
        { resetFields: resetFields2, updateSchema: updateSchema2, validate: validate2 },
      ] = useForm({
        labelWidth: 100,
        schemas: [
          {
            field: 'option',
            label: '处理方式',
            required: true,
            component: 'Select',
            colProps: {
              span: 16,
            },
            componentProps: {
              options: [
                {
                  label: '审批',
                  value: '审批',
                },
              ],
            },
          },
          {
            field: 'message',
            label: '备注',
            required: true,
            component: 'InputTextArea',
            colProps: {
              span: 24,
            },
          },
        ],
        showActionButtonGroup: false,
      });
      const [
        registerForm1,
        { resetFields: resetFields1, updateSchema: updateSchema1, validate: validate1 },
      ] = useForm({
        labelWidth: 100,
        schemas: [
          {
            field: 'personNo',
            label: '人员',
            component: 'EntitySelector',
            componentProps: {
              valueField: 'no',
              labelField: 'name',
              className: 'Student',
              refClassName: 'person',
            },
            required: true,
            colProps: {
              span: 24,
            },
          },
        ],
        showActionButtonGroup: false,
      });
      const lineOptions = ref([]);
      const deptOptions = ref([]);
      const grandParentNameOptions = ref([]);
      const gridOptions = ref<BasicTableProps>({
        id: 'VxeTable',
        keepSource: true,
        editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
        rowConfig: { isHover: true, useKey: true, isCurrent: false },
        columnConfig: { isHover: true, isCurrent: false },
        formConfig: {
          enabled: true,
          items: [
            {
              field: 'lineId',
              title: '线路',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: lineOptions,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'grandParentName',
              title: '分类',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: grandParentNameOptions,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'parentName',
              title: '工作项目',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'workPoint',
              title: '工作要点',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'mainDeptName',
              title: '责任单位',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: deptOptions,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              span: 24,
              align: 'center',
              className: '!pr-0',
              collapseNode: true,
              itemRender: {
                name: 'AButtonGroup',
                children: [
                  {
                    props: {
                      type: 'primary',
                      content: '查询',
                      htmlType: 'submit',
                    },
                    attrs: {
                      class: 'mr-2',
                    },
                  },
                  {
                    props: {
                      type: 'default',
                      htmlType: 'reset',
                      content: '重置',
                    },
                  },
                ],
              },
            },
          ],
        },
        columns: [
          {
            type: 'checkbox',
            width: '50',
            align: 'center',
          },
          {
            title: '序号',
            type: 'seq',
            width: '50',
            align: 'center',
          },
          {
            field: 'lineId',
            showOverflow: 'title',
            title: '线路',
            sortable: false,
            width: '200',
          },
          {
            field: 'grandParentName',
            showOverflow: 'title',
            title: '分类',
            sortable: false,
            width: '250',
          },
          {
            field: 'parentName',
            showOverflow: 'title',
            title: '工作项目',
            sortable: false,
            width: '250',
          },
          {
            field: 'workPoint',
            showOverflow: 'title',
            title: '工作要点',
            sortable: false,
            width: '400',
          },
          {
            field: 'mainDeptName',
            showOverflow: 'title',
            title: '责任单位',
            sortable: false,
            width: '250',
          },
          {
            title: '操作',
            align: 'center',
            slots: {
              default: 'action',
            },
            fixed: 'right',
          },
        ],
        toolbarConfig: {
          refresh: true, // 显示刷新按钮
          import: false, // 显示导入按钮
          export: false, // 显示导出按钮
          print: false, // 显示打印按钮
          zoom: false, // 显示全屏按钮
          custom: true,
          /*buttons: [
            {
              content: '批量转办',
              buttonRender: {
                name: 'AButton',
                props: {
                  type: 'primary',
                },
                events: {
                  click: () => {
                    debugger;
                    const records = tableRef.value.getCheckboxRecords();
                    if (records.length == 0) {
                      createMessage.error('请选择需要处理的记录！');
                    } else {
                      openModal1();
                      let ids = [];
                      records.forEach((item) => {
                        ids.push(item.taskId);
                      });
                      batchIds.value = ids;
                      batchAssign.value = true;
                    }
                  },
                },
              },
            },
            {
              content: '批量处理',
              buttonRender: {
                name: 'AButton',
                props: {
                  type: 'primary',
                },
                events: {
                  click: () => {
                    debugger;
                    const records = tableRef.value.getCheckboxRecords();
                    if (records.length == 0) {
                      createMessage.error('请选择需要处理的记录！');
                    } else {
                      let flag = true;
                      let pids = [];
                      for (const recordsKey in records) {
                        pids.push(records[recordsKey].processInstanceId);
                        if (records[recordsKey].taskStep == '经办人') {
                          flag = false;
                          break;
                        }
                      }
                      if (flag) {
                        batchComplete(pids).then((res) => {
                          if (res) {
                            createMessage.success('批量处理成功');
                            document.querySelector('button[title="刷新"]').click();
                          }
                        });
                      } else {
                        createMessage.error('存在无法批量的步骤');
                      }
                    }
                  },
                },
              },
            },
          ],*/
        },
        height: 'auto',
        proxyConfig: {
          ajax: {
            query: async ({ page, form }) => {
              debugger;
              const bpmns = await getFormTaskList(page, form);
              return bpmns;
            },
            queryAll: async ({ form }) => {
              const bpmns = await getFormTaskList();
              let rows = [];
              bpmns.forEach((bpmn) => {
                rows.push({ name: bpmn.split('.bpmn')[0] });
              });
              return rows;
            },
          },
        },
      });
      function actionChange(e) {
        console.log(e, step.value);
        if (e != '') {
          actionStep.value = e;
        }
        getProcessComponentOnChange(pid.value, cName.value, { option: e ? e : '' }).then((res) => {
          if (res) {
            res.forEach((item) => {
              if (item.field == 'option') item.componentProps.onChange = (e) => actionChange(e);
              if (item.field == 'enclosure')
                item.componentProps.api = getUpLoadApi('Operation', 'enclosure');
            });
            taskSchema.value = res;
          }
        });
      }
      interface RowVO {
        name: string;
      }

      const tableData = ref<Array<RowVO>>([]);

      onMounted(async () => {
        lineOptions.value = await getOptionsApi();
        deptOptions.value = await getDeptOptionsApi();
        grandParentNameOptions.value = await getGrandParentNameOptionsApi();
      });

      return {
        tableData,
        gridOptions,
        processInstanceId,
        register,
        register1,
        register2,
        openModal,
        openModal1,
        openModal2,
        closeModal,
        closeModal1,
        closeModal2,
        registerForm,
        registerForm1,
        registerForm2,
        resetFields1,
        resetFields2,
        validate1,
        validate2,
        descData,
        activeKey,
        acts,
        lookSchema,
        taskSchema,
        steps,
        actionStep,
        personNo,
        setFieldsValue,
        tableRef,
        current,
        uploadModalRef,
        registerPreviewModal,
        lineOptions,
        grandParentNameOptions,
        deptOptions,
        cancel: () => {
          resetFields();
          actionStep.value = '';
          closeModal();
        },
        approve1: () => {},
        cancel1: () => {},
        approve: async () => {
          let values = await validate();
          const params = {
            step: step.value,
            ...values,
            bussinesKey: bussinesKey.value,
            processInstanceId: processInstanceId.value,
          };
          complete(pid.value, params, cName.value).then((res) => {
            if (res == 200) {
              createMessage.success('办理完成');
              resetFields();
              document.querySelector('button[title="刷新"]').click();
            }
            taskStore.setTaskList();
            actionStep.value = '';
            closeModal();
          });
        },
        handleSubmit: async (v) => {
          debugger;
          if (!batchAssign.value) {
            delegate([pid.value], v).then((res) => {
              if (res) {
                createMessage.success('转办成功');
                closeModal1();
                document.querySelector('button[title="刷新"]').click();
              }
            });
          } else {
            delegate(batchIds.value, v).then((res) => {
              if (res) {
                createMessage.success('转办成功');
                closeModal1();
                document.querySelector('button[title="刷新"]').click();
              }
            });
          }
        },
        queryContions: [
          {
            field: 'no',
            title: '工号',
            itemRender: {
              name: 'AInput',
              defaultValue: '',
            },
            span: 6,
          },
          {
            field: 'name',
            title: '姓名',
            itemRender: {
              name: 'AInput',
              defaultValue: '',
            },
            span: 6,
          },
          {
            span: 4,
            align: 'right',
            className: '!pr-0',
            itemRender: {
              name: 'AButtonGroup',
              children: [
                {
                  props: {
                    type: 'primary',
                    content: '查询',
                    htmlType: 'submit',
                  },
                  attrs: {
                    class: 'mr-2',
                  },
                },
                {
                  props: {
                    type: 'default',
                    htmlType: 'reset',
                    content: '重置',
                  },
                },
              ],
            },
          },
        ],
        preCondition: new Map([
          ['deptStr', userStore.getUserInfo.dept],
          ['postStr', userStore.getUserInfo.post],
        ]),
        handleCancel: () => {
          resetFields();
        },
        createActions: (record) => {
          const actions: ActionItem[] = [
            {
              label: '办理',
              onClick: async () => {
                console.log(record);
                step.value = record.taskStep;
                pid.value = record.taskId;
                processInstanceId.value = record.processInstanceId;
                let schema = [];

                getClassName(record.taskId).then(async (res) => {
                  if (res) {
                    bussinesKey.value = res;
                    const arr = res.split('.');
                    const className = arr[0];
                    cName.value = className;
                    getProcessComponent(record.taskId, className).then((res) => {
                      console.log('component', res);
                      if (res) {
                        res.forEach((item) => {
                          if (item.field == 'option')
                            item.componentProps.onChange = (e) => actionChange(e);
                        });
                        taskSchema.value = res;
                      }
                    });
                    const id = arr[1];
                    buildApi(className).then(async (res) => {
                      const {
                        eruptModel: { eruptFieldModels, eruptJson },
                        tabErupts,
                        power,
                      } = res;

                      let details = [];
                      eruptFieldModels.forEach((item) => {
                        const key = item.fieldName;
                        const title = item.eruptFieldJson.edit.title;
                        if (item.eruptFieldJson.views.length > 0) {
                          item.eruptFieldJson.views.forEach((v) => {
                            if (v.show) {
                              const k = v.column ? key + '_' + v.column : key;
                              const d = {
                                field: k,
                                label: v.title,
                              };
                              details.push(d);
                            }
                          });
                        } else {
                          if (item.eruptFieldJson.edit.show.detail_show && key !== 'id') {
                            if (item.eruptFieldJson.edit.type == 'ATTACHMENT') {
                              const d = {
                                field: key,
                                label: title,
                                render: (val) => {
                                  return (
                                    <div>
                                      <Tooltip placement={'bottom'}>
                                        <a-button
                                          onClick={() => {
                                            const files = val ? val.split('|') : [];
                                            uploadModalRef.value.setFiles(files);
                                            openPreviewModal();
                                          }}
                                        >
                                          <Icon icon={'bi:eye'} />
                                        </a-button>
                                      </Tooltip>
                                    </div>
                                  );
                                },
                              };
                              details.push(d);
                            } else {
                              const d = {
                                field: key,
                                label: title,
                              };
                              details.push(d);
                            }
                          }
                        }
                        //  formState[key] = null
                      });
                      /*details.push({
                                          field: 'message',
                                          label: '备注',
                                          required: true,
                                          component: 'InputTextArea',
                                          colProps: {span: 24},
                                        })*/
                      lookSchema.value = details;
                      const entity = await getDetailOne(className, id);
                      descData.value = entity;
                      //gridOptions.columns = columns
                      //search()
                    });
                    const records = await getApprovalRecords(record.processInstanceId);
                    steps.value = records;
                    current.value = records.length;
                    openModal();
                  }
                });
              },
            },
          ];
          /*    actions.push({
            label: '转办',
            onClick: () => {
              pid.value = record.taskId;
              openModal1();
            },
          });*/
          if (record.taskStep == '经办人')
            actions.push({
              label: '转办',
              onClick: () => {
                pid.value = record.taskId;
                openModal1();
              },
            });
          return actions;
        },
      };
    },
  });
</script>
<style scoped>
  .ant-select-disabled {
    color: #303030;
    background: #fff;
    cursor: not-allowed;
  }

  .attachment-container {
    display: flex;
    align-items: center;
  }

  .attachment-container span {
    margin-left: 60px; /* 增加间距 */
    margin-right: 10px; /* 增加间距 */
  }
</style>
