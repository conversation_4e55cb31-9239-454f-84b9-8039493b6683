import { defHttp } from '/@/utils/http/axios/erupt-request';
import { BasicFetchResult } from '/@/api/model/baseModel';

export enum Api {
  ERUPT_BUILD = 'build/' /**构建列表**/,
  ERUPT_QUERY_TABLE = 'data/table/' /**列表查询**/,
  ERUPT_QUERY_TREE = 'data/tree/' /**列表查询**/,
  ERUPT_TAB_ADD = 'data/modify/tab-add/',
  ERUPT_TAB_MODIFY = 'data/modify/tab-update/',
  ERUPT_TAB_TREE_BUILD = 'data/tab/tree/' /**构建树**/,
  ERUPT_GET_ONE = 'data/' /**GETONE**/,
  ERUPT_MODIFY = 'data/modify/' /**保存数据**/,
  ERUPT_EXCEL = 'excel/' /**excel操作**/,
  ERUPT_DEPDENT_TREE = 'data/depend-tree/' /**左树**/,
  ERUPT_COMMON_TREE = 'data/tree/',
  ERUPT_UPLOAD = 'file/upload/',
  ERUPT_EXCEL_EXPORT = 'excel/export/',
  ERUPT_fIND_CHECK_BOX = '',
}

/**
 * @description: Get sample options value
 */
export const buildApi = (className: String) =>
  defHttp.get({ url: Api.ERUPT_BUILD + className, headers: fetchHeader(className, '') });

export const getRefClass = (className: String, fieldName: String) =>
  defHttp.get({
    url: Api.ERUPT_BUILD + `${className}/${fieldName}/refclass`,
    headers: fetchHeader(className, ''),
  });
export const tableQueryApi = (className: String, data) =>
  defHttp
    .post({ url: Api.ERUPT_QUERY_TABLE + className, data, headers: fetchHeader(className, '') })
    .then((res) => {
      // @ts-ignore
      res.list.forEach((item) => (item.key = item.id));
      const obj: BasicFetchResult<Object> = { items: res.list, total: res.total };
      return new Promise((resolve, reject) => {
        try {
          console.log('tabledata', obj);
          resolve(obj);
        } catch (err) {
          reject(err || new Error('request error!'));
        }
      });
    });

export const treeQueryApi = (className: String) =>
  defHttp.get({ url: Api.ERUPT_QUERY_TREE + className, headers: fetchHeader(className, '') });
export const upload = (className: String, params) =>
  defHttp.uploadFile({ url: `${Api.ERUPT_UPLOAD}${className}/pic`, params });
export const refBuildApi = (className: String, refName: String) =>
  defHttp.get({ url: `build/${className}/${refName}`, headers: fetchHeader(className, '') });
export const refBuildTreeApi = (className: String, refName: String) =>
  defHttp.get({
    url: `${Api.ERUPT_TAB_TREE_BUILD}${className}/${refName}`,
    headers: fetchHeader(className, ''),
  });

export const refTreeBuildApi = (className: String, refName: String) =>
  defHttp
    .get({
      url: `${Api.ERUPT_TAB_TREE_BUILD}${className}/${refName}`,
      headers: fetchHeader(className, ''),
    })
    .then((res) => {
      return new Promise((resolve, reject) => {
        try {
          const r = { list: res };
          resolve(r);
        } catch (err) {
          reject(err || new Error('request error!'));
        }
      });
    });
export const refQueryApi = (className: String, refName: String, data) =>
  defHttp
    .post({
      url: `data/${className}/reference-table/${refName}?tabRef=false`,
      headers: fetchHeader(className, ''),
      data: JSON.parse(JSON.stringify(data).replace('pageNo', 'pageIndex')),
    })
    .then((res) => {
      // @ts-ignore
      res.list.forEach((item) => (item.key = item.id));
      const obj: BasicFetchResult<Object> = { items: res.list, total: res.total };
      return new Promise((resolve, reject) => {
        try {
          console.log('tabledata', obj);
          resolve(obj);
        } catch (err) {
          reject(err || new Error('request error!'));
        }
      });
    });

export const tabAdd = (className: String, tabName: String, data) =>
  defHttp.post({
    url: `${Api.ERUPT_TAB_ADD}${className}/${tabName}`,
    data,
    headers: fetchHeader(className, ''),
  });
export const tabUpdate = (className: String, tabName: String, data) =>
  defHttp.post({
    url: `${Api.ERUPT_TAB_MODIFY}${className}/${tabName}`,
    data,
    headers: fetchHeader(className, ''),
  });

export const getOne = (className: String, id: number) =>
  defHttp.get({
    url: `${Api.ERUPT_GET_ONE}${className}/${id}`,
    headers: fetchHeader(className, ''),
  });
export const getDetailOne = (className: String, id: number) =>
  defHttp.get({
    url: `${Api.ERUPT_GET_ONE}${className}/detail/${id}`,
    headers: fetchHeader(className, ''),
  });

export const save = (className: String, data, link) =>
  defHttp.post({
    url: `${Api.ERUPT_MODIFY}${className}`,
    data,
    headers: fetchHeader(className, '', link),
  });
export const update = (className: String, data, link) =>
  defHttp.post({
    url: `${Api.ERUPT_MODIFY}${className}/update`,
    data,
    headers: fetchHeader(className, '', link),
  });
export const remove = (className: String, id) =>
  defHttp.delete({
    url: `${Api.ERUPT_MODIFY}${className}/${id}`,
    headers: fetchHeader(className, ''),
  });
export const batchRemove = (className: String, data) =>
  defHttp.post({
    url: `${Api.ERUPT_MODIFY}${className}/delete`,
    data,
    headers: { erupt: fetchHeader(className, '').erupt },
  });

export const excel_export = (className: String, data) => {
  return defHttp.post({
    url: `${Api.ERUPT_EXCEL}export/${className}`,
    data: JSON.stringify(data),
    responseType: 'arraybuffer',
  });
};

export const getTemplate = (className: String) =>
  defHttp.get({
    url: `${Api.ERUPT_EXCEL}template/${className}`,
    headers: fetchHeader(className, ''),
    responseType: 'arraybuffer',
    observe: 'events',
  });
export const findCheckBox = (className: String, fieldName: String) =>
  defHttp.get({
    url: `data/${className}/checkbox/${fieldName}`,
    headers: fetchHeader(className, ''),
  });
/**
 * tree
 */
export const refTreeQueryApi = async (params) => {
  const { className, refName } = params;
  let result = await defHttp.get({
    url: `/data/${className}/reference-tree/${refName}`,
    headers: fetchHeader(className, ''),
  });
  fetchTree(result);
  return { list: result };
};

export const getLeftTree = (className) => {
  return new Promise((resolve, reject) => {
    try {
      defHttp
        .get({ url: `${Api.ERUPT_DEPDENT_TREE + className}`, headers: fetchHeader(className, '') })
        .then((res) => {
          console.log('tree', res);
          // fetchTree(res)

          resolve(res);
        });
    } catch (err) {
      reject(err || new Error('request error!'));
    }
  });
};

export const getCommonTree = (className) => {
  return new Promise((resolve, reject) => {
    try {
      defHttp
        .get({ url: `${Api.ERUPT_COMMON_TREE + className}`, headers: fetchHeader(className, '') })
        .then((res) => {
          fetchTree(res);
          console.log('tree', res);
          resolve(res);
        });
    } catch (err) {
      reject(err || new Error('request error!'));
    }
  });
};
//export const getLeftTree = (className:String) => defHttp.get({url:`${Api.ERUPT_DEPDENT_TREE + className}`,headers:fetchHeader(className,'')})
export const fetchTree = (arr) => {
  arr.forEach((item) => {
    item.title = item.label;
    item.value = item.id;
    item.key = item.id;
    if (item.children) {
      fetchTree(item.children);
    }
  });
};
export const fetchHeader = (erupt, eruptparent, link) => {
  if (link) {
    if (!eruptparent) {
      return { erupt };
    }
    return { erupt, eruptparent, link };
  } else {
    if (!eruptparent) {
      return { erupt };
    }
    return { erupt, eruptparent };
  }
};
