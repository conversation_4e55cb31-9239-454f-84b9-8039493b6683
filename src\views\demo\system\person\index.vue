<template>
  <erupt-table class-name="Person" :extra-action="extraActions" :buttonRenders="buttonRenders" />
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  // import eruptTable from "../../../../components/EruptTable/eruptTable";
  import eruptTreeTable from '/@/components/EruptTreeTable/eruptTreeTable';
  import eruptTable from '/@/components/EruptTable/eruptTable';
  import { createAccount, UpdatePeople } from "/@/api/demo/system";
  import { useMessage } from '/@/hooks/web/useMessage';
  export default defineComponent({
    name: 'ChangePassword',
    components: { eruptTreeTable, eruptTable },
    setup() {
      const { notification, createConfirm } = useMessage();
      const buttonRenders = [
        {
          content: '人员同步',
          buttonRender: {
            name: 'AButton',
            props: {
              type: 'warning',
            },
            events: {
              click: () => {
                createConfirm({
                  title: '人员同步',
                  content: '确定要人员同步吗？',
                  onOk() {
                    updatePeople();
                  },
                  iconType: 'warning',
                });
              },
            },
          },
        },
        
      ];
      function updatePeople() {
        console.log('开始人员同步');
        UpdatePeople().then(() => {
          notification.success({
            message: '人员同步',
            description: '人员同步成功',
          });
        });
      }
      
      const overwriteAction = {
        add: () => {
          console.log('add');
        },
      };
      const extraActions = {
        nums: 1,
        actions: (row) => {
          return [
            {
              label: '创建账号',
              onClick: () => {
                console.log(row.row.id);
                createAccount(row.row.id).then(() => {
                  notification.success({
                    message: '创建账号',
                    description: '创建账号成功',
                  });
                });
              },
            },
          ];
        },
      };
      const dynamicController = {
        age: (form,e) => {
          const { formModel, formActionType } = form
          console.log(form,e)
          formActionType.updateSchema({
            field: 'tags',
            componentProps: (f) => {
              return { options:[{label: '111', value: '123123'}], onChange: (e) => {
                  dynamicController.tags(f)
                }}
            },
          })
        },
        tags:(form) => {
          console.log(form)
        }
      }
      return {
        buttonRenders,
        overwriteAction,
        dynamicController,
        extraActions
      };
    },
  });
</script>
