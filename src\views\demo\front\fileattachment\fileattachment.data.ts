import { BasicColumn, FormSchema } from '/@/components/Table';
import { uploadApi } from '/@/api/demo/FileattachmentUpload';
import { fileCategoryOptionsListApi } from '/@/api/demo/OptionsSelect';

export const columns: BasicColumn[] = [
  {
    title: '栏目名称',
    dataIndex: 'categoryName',
    width: 200,
  },
  {
    title: '文件名称',
    dataIndex: 'attachmentName',
    width: 300,
  },
  {
    title: '发文单位',
    dataIndex: 'fileUnit',
    width: 100,
  },
  {
    title: '发文日期',
    dataIndex: 'fileDate',
    width: 100,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'attachmentName',
    label: '文件名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'fileUnit',
    label: '发文单位',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    required: false,
    component: 'Input',
    show: false,
  },
  {
    field: 'attachmentName',
    label: '文件名称',
    required: true,
    component: 'Input',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'fileUnit',
    label: '发文单位',
    required: true,
    component: 'Input',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'categoryId',
    component: 'ApiSelect',
    label: '规章栏目',
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      api: fileCategoryOptionsListApi,
      resultField: 'list',
      // use name as label
      labelField: 'label',
      // use id as value
      valueField: 'value',
      // not request untill to select
      placeholder: '请选择规章制度栏目',
    },
  },
  {
    field: 'fileDate',
    label: '发文日期',
    required: true,
    component: 'DatePicker',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'filePath',
    label: '文件路径',
    required: false,
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'file',
    label: '上传文件',
    required: false,
    component: 'Upload',
    componentProps: ({ formModel }) => {
      return {
        api: uploadApi,
        maxSize: 20,
        onChange: (list: string[]) => {
          console.log(JSON.stringify(list));
          // or
          formModel.filePath = list[0];
        },
      };
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'note',
    label: '文件备注',
    required: false,
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
    },
    colProps: {
      span: 24,
    },
  },
];
