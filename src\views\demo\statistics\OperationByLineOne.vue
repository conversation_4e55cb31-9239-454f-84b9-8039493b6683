<template>
  <Card title="运营筹备线路完成率" :loading="loading">
    <template #extra>
      <div style="height: 32px" />
      <a-space direction="horizontal">
        <a-select
          v-if="userStore.getUserInfo.dept === '新线管理部'"
          v-model="dept"
          style="width: 200px"
          placeholder="请选择部门"
          :options="deptOpt"
          @change="setDept"
          :loading="loading"
          :disabled="loading"
          allowClear
        ></a-select>
      </a-space>
    </template>
    <div v-show="isShow > 0" v-loading="loadings" ref="chartRef" :style="{ width, height }"></div>
    <div v-show="isShow <= 0" style="text-align: center; margin-top: 100px; height: 300px">
      <a-empty />
    </div>
  </Card>
</template>

<script setup lang="ts">
  import { Card } from 'ant-design-vue';
  import { Ref, ref, watch } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { useUserStore } from '/@/store/modules/user';
  import { getOperationByLine } from '/@/views/dashboard/workbench/components/api/home';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    loading: Boolean,
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '400px',
    },
    lineOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    deptOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });
  const line = ref('');
  const isShow = ref(0);
  const dept = ref('');
  const deptLabel = ref('');
  const loadings = ref(true);
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const userStore = useUserStore();
  const router = useRouter();

  const setLine = (val) => {
    loadings.value = true;
    line.value = val;
    getChartDate();
  };
  const setDept = (val) => {
    loadings.value = true;
    const selectedOption = props.deptOpt.find((option) => option.value === val);
    if (selectedOption) {
      deptLabel.value = selectedOption.label; // 假设你有一个变量 lineLabel 来保存 label
    }
    dept.value = val;
    getChartDate();
  };

  watch(
    () => props.loading,
    () => {
      if (props.loading) {
        return;
      }
      getChartDate();
    },
    { immediate: true },
  );

  const getChartDate = async () => {
    const data = {
      line: line.value,
      dept: dept.value,
    };
    try {
      const res = await getOperationByLine(data);
      updateChart(res);
      isShow.value = res.line.length - 1;
      loadings.value = false;
    } catch (error) {
      console.log(error);
    }
  };

  function updateChart(value) {
    setOptions({
      xAxis: {
        type: 'category',
        data: value.line,
        name: '线路', // 设置x轴名称
        nameLocation: 'end', // 名称显示的位置
        nameTextStyle: {
          color: '#000', // 名称的颜色
          fontSize: 14, // 名称的字体大小
        },
        axisLabel: {
          /*formatter: function (value) {
            return value.length > 3 ? value.slice(0, 3) + '...' : value;
          },*/
          formatter: function (value) {
            return value.length > 8 ? value.slice(0, 8) + '\n' : value;
          },
        },
      },
      yAxis: {
        name: '完成率', // 设置y轴名称
        nameLocation: 'end', // 名称显示的位置
        nameGap: 21, // 调整名称与轴线之间的距离
        nameTextStyle: {
          color: '#333',
          fontSize: 14,
          fontWeight: 'bold',
          backgroundColor: '#f8f8f8', // 添加背景色
          borderColor: '#d9d9d9', // 边框颜色
          borderWidth: 1, // 边框宽度
          borderRadius: 4, // 圆角边框
          padding: [4, 8], // 内边距
        },
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
        axisLabel: {
          formatter: function (value) {
            return value + '%';
          },
        },
      },
      grid: {
        left: '10%', // 左边距
        right: '12%', // 右边距
        top: '10%',
        bottom: '15%',
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          const index = params.dataIndex;
          const lineName = value.line[index];
          const uncompletedRate = params.value; // 当前是未完成率
          const completedRate = 100 - uncompletedRate; // 计算完成率

          // 获取完成数量和总数量
          const completed = value.completed ? value.completed[index] : 0;
          const total = value.total ? value.total[index] : 0;
          const lineLabel = total - completed;

          return `
            ${lineName}<br/>
            完成率: ${uncompletedRate.toFixed(2)}%<br/>
            完成数量: ${lineLabel}<br/>
            总数量: ${total}
          `;
        },
      },
      series: [
        {
          name: '完成率',
          type: 'bar', // 显示柱状图
          data: value.data, // 根据选中的线路获取数据
          barWidth: '30%',
          itemStyle: {
            color: function (params) {
              // 根据索引返回不同的颜色
              const colorList = [
                '#5793f3',
                '#d14a61',
                '#675bba',
                '#e8684a',
                '#2ec7c9',
                '#b6a2de',
                '#5ab1ef',
                '#ffb980',
                '#d87a80',
              ];
              return colorList[params.dataIndex];
            },
          },
          label: {
            show: true,
            position: 'top', // 或者其他位置如：'inside', 'right'
            formatter: function (params) {
              return params.value.toFixed(2) + '%';
            }, // 显示数值并保留两位小数
            color: '#000', // 文本颜色
            fontSize: 14, // 字体大小
          },
        },
      ],
      graphic: [
        {
          type: 'group',
          right: 20, // 保持右侧位置
          top: 25, // 保持顶部位置
          children: value.line
            .map((state, index) => {
              const colorList = [
                '#5793f3',
                '#d14a61',
                '#675bba',
                '#e8684a',
                '#2ec7c9',
                '#b6a2de',
                '#5ab1ef',
                '#ffb980',
                '#d87a80',
              ];
              const color = colorList[index % colorList.length];
              return [
                {
                  type: 'text',
                  style: {
                    text: state,
                    fill: '#000000D9',
                    fontSize: 12, // 减小字体大小
                    textAlign: 'left',
                  },
                  top: `${index * 20}px`,
                  right: 45, // 距离右侧 30px
                  onclick: () => {
                    router.push({
                      path: '/newLine/operation/details',
                      query: {
                        line: props.lineOpt.find((option) => option.label === state).value,
                        dept: deptLabel.value,
                        state: 6,
                      },
                    });
                  },
                },
                {
                  type: 'rect',
                  shape: {
                    width: 25,
                    height: 14,
                    r: 5, // 添加圆角半径
                  },
                  style: {
                    fill: color,
                  },
                  top: `${index * 20}px`,
                  right: 19, // 距离右侧 15px
                  onclick: () => {
                    router.push({
                      path: '/newLine/operation/details',
                      query: {
                        line: props.lineOpt.find((option) => option.label === state).value,
                        dept: deptLabel.value,
                        state: 6,
                      },
                    });
                  },
                },
              ];
            })
            .flat(),
        },
      ],
    });
  }
</script>
