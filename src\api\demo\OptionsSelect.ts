import { defHttp } from '/@/utils/http/axios';
import { DemoOptionsGetResultModel, selectParams } from './model/optionsModel';
enum Api {
  FileCategory_OPTIONS_LIST = '/system/select/getFileCategoryOptions',
  NewsCategory_OPTIONS_LIST = '/system/select/getNewsCategoryOptions',
}

/**
 * @description: Get sample options value
 */
export const fileCategoryOptionsListApi = (params?: selectParams) =>
  defHttp.get<DemoOptionsGetResultModel[]>({ url: Api.FileCategory_OPTIONS_LIST, params });

export const newsCategoryOptionsListApi = (params?: selectParams) =>
  defHttp.get<DemoOptionsGetResultModel[]>({ url: Api.NewsCategory_OPTIONS_LIST, params });
