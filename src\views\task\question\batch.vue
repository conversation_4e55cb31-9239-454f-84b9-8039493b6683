<template>
  <div style="height: 900px">
    <VxeBasicTable ref="tableRef" v-bind="gridOptions">
      <template #action="{ row }">
        <TableAction outside :actions="createActions(row)" />
      </template>
    </VxeBasicTable>
    <PersonSelect
      @register="register1"
      :showFooter="true"
      title="请选择转办人"
      @ok="handleSubmit"
      v-model:value="personNo"
      :query-contions="queryContions"
      :pre-conditions="preCondition"
    >
    </PersonSelect>
    <BasicModal
      @register="register"
      :showFooter="true"
      defaultFullscreen
      title="请办理"
      @ok="handleSubmit"
      @close="handleCancel"
    >
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="详细信息">
          <Description
            class="mt-4"
            layout="vertical"
            :column="4"
            :data="descData"
            :schema="lookSchema"
          />
        </a-collapse-panel>
        <a-collapse-panel key="2" header="审批历史">
          <a-steps status="finish" direction="vertical" size="small" v-for="step in steps">
            <a-step :title="step.name">
              <template #description>
                <div>意见：{{ step.opinion }}</div>
                <div>办理人：{{ step.person }}</div>
                <p>办理时间：{{ step.createDateTime }}</p>
              </template>
            </a-step>
          </a-steps>
        </a-collapse-panel>
      </a-collapse>
      <a-card style="width: 100%; margin-top: 1%" title="审批处理">
        <BasicForm style="height: 100%" @register="registerForm"></BasicForm>
      </a-card>

      <template #footer>
        <VxeButton type="primary" @click="approve">确定</VxeButton>
        <VxeButton type="primary" @click="cancel">取消</VxeButton>
      </template>
    </BasicModal>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    PropType,
    ref,
    onMounted,
    watchEffect,
    computed,
    unref,
    watch,
    h,
  } from 'vue';
  import { VXETable, VxeTableInstance } from 'vxe-table';
  import BpmnViewer from '/@/views/process/bpmn/index1.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import {
    complete,
    deploy,
    deployedList,
    getApprovalRecords,
    getClassName,
    getFlowsTask,
    getProcessComponent,
    getProcessComponentOnChange,
    getProcessOne,
    getTasksListByUser,
    moduleListApi,
    read,
  } from '/@/api/process/process';
  import { ActionItem, TableAction } from '/@/components/Table';
  import { BasicTableProps, VxeBasicTable } from '/@/components/VxeTable';
  import { useRouter } from 'vue-router';
  import {
    Modal,
    Tag,
    Tooltip,
    Steps,
    Step,
    Tabs,
    TabPane,
    Card,
    Collapse,
    CollapsePanel,
  } from 'ant-design-vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { processDef, task } from '/@/views/process/definition/processDefData';
  import { useForm } from '/@/components/Form';
  import { getOptionsApi } from '/@/views/demo/system/assembly/api/assembly';
  import { buildApi, getDetailOne, getOne } from '/@/api/erupt/erupt';
  import {
    getColFomatter,
    getComponent,
    getComponentProps,
    getSearchComponent,
  } from '/@/components/EruptTable/componets';
  import { taskSchemas } from '/@/views/demo/page/form/high/data';
  import { useTaskStore } from '/@/store/modules/task';
  import { Description } from '/@/components/Description';
  import { getFormTaskList, getTasksList } from '/src/views/task/question/api/question';
  import EntitySelector from '/@/components/Form/src/components/EntitySelector.vue';
  import { delegate, delegateQ, getFormTaskListBatch } from '/@/views/task/assembly/api/assembly';
  import PersonSelect from '/@/views/task/components/personSelect.vue';
  import { useUserStore } from '/@/store/modules/user';

  export default defineComponent({
    components: {
      PersonSelect,
      EntitySelector,
      Description,
      Tooltip,
      BpmnViewer,
      Tag,
      Tabs,
      TabPane,
      [Steps.name]: Steps,
      [Steps.Step.name]: Steps.Step,
      [Card.name]: Card,
      [Collapse.name]: Collapse,
      [CollapsePanel.name]: CollapsePanel,
      BasicModal,
      VxeBasicTable,
      TableAction,
      BasicForm,
      BasicDrawer,
      VXETable,
    },
    setup(props, { attrs, emit }) {
      const tableRef = ref();
      const userStore = useUserStore();
      const batchAssign = ref(false);
      const batchIds = ref([]);
      const pid = ref();
      const { createMessage } = useMessage();
      const taskStore = useTaskStore();
      const lookSchema = ref([]);
      const taskSchema = ref([]);
      const personNo = ref();
      const [register, { openModal, closeModal }] = useModal();
      const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
      const activeKey = ref('1');
      const acts = ref([]);
      const cName = ref('');
      const step = ref('');
      const steps = ref([]);
      const descData = ref([]);
      const current = ref(0);
      const bussinesKey = ref('');
      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        schemas: taskSchema,
        showActionButtonGroup: false,
      });
      const lineOption = ref([]);
      const gridOptions = ref<BasicTableProps>({
        id: 'VxeTable',
        keepSource: true,
        editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
        rowConfig: { isHover: true, useKey: true, isCurrent: false },
        columnConfig: { isHover: true, isCurrent: false },
        formConfig: {
          enabled: true,
          items: [
            {
              field: 'lineIds',
              title: '所属线路',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: lineOption,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'code',
              title: '问题编码',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'typeId',
              title: '所属专业',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'reportDeptId',
              title: '提报部门',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'manageDeptId',
              title: '专业管理单位',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'center',
              folding: true,
            },
            {
              field: 'questionType',
              title: '问题类型',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              field: 'approveStatus',
              title: '当前状态',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              span: 24,
              align: 'center',
              className: '!pr-0',
              collapseNode: true,
              itemRender: {
                name: 'AButtonGroup',
                children: [
                  {
                    props: {
                      type: 'primary',
                      content: '查询',
                      htmlType: 'submit',
                    },
                    attrs: {
                      class: 'mr-2',
                    },
                  },
                  {
                    props: {
                      type: 'default',
                      htmlType: 'reset',
                      content: '重置',
                    },
                  },
                ],
              },
            },
          ],
        },
        columns: [
          {
            type: 'checkbox',
            width: '50',
            align: 'center',
          },
          {
            title: '序号',
            type: 'seq',
            width: '50',
            align: 'center',
          },
          {
            field: 'createUserId',
            showOverflow: 'title',
            title: '提报人',
            sortable: false,
            width: '150',
          },
          {
            field: 'lineIds',
            showOverflow: 'title',
            title: '所属线路',
            sortable: false,
            width: '150',
          },
          {
            field: 'reportDept',
            showOverflow: 'title',
            title: '提报部门',
            sortable: false,
            width: '150',
          },
          {
            field: 'createTime',
            showOverflow: 'title',
            title: '提报时间',
            sortable: false,
            width: '150',
          },
          {
            field: 'typeId',
            showOverflow: 'title',
            title: '所属专业',
            sortable: false,
            width: '150',
          },
          {
            field: 'code',
            showOverflow: 'title',
            title: '问题编码',
            sortable: false,
            width: '150',
          },
          {
            field: 'manageDept',
            showOverflow: 'title',
            title: '专业管理单位',
            sortable: false,
            width: '150',
          },
          {
            field: 'questionType',
            showOverflow: 'title',
            title: '问题类型',
            sortable: false,
            width: '150',
          },
          {
            field: 'approveStatus',
            showOverflow: 'title',
            title: '当前状态',
            sortable: false,
            width: '150',
          },
          {
            title: '操作',
            align: 'center',
            width: 230,
            slots: {
              default: 'action',
            },
            fixed: 'right',
          },
        ],
        toolbarConfig: {
          refresh: true, // 显示刷新按钮
          import: false, // 显示导入按钮
          export: false, // 显示导出按钮
          print: false, // 显示打印按钮
          zoom: false, // 显示全屏按钮
          custom: true,
          buttons: [
            {
              content: '批量转办',
              buttonRender: {
                name: 'AButton',
                props: {
                  type: 'primary',
                },
                events: {
                  click: () => {
                    debugger;
                    const records = tableRef.value.getCheckboxRecords();
                    if (records.length == 0) {
                      createMessage.error('请选择需要处理的记录！');
                    } else {
                      openModal1();
                      let ids = [];
                      records.forEach((item) => {
                        ids.push(item.taskId);
                      });
                      batchIds.value = ids;
                      batchAssign.value = true;
                    }
                  },
                },
              },
            },
          ],
        },
        height: 'auto',
        proxyConfig: {
          ajax: {
            query: async ({ page, form }) => {
              debugger;
              const bpmns = await getFormTaskListBatch(page, form);
              return bpmns;
            },
            queryAll: async ({ form }) => {
              const bpmns = await getFormTaskListBatch();
              let rows = [];
              bpmns.forEach((bpmn) => {
                rows.push({ name: bpmn.split('.bpmn')[0] });
              });
              return rows;
            },
          },
        },
      });

      function actionChange(e) {
        console.log(e, step.value);
        getProcessComponentOnChange(pid.value, cName.value, { option: e ? e : '' }).then((res) => {
          if (res) {
            res.forEach((item) => {
              if (item.field == 'option') item.componentProps.onChange = (e) => actionChange(e);
            });
            taskSchema.value = res;
          }
        });
      }

      interface RowVO {
        name: string;
      }

      const tableData = ref<Array<RowVO>>([]);

      onMounted(async () => {
        const options = await getOptionsApi();
        lineOption.value = options;
      });

      return {
        tableData,
        lineOption,
        gridOptions,
        personNo,
        register,
        register1,
        openModal,
        openModal1,
        closeModal,
        closeModal1,
        registerForm,
        descData,
        activeKey,
        acts,
        lookSchema,
        taskSchema,
        steps,
        setFieldsValue,
        current,
        tableRef,
        cancel: () => {
          closeModal();
        },
        approve: async () => {
          let values = await validate();
          const params = {
            step: step.value,
            ...values,
            bussinesKey: bussinesKey.value,
          };
          complete(pid.value, params, cName.value).then((res) => {
            if (res == 200) {
              createMessage.success('办理完成');
              resetFields();
              document.querySelector('button[title="刷新"]').click();
            }
            taskStore.setTaskList();
            closeModal();
          });
        },
        handleSubmit: async () => {
          /* let values = await validate1();
         console.log(values)*/
          if (!batchAssign.value) {
            delegateQ([pid.value], personNo.value.no).then((res) => {
              if (res) {
                createMessage.success('转办成功');
                closeModal1();
                document.querySelector('button[title="刷新"]').click();
              }
            });
          } else {
            delegateQ(batchIds.value, personNo.value.no).then((res) => {
              if (res) {
                createMessage.success('转办成功');
                closeModal1();
                document.querySelector('button[title="刷新"]').click();
              }
            });
          }
        },
        batchAssign,
        batchIds,
        queryContions: [
          {
            field: 'no',
            title: '工号',
            itemRender: {
              name: 'AInput',
              defaultValue: '',
            },
            span: 6,
          },
          {
            field: 'name',
            title: '姓名',
            itemRender: {
              name: 'AInput',
              defaultValue: '',
            },
            span: 6,
          },
          {
            span: 4,
            align: 'right',
            className: '!pr-0',
            itemRender: {
              name: 'AButtonGroup',
              children: [
                {
                  props: {
                    type: 'primary',
                    content: '查询',
                    htmlType: 'submit',
                  },
                  attrs: {
                    class: 'mr-2',
                  },
                },
                {
                  props: {
                    type: 'default',
                    htmlType: 'reset',
                    content: '重置',
                  },
                },
              ],
            },
          },
        ],
        preCondition: new Map([
          ['deptStr', userStore.getUserInfo.dept],
          ['postStr', userStore.getUserInfo.post],
        ]),
        handleCancel: () => {
          resetFields();
        },
        createActions: (record) => {
          const actions: ActionItem[] = [
            {
              label: '办理',
              onClick: async () => {
                console.log(record);
                step.value = record.taskStep;
                pid.value = record.taskId;
                let schema = [];

                getClassName(record.taskId).then(async (res) => {
                  if (res) {
                    bussinesKey.value = res;
                    const arr = res.split('.');
                    const className = arr[0];
                    cName.value = className;
                    getProcessComponent(record.taskId, className).then((res) => {
                      console.log('component', res);
                      if (res) {
                        res.forEach((item) => {
                          if (item.field == 'option')
                            item.componentProps.onChange = (e) => actionChange(e);
                        });
                        taskSchema.value = res;
                      }
                    });
                    const id = arr[1];
                    buildApi(className).then(async (res) => {
                      const {
                        eruptModel: { eruptFieldModels, eruptJson },
                        tabErupts,
                        power,
                      } = res;
                      let details = [];
                      eruptFieldModels.forEach((item) => {
                        const key = item.fieldName;
                        const title = item.eruptFieldJson.edit.title;
                        if (item.eruptFieldJson.views.length > 0) {
                          item.eruptFieldJson.views.forEach((v) => {
                            if (v.show) {
                              const k = v.column ? key + '_' + v.column : key;
                              const d = {
                                field: k,
                                label: v.title,
                              };
                              details.push(d);
                            }
                          });
                        } else {
                          if (item.eruptFieldJson.edit.show.detail_show && key !== 'id') {
                            const d = {
                              field: key,
                              label: title,
                            };
                            details.push(d);
                          }
                        }
                        //  formState[key] = null
                      });
                      /*details.push({
                      field: 'message',
                      label: '备注',
                      required: true,
                      component: 'InputTextArea',
                      colProps: {span: 24},
                    })*/
                      lookSchema.value = details;

                      const entity = await getDetailOne(className, id);
                      descData.value = entity;

                      //gridOptions.columns = columns
                      //search()
                    });
                    const records = await getApprovalRecords(record.processInstanceId);
                    steps.value = records;
                    current.value = records.length;
                    openModal();
                  }
                });
              },
            },
          ];
          /*if (record.taskStep == '新线部工程师') actions.push(
            {
              label: '转办',
              onClick: () => {
                pid.value = record.taskId
                openModal1()
              }
            }
        )*/
          return actions;
        },
      };
    },
  });
</script>
<style scoped>
  .ant-select-disabled {
    color: #303030;
    background: #fff;
    cursor: not-allowed;
  }
</style>
