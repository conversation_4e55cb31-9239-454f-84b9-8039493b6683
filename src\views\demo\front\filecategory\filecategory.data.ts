import { BasicColumn, FormSchema } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '栏目名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '栏目编号',
    dataIndex: 'code',
    width: 300,
  },
  {
    title: '备注',
    dataIndex: 'note',
    width: 300,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '栏目名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'code',
    label: '栏目编号',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    required: false,
    component: 'Input',
    show: false,
  },
  {
    field: 'name',
    label: '栏目名称',
    required: true,
    component: 'Input',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'code',
    label: '栏目编号',
    required: true,
    component: 'Input',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'note',
    label: '栏目备注',
    required: false,
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
    },
    colProps: {
      span: 24,
    },
  },
];
