<template>
<Select mode="tags" v-model:value="selectValue" :options="options"></Select>
</template>

<script lang="ts">
import {defineComponent, watch, ref } from 'vue';
import {Select} from 'ant-design-vue'
export default defineComponent({
  name: 'TagSelect',
  components: { Select },
  props: {
    value:{type:String},
    options: {},
    joinSeparator: {type:String}
  },
  emits: ['change', 'update:value'],
  setup(props, { emit }) {
    const {joinSeparator} = props
    const selectValue = ref([])
    watch(
      () => props.value,
      (val) => {
        if (val) {
          selectValue.value = val.split(joinSeparator)
        } else {
          selectValue.value = []
        }
      },
      { deep: true },
    );

    watch(
      () => selectValue.value,
      (val) => {
        if (val && val.length > 0) {
          const join = val.join(joinSeparator)
          emit('update:value', join);
          emit('change', join);
        } else {
          emit('update:value', '');
          emit('change', '');
        }
      },
      { deep: true },
    );


    return { selectValue };
  },
});
</script>

<style scoped lang="less">

</style>
