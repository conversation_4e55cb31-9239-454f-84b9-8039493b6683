import { VxeFormItemProps, VxeGridPropTypes } from '/@/components/VxeTable';

export const processDef: VxeGridPropTypes.Columns = [
  {
    title: '序号',
    type: 'seq',
    fixed: 'left',
    width: '50',
    align: 'center',
  },
  {
    title: 'ID',
    field: 'id',
    width: 150,
    showOverflow: 'tooltip',
    fixed: 'left',
  },
  {
    title: '流程名称',
    field: 'name',
  },
  {
    title: '流程版本',
    field: 'version',
  },
  {
    width: 160,
    title: '操作',
    align: 'center',
    slots: { default: 'action' },
    fixed: 'right',
  },
];

export const done: VxeGridPropTypes.Columns = [
  {
    title: '序号',
    type: 'seq',
    fixed: 'left',
    width: '50',
    align: 'center',
  },
  {
    title: '流程名称',
    field: 'defName',
    width: 300,
    showOverflow: 'tooltip',
    fixed: 'left',
  },
  {
    title: '当前步骤',
    field: 'name',
    width: 150,
  },
  {
    title: '描述',
    field: 'description',
    width: 750,
    showOverflow: 'tooltip',
  },
  {
    title: '完成时间',
    field: 'endTime',
    showOverflow: 'title',
    width: 150,
  },
  {
    title: '操作',
    align: 'center',
    slots: { default: 'action' },
  },
];
export const task: VxeGridPropTypes.Columns = [
  {
    title: '序号',
    type: 'seq',
    fixed: 'left',
    width: '50',
    align: 'center',
  },
  {
    title: '流程名称',
    field: 'defName',
    width: 300,
    showOverflow: 'tooltip',
    fixed: 'left',
  },
  {
    title: '当前步骤',
    field: 'name',
    width: 150,
  },
  {
    title: '描述',
    field: 'desc',
    width: 750,
    showOverflow: 'tooltip',
  },

  /*{
    title: '发起人',
    field: 'assignee',
  },*/
  {
    title: '时间',
    field: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    align: 'center',
    slots: { default: 'action' },
    fixed: 'right',
  },
];
export const vxeTableFormSchema: VxeFormItemProps[] = [
  {
    field: 'id',
    title: 'ID',
    itemRender: {
      name: 'AInput',
    },
    span: 6,
  },
];
