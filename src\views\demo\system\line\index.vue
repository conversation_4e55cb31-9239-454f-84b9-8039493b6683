<template>
  <erupt-table class-name="Line" :extra-action="extraAction"></erupt-table>
</template>

<script lang="ts">
  import { defineComponent, onMounted } from 'vue';
  import eruptTable from '../../../../components/EruptTable/eruptTable';
  import { NColorPicker } from 'naive-ui';
  import { useUserStore } from '/@/store/modules/user';
  import { startProcess } from '/@/views/demo/system/assembly/api/assembly';
  import { useMessage } from '/@/hooks/web/useMessage';
  import Template from '/@/views/demo/system/quality/template.vue';
  import { updateAssemblyEndTime } from '/@/views/demo/system/line/api/line';
  export default defineComponent({
    name: 'Line',
    components: { Template, eruptTable, NColorPicker },
    setup() {
      const userStore = useUserStore();
      const { createMessage } = useMessage();

      return {
        extraAction: {
          nums: 1,
          actions: (row) => {
            return [
              {
                label: '同步汇编时间',
                popConfirm: {
                  title: '同步汇编时间',
                  confirm: () => {
                    updateAssemblyEndTime(row.row.id).then((res) => {
                      if (res) {
                        createMessage.success('同步汇编时间成功');
                      }
                    });
                  },
                },
              },
            ];
          },
        },
      };
    },
  });
</script>
