import {defHttp} from '/@/utils/http/axios';

enum Api {
  LINE_OPTIONS = '/newline/special/getLineSite',
  LINE_OPTIONS_CONTAIN = '/newline/special/getLineSite/contain',
}


export const lineOptionsApi = (id: string) => {
  return defHttp.post({url: Api.LINE_OPTIONS, params: {lineId: id}})
}

export const lineOptionsContainApi = (id: string) => {
  return defHttp.post({url: Api.LINE_OPTIONS_CONTAIN, params: {id}})
}
