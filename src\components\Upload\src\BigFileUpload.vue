<template>
  <div class="big-file-upload">
    <div
      class="upload-drag-area"
      :class="{ 'is-dragover': isDragover }"
      @drop.prevent="handleDrop"
      @dragover.prevent="isDragover = true"
      @dragleave.prevent="isDragover = false"
    >
      <Upload
        v-show="!currentFile"
        :show-upload-list="false"
        v-model:file-list="fileList"
        :before-upload="handleBeforeUpload"
        :custom-request="() => {}"
        :multiple="false"
        :max-count="1"
      >
        <div class="upload-drag-content">
          <InboxOutlined class="upload-icon" />
          <p class="upload-text">点击或拖拽文件到此处上传</p>
          <p class="upload-hint">支持上传任意大小的文件，大于20MB的文件将自动分片上传</p>
          <p class="upload-hint"
            >仅支持以下格式：jpg, jpeg, png, gif, bmp, pdf, doc, docx, xls, xlsx, ppt, pptx, zip,
            rar, tar, gz, txt, md</p
          >
        </div>
      </Upload>
      <div class="ant-upload-list ant-upload-list-text" v-for="file in fileList" :key="file.url"
        ><div class="ant-upload-list-item-container"
          ><div class="ant-upload-list-item ant-upload-list-item-done"
            ><div class="ant-upload-text-icon"
              ><span role="img" aria-label="paper-clip" class="anticon anticon-paper-clip">
                <svg
                  focusable="false"
                  data-icon="paper-clip"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                  aria-hidden="true"
                  viewBox="64 64 896 896"
                >
                  <path
                    d="M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z"
                  ></path></svg
                ><!----></span
              >
              <a
                target="_blank"
                rel="noopener noreferrer"
                class="ant-upload-list-item-name"
                :title="file.name"
                style="display: inline"
                >{{ file.name }}</a
              ></div
            ><span class="ant-upload-list-item-actions"><!----> </span>
          </div></div
        ></div
      >
      <div v-show="currentFile" class="selected-file">
        <FileOutlined class="file-icon" />
        <p class="file-size">{{ formatFileSize(currentFile?.size) }}</p>
        <Button class="cancel-select" type="link" danger @click="cancelSelect">
          <CloseOutlined />取消选择
        </Button>
      </div>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploadStatus.isUploading" class="upload-progress">
      <Progress
        :percent="uploadStatus.progress"
        :status="uploadStatus.status"
        :format="(percent) => `${percent}% ${uploadStatus.speed}`"
      />
    </div>

    <!-- 操作按钮 -->
    <div class="upload-actions" v-if="currentFile">
      <Space>
        <Button type="primary" :loading="uploadStatus.isUploading" @click="startUpload">
          {{ uploadStatus.isUploading ? '上传中' : '开始上传' }}
        </Button>
        <Button v-if="uploadStatus.isUploading" @click="cancelUpload"> 取消上传 </Button>
      </Space>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, reactive, watch } from 'vue';
  import { Upload, Button, Progress, Space, message } from 'ant-design-vue';
  import { InboxOutlined, FileOutlined, CloseOutlined } from '@ant-design/icons-vue';
  import SparkMD5 from 'spark-md5';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { defHttp } from '/@/utils/http/axios';

  interface UploadFile {
    url: string;
    name: string;
    uid: string;
    md5: string;
  }

  export default defineComponent({
    name: 'BigFileUpload',
    emits: ['change', 'update:value'],
    props: {
      value: { type: String },
      beforeUploadUrl: {
        type: String,
        default: '/system/upload/before',
      },
      uploadChunkUrl: {
        type: String,
        default: '/system/upload/chunk',
      },
      uploadProgressUrl: {
        type: String,
        default: '/system/upload/progress',
      },
      uploadMergeUrl: {
        type: String,
        default: '/system/upload/merge',
      },
    },
    components: {
      Upload,
      Button,
      Progress,
      Space,
      InboxOutlined,
      FileOutlined,
      CloseOutlined,
    },
    setup(props, { attrs, emit }) {
      const CHUNK_SIZE = 20 * 1024 * 1024; // 20MB 分片大小
      const fileList = ref<UploadFile[]>([]);
      const isDragover = ref(false);
      const currentFile = ref<File | null>(null);
      const { createMessage } = useMessage();

      const uploadStatus = reactive({
        isUploading: false,
        progress: 0,
        status: 'normal' as 'success' | 'normal' | 'active' | 'exception',
        speed: '',
      });
      watch(
        () => props.value,
        (v) => {
          if (v) {
            let files: UploadFile[] = [];
            files.push({
              url: v,
              name: v.substring(v.lastIndexOf('/') + 1),
              uid: Date.now().toString(),
              md5: '',
            });
            fileList.value = files;
          } else {
            fileList.value = [];
          }
        },
      );
      // 文件大小检查
      const handleBeforeUpload = (file: File) => {
        if (currentFile.value) {
          createMessage.warning('请先完成当前文件的上传');
          return false;
        }

        // 检查文件类型
        const allowedTypes = [
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/bmp',
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'application/zip',
          'application/x-zip-compressed',
          'application/x-rar-compressed',
          'application/x-tar',
          'application/gzip',
          'text/plain',
          'text/markdown',
        ];

        // 检查文件扩展名
        const fileExt = file.name.toLowerCase().split('.').pop();
        if ((fileExt === 'zip' || fileExt === 'rar') && !allowedTypes.includes(file.type)) {
          // 如果是 zip 文件但 MIME 类型不匹配，仍然允许上传
          currentFile.value = file;
          return false;
        }

        if (!allowedTypes.includes(file.type)) {
          createMessage.error('不支持的文件格式，请上传支持的文件类型');
          return false;
        }

        currentFile.value = file;
        return false;
      };

      // 计算文件MD5
      const calculateFileMd5 = async (file: File | Blob): Promise<string> => {
        const spark = new SparkMD5.ArrayBuffer();
        const chunks = Math.ceil(file.size / CHUNK_SIZE);

        for (let i = 0; i < chunks; i++) {
          const chunk = file.slice(i * CHUNK_SIZE, (i + 1) * CHUNK_SIZE);
          const buffer = await chunk.arrayBuffer();
          spark.append(buffer);
        }

        return spark.end();
      };

      // 开始上传
      const startUpload = async () => {
        if (!currentFile.value) return;

        uploadStatus.isUploading = true;
        uploadStatus.progress = 0;
        uploadStatus.status = 'active';

        try {
          // 1. 计算文件MD5
          const fileMd5 = await calculateFileMd5(currentFile.value);

          // 2. 初始化上传任务
          const initResult = await defHttp.post(
            {
              url: props.beforeUploadUrl,
              params: {
                fileMd5,
                fileName: currentFile.value.name,
                fileSize: currentFile.value.size,
              },
            },
            { errorMessageMode: 'message' },
          );

          console.log('initResult 完整内容:', JSON.stringify(initResult, null, 2));

          // 判断任务是否已完成
          if (initResult && initResult.status === 1) {
            uploadStatus.progress = 100;
            uploadStatus.status = 'success';
            uploadStatus.speed = '';
            emit('update:value', initResult.fileName);
            emit('change', initResult.fileName);
            createMessage.success('文件已存在，上传成功');
            // 重置控件状态，但保留文件名
            fileList.value = [
              {
                url: initResult.fileName,
                name: currentFile.value.name,
                uid: Date.now().toString(),
                md5: fileMd5,
              },
            ];
            currentFile.value = null;
            uploadStatus.isUploading = false;
            return;
          }

          // 3. 分片上传
          const chunks = Math.ceil(currentFile.value.size / CHUNK_SIZE);
          let uploadedChunks = 0;
          const startTime = Date.now();

          // 创建上传进度更新函数
          const updateProgress = (completed: number) => {
            uploadedChunks = completed;
            uploadStatus.progress = Math.floor((uploadedChunks / chunks) * 100);

            // 计算上传速度
            const currentTime = Date.now();
            const timeElapsed = (currentTime - startTime) / 1000; // 转换为秒
            const uploadedBytes = uploadedChunks * CHUNK_SIZE;
            const bytesPerSecond = uploadedBytes / timeElapsed;

            // 格式化速度显示
            if (bytesPerSecond > 1024 * 1024) {
              uploadStatus.speed = `${(bytesPerSecond / (1024 * 1024)).toFixed(2)} MB/s`;
            } else if (bytesPerSecond > 1024) {
              uploadStatus.speed = `${(bytesPerSecond / 1024).toFixed(2)} KB/s`;
            } else {
              uploadStatus.speed = `${Math.floor(bytesPerSecond)} B/s`;
            }
          };

          // 串行上传分片，以便准确跟踪进度
          for (let i = 0; i < chunks; i++) {
            const chunk = currentFile.value.slice(i * CHUNK_SIZE, (i + 1) * CHUNK_SIZE);
            const chunkMd5 = await calculateFileMd5(chunk);

            const formData = new FormData();
            formData.append('chunk', new Blob([chunk]), 'chunk');
            formData.append('chunkIndex', i.toString());
            formData.append('fileMd5', fileMd5);
            formData.append('chunkMd5', chunkMd5);

            await defHttp.post(
              {
                url: props.uploadChunkUrl,
                params: formData,
              },
              {
                errorMessageMode: 'message',
              },
            );

            // 更新进度
            updateProgress(i + 1);
          }

          // 6. 请求合并分片
          const res = await defHttp.post(
            {
              url: props.uploadMergeUrl,
              params: { fileMd5 },
            },
            { errorMessageMode: 'message' },
          );

          uploadStatus.progress = 100;
          uploadStatus.status = 'success';
          uploadStatus.speed = '';
          emit('update:value', res);
          emit('change', res);
          createMessage.success('上传成功');
          // 重置控件状态，但保留文件名
          fileList.value = [
            {
              url: res,
              name: currentFile.value.name,
              uid: Date.now().toString(),
              md5: fileMd5,
            },
          ];
          currentFile.value = null;
          uploadStatus.isUploading = false;
        } catch (error: any) {
          uploadStatus.status = 'exception';
          createMessage.error('上传失败：' + error.message);
          // 上传失败时也重置状态
          currentFile.value = null;
          fileList.value = [];
        } finally {
          uploadStatus.isUploading = false;
        }
      };

      // 取消上传
      const cancelUpload = () => {
        uploadStatus.isUploading = false;
        uploadStatus.progress = 0;
        uploadStatus.status = 'normal';
        currentFile.value = null;
        emit('update:value', null);
        emit('change', null);
      };

      // 处理拖拽
      const handleDrop = (e: DragEvent) => {
        isDragover.value = false;
        const files = e.dataTransfer?.files;
        if (files && files.length > 0) {
          if (currentFile.value) {
            createMessage.warning('请先完成当前文件的上传');
            return;
          }
          handleBeforeUpload(files[0]);
        }
      };

      // 取消选择文件
      const cancelSelect = () => {
        currentFile.value = null;
        fileList.value = [];
        emit('update:value', null);
        emit('change', null);
      };

      // 格式化文件大小
      const formatFileSize = (bytes?: number): string => {
        if (!bytes) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
      };

      return {
        fileList,
        isDragover,
        currentFile,
        uploadStatus,
        handleBeforeUpload,
        handleDrop,
        startUpload,
        cancelUpload,
        cancelSelect,
        formatFileSize,
      };
    },
  });
</script>

<style lang="less" scoped>
  .big-file-upload {
    width: 100%;

    .upload-drag-area {
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      padding: 20px;
      text-align: center;
      background: #fafafa;
      transition: border-color 0.3s;
      cursor: pointer;

      &.is-dragover {
        border-color: #1890ff;
        background: #e6f7ff;
      }
    }

    .upload-drag-content {
      padding: 20px;

      .upload-icon {
        font-size: 48px;
        color: #40a9ff;
      }

      .upload-text {
        margin: 10px 0;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
      }

      .upload-hint {
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
      }
    }

    .selected-file {
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .file-icon {
        font-size: 32px;
        color: #40a9ff;
      }

      .file-name {
        margin: 10px 0 5px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }

      .file-size {
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
        margin: 5px 0 15px;
      }

      .cancel-select {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 12px;
        height: auto;
        font-size: 14px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          background-color: #fff1f0;
        }

        .anticon {
          font-size: 14px;
        }
      }
    }

    .upload-progress {
      margin: 20px 0;
    }

    .upload-actions {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
</style>
