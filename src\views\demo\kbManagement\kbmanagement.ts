import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetDirectoryCount = '/kbManagement/getDirectoryCount',
  GetFileCount = '/kbManagement/getFileCount',
  GetFileDownload = '/kbManagement/getFileDownload',
  GetQuestionCount = '/kbManagement/getQuestionCount',
  SearchSolrQuery = '/solr/query',
}

export const getDirectoryCount = () => {
  return defHttp.get<number>({
    url: Api.GetDirectoryCount,
  });
};

export const getFileCount = () => {
  return defHttp.get<number>({
    url: Api.GetFileCount,
  });
};

export const getFileDownload = () => {
  return defHttp.get<number>({
    url: Api.GetFileDownload,
  });
};

export const getQuestionCount = () => {
  return defHttp.get<number>({
    url: Api.GetQuestionCount,
  });
};

export const searchSolrQuery = (params: { search: string; module: string[] }) => {
  return defHttp.post<[]>({
    url: Api.SearchSolrQuery,
    data: params,
  });
};
