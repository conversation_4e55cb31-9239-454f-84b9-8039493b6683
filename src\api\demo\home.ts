import { defHttp } from '/@/utils/http/axios';
enum Api {
  ASSEMBLY = '/home/<USER>',
  ASSEMBLY_BY_LINE = '/home/<USER>',
  ASSEMBLY_BY_DEPT = '/home/<USER>',
  ASSEMBLY_BY_DEPT_AND_LINE = '/home/<USER>',
}

export const getAssembly = (data) => {
  return defHttp.post({ url: Api.ASSEMBLY, data: data });
};
export const getAssemblyByLine = (data) => {
  return defHttp.post({ url: Api.ASSEMBLY_BY_LINE, data: data });
};
export const getAssemblyByDept = (data) => {
  return defHttp.post({ url: Api.ASSEMBLY_BY_DEPT, data: data });
};

export const getAssemblyByDeptAndLine = (data) => {
  return defHttp.post({ url: Api.ASSEMBLY_BY_DEPT_AND_LINE, data: data });
};
