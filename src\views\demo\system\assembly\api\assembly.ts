import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';
import { UploadApiResult } from '/@/api/sys/model/uploadModel';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  GetItems = '/newline/assembly/items/',
  GetOptions = '/newline/line/list',
  Issued = '/newline/assembly/issued',
  Upload = '/newline/assembly/upload/',
  Persist = '/newline/assembly/save/temp/',
  ItemRemove = '/newline/assembly/item/remove',
  ItemEdit = '/newline/assembly/item/edit',
  GetLineFilter = '/newline/line/unissued',
  SaveJson = '/newline/assembly/add/json',
  StartProcess = '/newline/assembly/startProcess',
  GetDepartmentList = '/newline/assembly/getDepartmentList',
}

const globSetting = useGlobSetting();
export const getItemsApi = async (id) => {
  return await defHttp.get({ url: Api.GetItems + id });
};

export const addJson = async (username) => {
  return await defHttp.get({ url: Api.SaveJson + `/${username}` });
};

export const getOptionsApi = async () => {
  return await defHttp.get({ url: Api.GetOptions });
};

export const getDepartmentList = async () => {
  return await defHttp.get({ url: Api.GetDepartmentList });
};

export const GetLineFilter = async () => {
  return await defHttp.get({ url: Api.GetLineFilter });
};

export const issuedApi = async (id, params) => {
  return await defHttp.post({ url: Api.Issued + `/${id}`, params });
};

export const persistApi = async (key) => {
  return await defHttp.post({ url: Api.Persist + `${key}` });
};

export const itemRemove = async (id) => {
  return await defHttp.post({ url: Api.ItemRemove + `/${id}` });
};

export const startProcess = async (id) => {
  return await defHttp.post({ url: Api.StartProcess + `/${id}` });
};
export const itemEdit = async (params) => {
  return await defHttp.post({ url: Api.ItemEdit, params });
};

export const itemSave = async (params) => {
  return await defHttp.post({ url: Api.ItemEdit, params });
};
export const getUpLoadApi = (tplid) => {
  return (params: UploadFileParams, onUploadProgress: (progressEvent: ProgressEvent) => void) => {
    return defHttp.uploadFile<UploadApiResult>(
      {
        url: `${globSetting.apiUrl}${Api.Upload}${tplid}`,
        onUploadProgress,
      },
      params,
    );
  };
};
