<template>
  <div style="padding: 16px 24px; background: white">
    <vxe-toolbar ref="toolbarRef">
      <template #buttons>
        <vxe-button type="primary" @click="goBack">返回</vxe-button>
        <vxe-button type="primary" status="primary" @click="add">新增</vxe-button>
      </template>
    </vxe-toolbar>
    <vxe-grid ref="gridRef" v-bind="gridOptions">
      <!-- 添加表格引用 -->
      <template #active="{ row }">
        <vxe-button type="text" status="primary" @click="edit(row)">编辑</vxe-button>
        <a-popconfirm title="是否确认删除" placement="left" @confirm="remove(row)">
          <vxe-button type="text" status="danger">删除</vxe-button>
        </a-popconfirm>
      </template>
    </vxe-grid>
    <BasicDrawer
      width="600px"
      @register="registerDrawer"
      :showFooter="true"
      :title="!isEdit ? '新增' : '编辑'"
      @ok="handleSubmit"
    >
      <BasicForm style="height: 300px" @register="registerForm"></BasicForm>
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    getDeptOptionsApi,
    getItemsApi,
    itemAdd,
    itemEdit,
    itemRemove,
  } from '/@/views/demo/system/operation/api/operation';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDrawer } from '/@/components/Drawer';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { useForm } from '/@/components/Form';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { useWatermark } from '/@/hooks/web/useWatermark';

  const route = useRoute();
  const router = useRouter();
  const { createMessage } = useMessage();
  const userStore = useUserStore();
  const { setWatermark } = useWatermark();

  // 表格引用和展开状态控制
  const gridRef = ref(); // 新增：表格实例引用
  let expandedKeys = ref<string[]>([]); // 保存展开的节点ID

  // 表单和抽屉控制
  let isEdit = ref(false);
  let isEdit3 = ref(false);
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawer();
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: [
      { field: 'id', label: 'id', show: false, component: 'Input', colProps: { span: 24 } },
      {
        field: 'chapter',
        label: '编号',
        required: true,
        colProps: { span: 24 },
        component: 'Input',
        componentProps: { placeholder: '请输入编号', disabled: isEdit },
        rules: [
          { required: true, message: '编号不能为空!' },
          { pattern: /^[0-9.]+$/, message: '编号只能包含数字和点!' },
        ],
      },
      { field: 'provisions', label: '条文', component: 'Input', colProps: { span: 24 } },
      {
        field: 'endTime',
        label: '截止时间',
        component: 'DatePicker',
        colProps: { span: 24 },
        componentProps: { valueFormat: 'YYYY-MM-DD', disabled: isEdit3 },
      },
      {
        field: 'responsibleUnit',
        label: '责任单位',
        component: 'ApiSelect',
        colProps: { span: 24 },
        componentProps: {
          api: getDeptOptionsApi,
          resultField: 'list',
          labelField: 'label',
          valueField: 'value',
          placeholder: '请选择责任单位',
          disabled: isEdit3,
        },
      },
    ],
    showActionButtonGroup: false,
  });

  // 数据初始化
  const id = route.params?.id ?? -1;
  const tableData = ref([]);
  const gridOptions = ref({
    border: true,
    treeConfig: { rowField: 'id', childrenField: 'children' },
    columns: [
      { field: 'chapter', title: '编号', treeNode: true },
      { field: 'provisions', title: '条文' },
      { field: 'endTime', title: '截止时间' },
      { field: 'responsibleUnit', title: '责任单位' },
      { title: '操作', slots: { default: 'active' } },
    ],
    data: tableData,
  });

  // 生命周期
  onMounted(() => {
    setWatermark(userStore.getUserInfo.username);
    getList();
  });

  // 核心功能方法
  const saveExpandedState = () => {
    const expandedRows = gridRef.value?.getTreeExpandRecords() || [];
    expandedKeys.value = expandedRows.map((row) => row.id);
  };

  const restoreExpandedState = () => {
    nextTick(() => {
      expandedKeys.value.forEach((key) => {
        const node = findNodeById(tableData.value, key);
        if (node) gridRef.value?.setTreeExpand(node, true);
      });
    });
  };

  const findNodeById = (data: any[], id: string): any => {
    for (const item of data) {
      if (item.id === id) return item;
      if (item.children) {
        const found = findNodeById(item.children, id);
        if (found) return found;
      }
    }
  };

  async function getList() {
    saveExpandedState();
    tableData.value = await getItemsApi(id);
    restoreExpandedState();
  }

  const handleSubmit = async () => {
    try {
      const values = await validate();
      saveExpandedState();
      const res = isEdit.value ? await itemEdit(values) : await itemAdd(id, values);
      createMessage[res ? 'success' : 'error'](res ? '保存成功' : '保存失败');
      await getList();
    } catch (error) {
      createMessage.error('保存失败');
    } finally {
      closeDrawer();
    }
  };

  // 其他操作
  const add = () => {
    isEdit.value = false;
    isEdit3.value = false;
    resetFields();
    openDrawer();
  };

  const edit = (row) => {
    isEdit.value = true;
    isEdit3.value = row.level != 3;
    openDrawer();
    nextTick(() => setFieldsValue(row));
  };

  const remove = (row) => {
    itemRemove(row.id).then((res) => {
      createMessage[res ? 'success' : 'error'](res ? '删除成功' : '删除失败');
      getList();
    });
  };

  const goBack = () => router.push('/newLine/operation/operationTpl');
</script>
