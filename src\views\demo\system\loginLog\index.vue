<template>
  <erupt-table class-name="LoginLog" :overwrite-action="overwriteAction"></erupt-table>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import eruptTable from '/@/components/EruptTable/eruptTable';

  export default defineComponent({
    name: 'LoginLog',
    components: { eruptTable },
    setup() {
      const overwriteAction = {
        detail: async (row) => {
          return false;
        }
      };
   
      return {
        overwriteAction,
      };
    },
  });
</script>
