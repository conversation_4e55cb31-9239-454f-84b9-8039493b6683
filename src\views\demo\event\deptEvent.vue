<script setup lang="ts">
  import { Card, CardMeta, Carousel } from 'ant-design-vue';
  import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons-vue';
  import logo from '/@/assets/images/deptIntro.png';
  import bg from '/@/assets/images/deptEventBg.png';
  import bg2 from '/@/assets/images/deptEventBg2.png';
  import deptPersonPng from '/@/assets/images/deptPerson.png';
  import { Icon } from '/@/components/Icon';
  import TimeLine from './components/TimeLine.vue';
  import { onMounted, ref, reactive } from 'vue';
  import {
    getCarouselPicApi,
    getDeptEventApi,
    getDeptGoodPersonApi,
    getDeptPersonApi,
    getDeptPictureApi,
    getDeptTargetApi,
  } from '/@/views/demo/event/api';

  const baseUrl = import.meta.env.VITE_GLOB_ERUPT_ATTACHMENT;
  const deptPerson = reactive({
    deptPersonNum: 0,
    partyMemberNum: 0,
    thisYearAddNum: 0,
  });
  const deptEventList = ref([
    { date: '', title: '', desc: '' },
    { date: '', title: '', desc: '' },
    { date: '', title: '', desc: '' },
    { date: '', title: '', desc: '' },
    { date: '', title: '', desc: '' },
  ]);
  const nextyear = ref(new Date().getFullYear());
  const deptTarget = ref([]);
  const deptGoodPerson = ref([]);
  const deptPicture = ref([]);
  const carouselPics = ref([]);

  async function getDeptPerson() {
    const res = await getDeptPersonApi();
    deptPerson.deptPersonNum = res.deptPersonNum;
    deptPerson.partyMemberNum = res.partyMemberNum;
    deptPerson.thisYearAddNum = res.thisYearAddNum;
  }

  async function getDeptEventList() {
    const result = await getDeptEventApi();
    deptEventList.value = result && result.length > 0 ? result : deptEventList.value;
  }

  async function getDeptTarget() {
    deptTarget.value = await getDeptTargetApi(nextyear.value);
  }

  async function getDeptGoodPerson() {
    deptGoodPerson.value = await getDeptGoodPersonApi();
  }

  async function getDeptPicture() {
    deptPicture.value = await getDeptPictureApi();
  }

  async function getCarouselPic() {
    carouselPics.value = await getCarouselPicApi();
  }

  onMounted(() => {
    getDeptPerson();
    getDeptEventList();
    getDeptTarget();
    getDeptGoodPerson();
    getDeptPicture();
    getCarouselPic();
  });
</script>

<template>
  <div>
    <a-row :gutter="8" style="width: calc(100% - 13px); height: 240px; margin: 0; line-height: 0">
      <a-col :span="16" style="padding: 0px">
        <h1
          style="
            position: absolute;
            color: #000000;
            font-size: 18px;
            top: 20px;
            left: 20px;
            z-index: 2;
            font-weight: bold;
          "
        >
          运营先锋
        </h1>
        <a-space size="small" direction="vertical" style="width: 100%">
          <Carousel arrows autoplay style="height: 100%">
            <template #prevArrow>
              <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
                <LeftCircleOutlined />
              </div>
            </template>
            <template #nextArrow>
              <div class="custom-slick-arrow" style="right: 10px">
                <RightCircleOutlined />
              </div>
            </template>
            <div
              v-for="carousel in carouselPics"
              :key="carousel.id"
              style="width: 100%; height: 225px; padding: 0; border: none"
            >
              <a-image
                :preview="false"
                :src="baseUrl + carousel.enclosure"
                style="height: 225px; width: 100%; object-fit: cover"
              />
            </div>
          </Carousel>
        </a-space>
      </a-col>

      <a-col :span="8" style="padding: 0px 0px 0px 10px; height: 240px">
        <Card
          title="新线部门简介"
          :body-style="{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-around',
            padding: '15px',
            height: '169px',
          }"
          style="border-radius: 4px; background: white; margin-top: 3px; width: 562px"
        >
          <a-statistic
            title="部门人数"
            suffix="人"
            :value="deptPerson.deptPersonNum"
            class="stat-item"
          />
          <a-statistic
            title="党员人数"
            suffix="人"
            :value="deptPerson.partyMemberNum"
            class="stat-item"
          />
          <a-statistic
            title="本年新增"
            suffix="人"
            :value="deptPerson.thisYearAddNum"
            class="stat-item"
          />
        </Card>
      </a-col>
    </a-row>

    <!-- 其他内容 -->
    <a-row :gutter="8" style="width: 100%; margin-top: 0px">
      <a-col :span="16">
        <Card
          title="发展历程"
          style="min-height: 450px"
          :style="{
            backgroundImage: `url(${bg2})`,
            backgroundSize: '100% auto',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'bottom',
          }"
        >
          <TimeLine
            color="#3B7EF0"
            sub-color="#CCDEFF"
            :date="deptEventList[4].date"
            :title="deptEventList[4].title"
            :desc="deptEventList[4].desc"
            style="position: absolute; left: 66%; bottom: 60%"
          />
          <TimeLine
            color="#009651"
            sub-color="#D8F7E9"
            :date="deptEventList[3].date"
            :title="deptEventList[3].title"
            :desc="deptEventList[3].desc"
            reverse="true"
            style="position: absolute; right: 47%; bottom: 49%"
          />
          <TimeLine
            color="#DB9046"
            sub-color="#FFE4C8"
            :date="deptEventList[2].date"
            :title="deptEventList[2].title"
            :desc="deptEventList[2].desc"
            style="position: absolute; left: 69%; bottom: 33%"
          />
          <TimeLine
            color="#F0582E"
            sub-color="#FBD8CF"
            :date="deptEventList[1].date"
            :title="deptEventList[1].title"
            :desc="deptEventList[1].desc"
            reverse="true"
            style="position: absolute; right: 35%; bottom: 10%"
          />
          <TimeLine
            color="#6D6ABA"
            sub-color="#D9D7FF"
            :date="deptEventList[0].date"
            :title="deptEventList[0].title"
            :desc="deptEventList[0].desc"
            reverse="true"
            style="position: absolute; right: 67%; bottom: 10%"
          />
        </Card>
        <Card title="部门荣誉" :body-style="{ padding: '10px 20px' }" style="margin-top: 8px">
          <a-row :gutter="[8, 8]">
            <a-col :span="8" v-for="(item, index) in deptPicture" :key="index">
              <Card hoverable style="width: 100%">
                <template #cover>
                  <img
                    style="height: 150px; width: 100%; object-fit: cover"
                    :src="baseUrl + item.enclosure"
                  />
                </template>
                <CardMeta :title="item.name">
                  <template #description>
                    <div class="description-wrapper">
                      <div style="height: 45px" class="text-content">{{ item.description }}</div>
                      <div class="date-text">{{ item.eventDate }}</div>
                    </div>
                  </template>
                </CardMeta>
              </Card>
            </a-col>
          </a-row>
        </Card>
      </a-col>

      <a-col :span="8">
        <Card
          :style="{ width: '562px' }"
          :title="nextyear + '年部门目标'"
          :body-style="{ padding: '10px 20px', height: '250px' }"
        >
          <a-list>
            <a-list-item v-for="(item, index) in deptTarget.deptTargetItems" :key="item">
              <template v-if="index === 0">
                <Icon icon="fluent-emoji-flat:fire" :size="25" />
              </template>
              <template v-else>
                <span style="margin-left: 30px" />
              </template>
              {{ index + 1 }}、{{ item.targetDesc }}
            </a-list-item>
          </a-list>
        </Card>
        <Card
          :style="{ width: '562px' }"
          title="部门组织架构"
          style="top: 10px"
          :body-style="{ padding: '10px 25px', height: '450px' }"
        >
          <a-list item-layout="horizontal">
            <a-list-item>
              <a-list-item-meta>
                <template #avatar>
                  <a-image :preview="false" width="500px" height="393px" :src="deptPersonPng" />
                </template>
              </a-list-item-meta>
            </a-list-item>
          </a-list>
        </Card>
        <!--        <Card
          :style="{ width: '562px' }"
          title="部门组织架构"
          style="top: 10px"
          :body-style="{ padding: '10px 25px', height: '450px' }"
        >
          <div item-layout="horizontal">
            &lt;!&ndash; 领导 &ndash;&gt;
            <Carousel arrows autoplay style="height: auto">
              <template #prevArrow>
                <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
                  <LeftCircleOutlined />
                </div>
              </template>
              <template #nextArrow>
                <div class="custom-slick-arrow" style="right: 10px">
                  <RightCircleOutlined />
                </div>
              </template>
              <div
                v-for="(item, index) in deptGoodPerson.ld"
                :key="index"
                style="width: 100%; padding: 0; border: none; text-align: center"
              >
                <div style="display: inline-block">
                  <div>{{ item.job }}:{{ item.name }}</div>
                </div>
                <a-image
                  :src="baseUrl + item.enclosure"
                  :preview="false"
                  :style="{ width: '100%', maxHeight: '215px', objectFit: 'contain' }"
                />
              </div>
            </Carousel>

            &lt;!&ndash; 员工 &ndash;&gt;
            <Carousel arrows autoplay style="height: auto">
              <template #prevArrow>
                <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
                  <LeftCircleOutlined />
                </div>
              </template>
              <template #nextArrow>
                <div class="custom-slick-arrow" style="right: 10px">
                  <RightCircleOutlined />
                </div>
              </template>
              <div
                v-for="(item, index) in deptGoodPerson.yg"
                :key="index"
                style="
                  width: 100%;
                  padding: 0;
                  border: none;
                  text-align: center;
                  display: flex;
                  flex-direction: column;
                "
              >
                <div style="display: inline-block">
                  <div style="margin-bottom: 10px">{{ item.job }}:{{ item.name }}</div>
                </div>
                <a-image
                  :src="baseUrl + item.enclosure"
                  :preview="false"
                  :style="{ width: '100%', maxHeight: '215px', objectFit: 'contain' }"
                />
              </div>
            </Carousel>
          </div>
        </Card>-->
      </a-col>
    </a-row>
  </div>
</template>
<style scoped lang="less">
  .description-wrapper {
    .text-content {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.5;
      max-height: 3em; /* 2行 x 1.5行高 */
      transition: all 0.3s;
    }

    .date-text {
      color: #666;
      font-size: 12px;
      margin-top: 6px;
    }

    &:hover .text-content {
      -webkit-line-clamp: unset;
      max-height: none;
      overflow: visible;
      background: white;
      position: relative;
      z-index: 1;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      padding: 8px;
      border-radius: 4px;
    }
  }

  :deep(.slick-slide) {
    text-align: center;
    height: 220px;
    line-height: 160px;
    background: #ffffff;
    overflow: hidden;
  }

  :deep(.slick-arrow.custom-slick-arrow) {
    width: 25px;
    height: 25px;
    font-size: 25px;
    color: #fff;
    background-color: rgba(31, 45, 61, 0.11);
    transition: ease all 0.3s;
    opacity: 0.3;
    z-index: 1;
  }

  :deep(.slick-arrow.custom-slick-arrow:before) {
    display: none;
  }

  :deep(.slick-arrow.custom-slick-arrow:hover) {
    color: #fff;
    opacity: 0.5;
  }

  :deep(.slick-slide h3) {
    color: #fff;
  }

  .stat-item {
    width: 33%;
    text-align: center;
    color: #333;
    font-size: 14px;
    border: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-left: 15px;
    padding: 10px;
    border-radius: 4px;

    ::v-deep {
      .ant-statistic-title {
        font-weight: 700;
        color: #333;
        font-size: 20px; // 保持与原有样式一致
      }

      .ant-statistic-content {
        color: #4d9aed;
        font-size: 40px;
        font-weight: 700;
        // ...其他已有样式
      }

      .ant-statistic-content-suffix {
        font-size: 20px;
      }
    }

    .ant-statistic-content-value {
      font-size: 24px;
      color: #333;
      margin-bottom: 8px;
    }

    .ant-statistic-content-suffix {
      font-size: 14px;
      color: #999;
    }
  }
</style>

<!--
<script setup lang="ts">
  import { Card } from 'ant-design-vue';
  import logo from '/@/assets/images/deptIntro.png';
  import bg from '/@/assets/images/deptEventBg.png';
  import bg2 from '/@/assets/images/deptEventBg2.png';
  import { Icon } from '/@/components/Icon';
  import TimeLine from './components/TimeLine.vue';
  import { onMounted, ref, reactive } from 'vue';
  import {
    getDeptEventApi,
    getDeptGoodPersonApi,
    getDeptPersonApi,
    getDeptPictureApi,
    getDeptTargetApi,
  } from '/@/views/demo/event/api';
  const baseUrl = import.meta.env.VITE_GLOB_ERUPT_ATTACHMENT;
  const deptPerson = reactive({
    deptPersonNum: 0,
    partyMemberNum: 0,
    thisYearAddNum: 0,
  });
  const deptEventList = ref([
    { date: '', title: '', desc: '' },
    { date: '', title: '', desc: '' },
    { date: '', title: '', desc: '' },
    { date: '', title: '', desc: '' },
    { date: '', title: '', desc: '' },
  ]);
  const nextyear = ref(new Date().getFullYear() + 1);
  const deptTarget = ref([]);
  const deptGoodPerson = ref([]);
  const deptPicture = ref([]);
  async function getDeptPerson() {
    const res = await getDeptPersonApi();
    deptPerson.deptPersonNum = res.deptPersonNum;
    deptPerson.partyMemberNum = res.partyMemberNum;
    deptPerson.thisYearAddNum = res.thisYearAddNum;
  }
  async function getDeptEventList() {
    const result = await getDeptEventApi();
    deptEventList.value = result && result.length > 0 ? result : deptEventList.value;
  }
  async function getDeptTarget() {
    deptTarget.value = await getDeptTargetApi(nextyear.value);
  }
  async function getDeptGoodPerson() {
    deptGoodPerson.value = await getDeptGoodPersonApi();
  }
  async function getDeptPicture() {
    deptPicture.value = await getDeptPictureApi();
  }
  onMounted(() => {
    getDeptPerson();
    getDeptEventList();
    getDeptTarget();
    getDeptGoodPerson();
    getDeptPicture();
  });
</script>
<template>
  <div class="page flex-col">
    <div class="box_3 flex-row">
      <div class="group_3 flex-col">
        <div class="box_4 flex-col">
          <div class="text-wrapper_1 flex-row">
            <span class="text_2">运营先锋</span>
          </div>
          <div class="box_5 flex-row justify-between">
            <div class="image-wrapper_1 flex-col">
              <img
                class="thumbnail_1"
                referrerpolicy="no-referrer"
                src="https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNGecdd763ca519982e810481375f7f2c50.png"
              />
            </div>
            <img
              class="thumbnail_2"
              referrerpolicy="no-referrer"
              src="https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNG2dee6f160c928e1269ff4500de5b9e1a.png"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="box_6 flex-row justify-between">
      <div class="block_1 flex-col justify-between">
        <div class="block_2 flex-col">
          <div class="box_7 flex-row">
            <div class="group_4 flex-col"></div>
            <span class="text_3">发展历程</span>
            <TimeLine
                color="#3B7EF0"
                sub-color="#CCDEFF"
                :date="deptEventList[4].date"
                :title="deptEventList[4].title"
                :desc="deptEventList[4].desc"
            />
            <TimeLine
                color="#009651"
                sub-color="#D8F7E9"
                :date="deptEventList[3].date"
                :title="deptEventList[3].title"
                :desc="deptEventList[3].desc"
                reverse="true"
            />
            <TimeLine
                color="#DB9046"
                sub-color="#FFE4C8"
                :date="deptEventList[2].date"
                :title="deptEventList[2].title"
                :desc="deptEventList[2].desc"
            />
            <TimeLine
                color="#F0582E"
                sub-color="#FBD8CF"
                :date="deptEventList[1].date"
                :title="deptEventList[1].title"
                :desc="deptEventList[1].desc"
                reverse="true"
            />
            <TimeLine
                color="#6D6ABA"
                sub-color="#D9D7FF"
                :date="deptEventList[0].date"
                :title="deptEventList[0].title"
                :desc="deptEventList[0].desc"
                reverse="true"
            />
          </div>

          &lt;!&ndash;            <div class="box_8 flex-row justify-between">
              <div class="text-wrapper_3 flex-col justify-between">
                <span class="text_7">成立新线办公室</span>
                <span class="text_8"
                >总公司下发《宁地铁党人字【2010】63号“关于同意运营分公司组织架构及人员编制调整的批复”》，同意设立运营分公司新线管理办公室，编制为10人，其中主任1人、主任工程师1人、专业工程师7人、管理员1人。</span
                >
              </div>
              <span class="text_9"
              >确保一号线南延线、二号线系统总联调、接管工作顺利推进，确保三号线和一号线西延线运营从设计阶段的介入。</span
              >
            </div>
            <div class="box_9 flex-row">
              <div class="image-text_1 flex-row justify-between">
                <div class="text-group_1 flex-col">
                  <span class="text_10">2012.12.31</span>
                  <span class="text_11">成立新线管理部</span>
                  <span class="text_12"
                  >南京地下铁道有限责任公司运营分公司改制为南京地铁运营有限责任公司，相关部门随之进行调整，新线管理办公室更名为新线管理部，下设新线配合科、运营筹备科两个职能科室，定员15人。</span
                  >
                </div>
                <img
                    class="image_1"
                    referrerpolicy="no-referrer"
                    src="https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNG98de39614d677b9c841e3e813420fef8.png"
                />
              </div>
              <div class="image-text_2 flex-row justify-between">
                <div class="text-group_2 flex-col">
                  <span class="text_13">2012.12.31</span>
                  <span class="text_14">成立新线管理部</span>
                  <span class="text_15"
                  >南京地下铁道有限责任公司运营分公司改制为南京地铁运营有限责任公司，相关部门随之进行调整，新线管理办公室更名为新线管理部，下设新线配合科、运营筹备科两个职能科室，定员15人。</span
                  >
                </div>
                <img
                    class="image_2"
                    referrerpolicy="no-referrer"
                    src="https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNG19d6fb082395ae49ac715cee24125fb0.png"
                />
              </div>
              <div class="image-text_3 flex-row justify-between">
                <div class="group_6 flex-col"></div>
                <div class="text-group_3 flex-col">
                  <span class="text_16">2011.3.30</span>
                  <span class="text_17">调整分管领导</span>
                  <span class="text_18">分公司领导班子分工调整</span>
                </div>
              </div>
              <img
                  class="image_3"
                  referrerpolicy="no-referrer"
                  src="https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNG57222ebca635437cde44b64b39ff49a4.png"
              />
            </div>&ndash;&gt;
        </div>
        <div class="block_3 flex-col">
          <div class="box_10 flex-row justify-between">
            <div class="group_7 flex-col"></div>
            <span class="text_19">部门荣誉</span>
          </div>
          <div class="box_11 flex-row justify-between">
            <div class="box_12 flex-col">
              <div class="image-text_4 flex-col justify-between">
                <img
                  class="image_4"
                  referrerpolicy="no-referrer"
                  src="https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNG34f49ea094c2002d41f78c48b6e2e060.png"
                />
                <div class="text-group_4 flex-col">
                  <span class="text_20">新一代产品架构研讨会圆满结束</span>
                  <span class="text_21"
                    >团队成员深入探讨了新产品架构方案，为下一阶段开发奠定基础，深入探讨了新产品架构方案，为下一阶段开发奠定基础</span
                  >
                  <span class="text_22">2024-02-15</span>
                </div>
              </div>
            </div>
            <div class="box_13 flex-col">
              <div class="image-text_5 flex-col justify-between">
                <img
                  class="image_5"
                  referrerpolicy="no-referrer"
                  src="https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNG9ecb9f692569ba559b448d24b78cec05.png"
                />
                <div class="text-group_5 flex-col">
                  <span class="text_23">月度技术分享会：AI&nbsp;技术应用实践</span>
                  <span class="text_24"
                    >深入探讨&nbsp;AI&nbsp;技术在产品中的实际应用案例和最佳实践，深入探讨&nbsp;AI&nbsp;技术在产品中的实际应用案例和最佳实践</span
                  >
                  <span class="text_25">2024-02-15</span>
                </div>
              </div>
            </div>
            <div class="box_14 flex-col">
              <div class="image-text_6 flex-col justify-between">
                <img
                  class="image_6"
                  referrerpolicy="no-referrer"
                  src="https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNG069c40e31ecab35db5c119ff5665c7bb.png"
                />
                <div class="text-group_6 flex-col justify-between">
                  <span class="text_26">2024&nbsp;年第一季度团建活动</span>
                  <span class="text_27"
                    >通过户外拓展活动增进团队凝聚力，提升团队协作能力，增进团队凝聚力，提升团队协作能力</span
                  >
                  <span class="text_28">2024-02-15</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="block_4 flex-col justify-between">
        <div class="box_15 flex-col">
          <div class="group_8 flex-row justify-between">
            <div class="box_16 flex-col"></div>
            <span class="text_29">2025年部门目标</span>
          </div>
          <div class="group_9 flex-row">
            <div class="image-text_7 flex-row justify-between">
              <div class="box_17 flex-col"></div>
              <span class="text-group_7">1、南京地铁5号线交付；</span>
            </div>
          </div>
          <div class="text-wrapper_4 flex-row">
            <span class="text_30">2、南京地铁9号线工程建设管理；</span>
          </div>
          <div class="text-wrapper_5 flex-row">
            <span class="text_31">3、南京地铁7号线运营交接；</span>
          </div>
          <div class="text-wrapper_6 flex-row">
            <span class="text_32">4、南京地铁5号线交付；</span>
          </div>
          <div class="text-wrapper_7 flex-row">
            <span class="text_33">5、南京地铁9号线工程建设管理；</span>
          </div>
          <div class="text-wrapper_8 flex-row">
            <span class="text_34">6、南京地铁7号线运营交接；</span>
          </div>
        </div>
        <div class="box_18 flex-col">
          <div class="group_10 flex-row justify-between">
            <div class="box_19 flex-col"></div>
            <span class="text_35">部门组织架构</span>
          </div>
          <img
            class="image_7"
            referrerpolicy="no-referrer"
            src="https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNG650d7a3f79b7115109a742d0d0131626.png"
          />
        </div>
      </div>
    </div>
    <div class="box_20 flex-col"></div>
    <div class="box_21 flex-col">
      <div class="text-wrapper_9 flex-row">
        <span class="text_36">新线部门简介</span>
      </div>
      <div class="box_22 flex-row justify-between">
        <div class="group_11 flex-col">
          <div class="text-wrapper_10">
            <span class="text_37">59</span> <span class="text_38">人</span>
          </div>
          <span class="text_39">部门人数</span>
        </div>
        <div class="group_12 flex-col">
          <div class="text-wrapper_11">
            <span class="text_40">26</span> <span class="text_41">人</span>
          </div>
          <span class="text_42">党员人数</span>
        </div>
        <div class="group_13 flex-col">
          <div class="text-wrapper_12">
            <span class="text_43">2</span> <span class="text_44">人</span>
          </div>
          <span class="text_45">本年新增</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style src="./css/common.css" />
<style src="./css/index.css" />
-->
