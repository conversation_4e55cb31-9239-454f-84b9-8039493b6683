<template>
  <div style="padding: 16px 24px; background: white">
    <vxe-toolbar ref="toolbarRef">
      <template #buttons>
        <vxe-button type="primary" @click="goBack">返回</vxe-button>
        <vxe-button type="primary" status="primary" @click="add">新增</vxe-button>
      </template>
    </vxe-toolbar>
    <vxe-grid v-bind="gridOptions">
      <template #active="{ row }">
        <vxe-button type="text" status="primary" @click="edit(row)">编辑</vxe-button>
        <a-popconfirm title="是否确认删除" placement="left" @confirm="remove(row)">
          <vxe-button type="text" status="danger">删除</vxe-button>
        </a-popconfirm>
      </template>
    </vxe-grid>
    <BasicDrawer
      width="600px"
      @register="registerDrawer"
      :showFooter="true"
      :title="!isEdit ? '新增' : '编辑'"
      @ok="handleSubmit"
    >
      <BasicForm style="height: 300px" @register="registerForm"></BasicForm>
    </BasicDrawer>
  </div>
</template>
<script setup lang="ts">
  import { nextTick, onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    getItemsApi,
    itemAdd,
    itemEdit,
    itemRemove,
  } from '/@/views/demo/system/transfer/api/transfer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useDrawer } from '/@/components/Drawer';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { useForm } from '/@/components/Form';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';

  const route = useRoute();
  const router = useRouter();
  let isEdit = ref(false);
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawer();
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: [
      {
        field: 'id',
        label: 'id',
        required: false,
        show: false,
        component: 'Input',
        colProps: { span: 24 },
      },
      {
        field: 'chapter',
        label: '编号',
        required: true,
        component: 'Input',
        colProps: { span: 24 },
        componentProps: {
          placeholder: '请输入编号',
          disabled: isEdit,
        },
        rules: [
          { required: true, message: '编号不能为空!' },
          { pattern: /^[0-9.]+$/, message: '编号只能包含数字和点!' },
        ],
      },
      {
        field: 'provisions',
        label: '条文',
        required: true,
        component: 'Input',
        colProps: { span: 24 },
        componentProps: {
          placeholder: '请输入条文',
        },
      },
    ],
    showActionButtonGroup: false,
  });
  const id = route.params?.id ?? -1;
  const row = route.query;
  delete row._X_ROW_KEY;
  delete row.key;
  const loading = ref(false);
  const tableData = ref([]);
  const { createMessage } = useMessage();
  const gridOptions = ref({
    border: true,
    treeConfig: {
      rowField: 'id',
      childrenField: 'children',
    },
    columns: [
      { field: 'chapter', title: '编号', treeNode: true, width: '20%' },
      { field: 'provisions', title: '条文', width: '60%' },
      { title: '操作', slots: { default: 'active' }, width: '20%' },
    ],
    data: tableData,
  });
  onMounted(() => {
    getList();
  });

  function goBack() {
    router.push('/newLine/transfer/transferTpl');
  }

  async function getList() {
    tableData.value = await getItemsApi(id);
  }

  const add = () => {
    isEdit.value = false;
    resetFields();
    openDrawer();
  };

  const edit = (row) => {
    isEdit.value = true;
    openDrawer();
    nextTick(() => {
      setFieldsValue(row);
    });
  };

  const remove = (row) => {
    itemRemove(row.id).then((res) => {
      if (res) {
        createMessage.success('删除成功');
      } else {
        createMessage.error('删除失败');
      }
      getList();
    });
  };
  const handleSubmit = async () => {
    try {
      let values = await validate();
      let res;
      if (isEdit.value) {
        res = await itemEdit(values);
      } else {
        res = await itemAdd(id, values);
      }
      if (res) {
        createMessage.success('保存成功');
      } else {
        createMessage.error('保存失败');
      }
    } catch (error) {
      createMessage.error('保存失败');
    } finally {
      closeDrawer();
      getList();
    }
  };
</script>
