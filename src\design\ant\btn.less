// button reset
.ant-btn {
  &-link:hover,
  &-link:focus,
  &-link:active {
    border-color: transparent !important;
  }

  &-primary {
    color: @white;
    background-color: @button-primary-color;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-primary-hover-color;
    }
  }

  &-primary:not(&-background-ghost):not([disabled]) {
    color: @white;
  }

  &-default {
    color: @button-cancel-color;
    background-color: @button-cancel-bg-color;
    border-color: @button-cancel-border-color;

    &:hover,
    &:focus {
      color: @button-cancel-hover-color;
      background-color: @button-cancel-hover-bg-color;
      border-color: @button-cancel-hover-border-color;
    }
    //
    //&[disabled],
    //&[disabled]:hover {
    //  color: fade(@button-cancel-color, 40%) !important;
    //  background: fade(@button-cancel-bg-color, 40%) !important;
    //  border-color: fade(@button-cancel-border-color, 40%) !important;
    //}
  }

  [data-theme='light'] &.ant-btn-link.is-disabled {
    color: rgb(0 0 0 / 25%);
    text-shadow: none;
    cursor: not-allowed !important;
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none;
  }

  [data-theme='dark'] &.ant-btn-link.is-disabled {
    color: rgb(255 255 255 / 25%) !important;
    text-shadow: none;
    cursor: not-allowed !important;
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none;
  }

  // color: @white;

  &-success.ant-btn-link:not([disabled='disabled']) {
    color: @button-success-color;

    &:hover,
    &:focus {
      color: @button-success-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-success-active-color;
    }
  }

  &-success.ant-btn-link.ant-btn-loading,
  &-warning.ant-btn-link.ant-btn-loading,
  &-error.ant-btn-link.ant-btn-loading,
  &-background-ghost.ant-btn-link.ant-btn-loading,
  &.ant-btn-link.ant-btn-loading {
    &::before {
      background: transparent;
    }
  }

  &-success:not(.ant-btn-link, .is-disabled) {
    color: @white;
    background-color: @button-success-color;
    border-color: @button-success-color;
    //border-width: 0;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-success-hover-color;
      border-color: @button-success-hover-color;
    }

    &:active {
      background-color: @button-success-active-color;
      border-color: @button-success-active-color;
    }
  }

  &-warning.ant-btn-link:not([disabled='disabled']) {
    color: @button-warn-color;

    &:hover,
    &:focus {
      color: @button-warn-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-warn-active-color;
    }
  }

  &-warning:not(.ant-btn-link, .is-disabled) {
    color: @white;
    background-color: @button-warn-color;
    border-color: @button-warn-color;
    //border-width: 0;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-warn-hover-color;
      border-color: @button-warn-hover-color;
    }

    &:active {
      background-color: @button-warn-active-color;
      border-color: @button-warn-active-color;
    }

    //&[disabled],
    //&[disabled]:hover {
    //  color: @white;
    //  background-color: fade(@button-warn-color, 40%);
    //  border-color: fade(@button-warn-color, 40%);
    //}
  }

  &-error.ant-btn-link:not([disabled='disabled']) {
    color: @button-error-color;

    &:hover,
    &:focus {
      color: @button-error-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-error-active-color;
    }
  }

  &-error:not(.ant-btn-link, .is-disabled) {
    color: @white;
    background-color: @button-error-color;
    border-color: @button-error-color;
    //border-width: 0;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-error-hover-color;
      border-color: @button-error-hover-color;
    }

    &:active {
      background-color: @button-error-active-color;
      border-color: @button-error-active-color;
    }

    //&[disabled],
    //&[disabled]:hover {
    //  color: @white;
    //  background-color: fade(@button-error-color, 40%);
    //  border-color: fade(@button-error-color, 40%);
    //}
  }

  &-background-ghost {
    border-width: 1px;
    background-color: transparent !important;

    &[disabled],
    &[disabled]:hover {
      color: fade(@white, 40%) !important;
      background-color: transparent !important;
      border-color: fade(@white, 40%) !important;
    }
  }

  &-dashed&-background-ghost,
  &-default&-background-ghost {
    color: @button-ghost-color;
    border-color: @button-ghost-color;

    &:hover,
    &:focus {
      color: @button-ghost-hover-color;
      border-color: @button-ghost-hover-color;
    }

    &:active {
      color: @button-ghost-active-color;
      border-color: @button-ghost-active-color;
    }

    &[disabled],
    &[disabled]:hover {
      color: fade(@white, 40%) !important;
      border-color: fade(@white, 40%) !important;
    }
  }

  &-background-ghost&-success:not(.ant-btn-link) {
    color: @button-success-color;
    background-color: transparent;
    border-color: @button-success-color;
    border-width: 1px;

    &:hover,
    &:focus {
      color: @button-success-hover-color !important;
      border-color: @button-success-hover-color;
    }

    &:active {
      color: @button-success-active-color;
      border-color: @button-success-active-color;
    }
  }

  &-background-ghost&-warning:not(.ant-btn-link) {
    color: @button-warn-color;
    background-color: transparent;
    border-color: @button-warn-color;
    border-width: 1px;

    &:hover,
    &:focus {
      color: @button-warn-hover-color !important;
      border-color: @button-warn-hover-color;
    }

    &:active {
      color: @button-warn-active-color;
      border-color: @button-warn-active-color;
    }
  }

  &-background-ghost&-error:not(.ant-btn-link) {
    color: @button-error-color;
    background-color: transparent;
    border-color: @button-error-color;
    border-width: 1px;

    &:hover,
    &:focus {
      color: @button-error-hover-color !important;
      border-color: @button-error-hover-color;
    }

    &:active {
      color: @button-error-active-color;
      border-color: @button-error-active-color;
    }
  }

  &-ghost.ant-btn-link:not([disabled='disabled']) {
    color: @button-ghost-color;

    &:hover,
    &:focus {
      color: @button-ghost-hover-color;
      border-color: transparent;
    }
  }
}
