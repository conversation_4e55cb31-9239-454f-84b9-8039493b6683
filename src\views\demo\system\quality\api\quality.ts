import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';
import { UploadApiResult } from '/@/api/sys/model/uploadModel';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  GetItems = '/newline/quality/items/',
  GetOptions = '/newline/line/list',
  Issued = '/newline/quality/issued',
  Upload = '/newline/quality/upload/',
  Persist = '/newline/quality/save/temp/',
  ItemRemove = '/newline/quality/item/remove',
  ItemEdit = '/newline/quality/item/edit',
  ItemAdd = '/newline/quality/item/add',
  getSystemItems = '/newline/quality/getSystemItems',
  GetViews = '/newline/quality/tplView/',
}

const globSetting = useGlobSetting();

export const getItemsApi = async (id) => {
  return await defHttp.get({ url: Api.GetItems + id });
};
export const getViewApi = async (id) => {
  return await defHttp.get({ url: Api.GetViews + id });
};
export const getSystemItemsApi = async () => {
  return await defHttp.get({ url: Api.getSystemItems });
};

export const getOptionsApi = async () => {
  return await defHttp.get({ url: Api.GetOptions });
};

export const issuedApi = async (id) => {
  return await defHttp.post({ url: Api.Issued + `/${id}` });
};

export const persistApi = async (key) => {
  return await defHttp.post({ url: Api.Persist + `${key}` });
};

export const itemRemove = async (id) => {
  return await defHttp.post({ url: Api.ItemRemove + `/${id}` });
};

export const itemEdit = async (params) => {
  return await defHttp.post({ url: Api.ItemEdit, params });
};

export const itemAdd = async (id, params) => {
  return await defHttp.post({ url: Api.ItemAdd + `/${id}`, params });
};

export const getUpLoadApi = (tplid) => {
  return (params: UploadFileParams, onUploadProgress: (progressEvent: ProgressEvent) => void) => {
    return defHttp.uploadFile<UploadApiResult>(
      {
        url: `${globSetting.apiUrl}${Api.Upload}${tplid}`,
        onUploadProgress,
      },
      params,
    );
  };
};
