import { BasicFetchResult } from '/@/api/model/baseModel';
/**
 * @description: Login interface parameters
 */
export interface LoginParams {
  username: string;
  password: string;
}

export interface CasLoginParams {
  ticket: string;
}

export interface RoleInfo {
  roleName: string;
  value: string;
}

/**
 * @description: Login interface return value
 */
export interface LoginResultModel {
  userId: string | number;
  token: string;
  role: RoleInfo;
}

export type ForgetParams = {
  personNo?: string;
  smsCode?: string;
};

export interface ForgetItem {
  id: string;
  personNo: string;
  phone: string;
  smsKey: string;
  smsCode: string;
}

export type ForgetGetResultModel = BasicFetchResult<ForgetItem>;

/**
 * @description: Get user information return value
 */
export interface GetUserInfoModel {
  roles: RoleInfo[];
  // 用户id
  userId: string | number;
  // 用户名
  username: string;
  // 真实名字
  realName: string;
  // 头像
  avatar: string;
  // 介绍
  desc?: string;
  // 首次登陆
  firstLogin: boolean;
}
