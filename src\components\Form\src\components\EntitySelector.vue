<template>
  <div>
    <a-input-search
      readOnly
      v-model:value="label"
      placeholder="请选择"
      style="width: 100%"
      @search="onSearch"
      :disabled="disabled"
      allowClear
    />
    <BasicModal :width="1200" v-bind="$attrs" @register="register" title="值列表" @ok="handleSubmit"
                @cancel="close">
      <vxe-basic-table ref="tableRef" :columns="columns" :proxyConfig="{
          ajax: {
            query: async ({page, form}) => {
              return this.search(page, form);
            },
          },
        }" :formConfig="{
          enabled: true,
          items: this.querys,
        }" :exportConfig="{
        enabled: false,
        }" :toolbarConfig="{enabled:false}"
      >
        <template #upload="row,key">
          <div>
            <Tooltip placement="bottom">
              <a-button @click="previewFile(row,key)">
              <Icon icon="bi:eye"/>
              </a-button>
            </Tooltip>

          </div>
        </template>
        <template #tags="row,key">
          <div v-for="tag in row[key]">
            <Tag>{{tag}}</Tag>
          </div>
        </template>
      </vxe-basic-table>
      <!--<Form
        ref="formElRef"
        @keypress.enter="handleEnterPress"
      >
        <Row>
          <template v-for="schema in querys" :key="schema.key">
            &lt;!&ndash;            <FormItem&ndash;&gt;
            &lt;!&ndash;              :schema="schema"&ndash;&gt;
            &lt;!&ndash;            >&ndash;&gt;
            &lt;!&ndash;            </FormItem>&ndash;&gt;

            <FormItem
              :label="schema.title"
              :name="schema.key"
            >
              <Col :span="20">
                <a-input v-if="schema.component == 'INPUT'"
                         v-model:value="formState[schema.key]"></a-input>
                <a-input v-if="schema.component == 'NUMBER'" v-model:value="formState[schema.key]"
                         type="number"></a-input>
                <date-picker v-if="schema.component == 'DATE'" v-model:value="formState[schema.key]"
                             value-format="YYYY-MM-DD"/>
                <date-picker v-if="schema.component == 'YEAR'" v-model:value="formState[schema.key]"
                             value-format="YYYY"/>
                <a-select
                  v-if="schema.component == 'BOOLEAN'"
                  style="width: 120px"
                  allowClear
                  v-model:value="formState[schema.key]"
                >
                  <select-option :value="true">{{schema.props.trueText}}</select-option>
                  <select-option :value="false">{{schema.props.falseText}}</select-option>
                </a-select>

              </Col>
            </FormItem>

          </template>

          <FormItem>
            <div style="width: 100%">
              <Col :span="20">
                <FormItem>
                  <Button
                    type="default"
                    class="mr-2"
                    @click="clear"
                  >
                    重置
                  </Button>
                  <Button
                    type="primary"
                    class="mr-2"
                    @click="search"
                  >
                    查询
                  </Button>
                </FormItem>
              </Col>
            </div>
          </FormItem>

          &lt;!&ndash;          <FormAction v-bind="getFormActionBindProps" @toggle-advanced="handleToggleAdvanced">&ndash;&gt;
          &lt;!&ndash;            <template&ndash;&gt;
          &lt;!&ndash;              #[item]="data"&ndash;&gt;
          &lt;!&ndash;              v-for="item in ['resetBefore', 'submitBefore', 'advanceBefore', 'advanceAfter']"&ndash;&gt;
          &lt;!&ndash;            >&ndash;&gt;
          &lt;!&ndash;              <slot :name="item" v-bind="data || {}"></slot>&ndash;&gt;
          &lt;!&ndash;            </template>&ndash;&gt;
          &lt;!&ndash;          </FormAction>&ndash;&gt;
          <slot name="formFooter"></slot>
        </Row>
      </Form>
      <a-table :row-selection="{ selectedRowKeys, onChange: onSelectChange, type: 'radio' }"
               :dataSource="dataSource" :columns="columns" :pagination="pagination"></a-table>-->
      <!--<VxeTable>
      </VxeTable>-->

    </BasicModal>
    <EruptUploadPreviewModal :readOnly="true" ref="uploadModalRef" @Register="registerPreviewModal"/>
  </div>

</template>
<script lang="ts">
  import {defineComponent, PropType, ref, watchEffect, computed, unref, watch, h} from 'vue';
  import {BasicModal, useModal} from "/@/components/Modal";
  import {Icon} from "/@/components/Icon";
  import {
    Table,
    Form,
    Row,
    FormItem,
    Col,
    Button,
    DatePicker,
    RadioGroup,
    Radio,
    Select,
    SelectOption,
    Tooltip,
    Tag
  } from 'ant-design-vue';
  import {bool, string} from "vue-types";

  import {
    buildApi,
    tableQueryApi,
    refBuildApi,
    refQueryApi,
    refTreeQueryApi
  } from "/@/api/erupt/erupt";
  import EruptUploadPreviewModal from "/@/components/Form/src/components/EruptUploadPreviewModal.vue";
  import {componentMap, getColFomatterEn} from '../componentMap';
  import {getColFomatter, getSearchComponent} from "/@/components/EruptTable/componets";
  import VxeBasicTable from "/@/components/VxeTable/src/VxeBasicTable";
  const useForm = Form.useForm;

  export default defineComponent({
    name: 'EntitySelector',
    props: {
      value: {type: Object},
      className: {type: string},
      labelField: {type: string},
      valueField: {type: string},
      refClassName: {type: string},
      disabled: {type: bool, default: false}
    },
    emits: ['change', 'update:value'],
    components: {
      Tooltip,
      Icon,
      BasicModal,
      aTable: Table,
      aButton:Button,
      Form,
      Row,
      FormItem,
      Col,
      Button,
      DatePicker,
      RadioGroup,
      Radio,
      Tag,
      aSelect: Select,
      SelectOption,
      VxeBasicTable,
      EruptUploadPreviewModal
      //FormAction
    },
    setup(props, {attrs, emit}) {
      const {className,refClassName, labelField, valueField, disabled} = props
      const [register, {openModal, closeModal}] = useModal();
      const [registerPreviewModal, {openModal: openPreviewModal}] = useModal();
      const tableRef = ref()
      const uploadModalRef = ref()
      let dataSource = ref([])
      let columns = [{type: 'radio'}];
      let querys = []
      const pagination = ref({
        // 分页配置
        pageSize: 10, // 每页显示的条数
        showSizeChanger: true, // 是否可以改变每页显示的条数
        pageSizeOptions: ['10', '20', '30', '40'], // 可选的每页显示条数
        showQuickJumper: true, // 是否可以快速跳转到指定页
        showTotal: total => `共 ${total} 条`, // 显示总条数和当前数据范围
        current: 1,
        onChange: (page,pageSize) => {
          pagination.value.current = page
          pagination.value.pageSize = pageSize
        } // 页码改变时的回调函数
      })
      const selectedRowKeys = ref(null)
      const rowkey = ref('')
      const selectedRows = ref({})
      const formState = ref({});
      const label = ref('')
      refBuildApi(className,refClassName).then(res => {
        debugger
        const {eruptFieldModels} = res.eruptModel
        console.log(res.eruptModel)
        eruptFieldModels.forEach(item => {
          const key = item.fieldName
          const title = item.eruptFieldJson.edit.title
          item.eruptFieldJson.views.forEach(v => {
            if (v.show) {
              columns.push(getColFomatter(key,v,item.eruptFieldJson.edit))
            }
          })
          //  formState[key] = null
          if (item.eruptFieldJson.edit.search.value) {
            querys.push(getSearchComponent(key,title,item,8,refClassName))

          }
        })
        querys.push({
          span: 4,
          align: 'right',
          className: '!pr-0',
          itemRender: {
            name: 'AButtonGroup',
            children: [
              {
                props: {type: 'primary', content: '查询', htmlType: 'submit'},
                attrs: {class: 'mr-2'},
              },
              {props: {type: 'default', htmlType: 'reset', content: '重置'}},
            ],
          },
        })
        console.log(columns)
       // search()
      })

      watch(
        () => rowkey.value,
        (v) => {

          emit('update:value', selectedRows.value);
          return emit('change', selectedRows.value);
        },
      );
      watch(
        () => props.value,
        (v) => {
          if (v) {
            label.value = v[labelField]
          } else {
            label.value = ''
            selectedRows.value = null
            rowkey.value = ''
            if (tableRef.value)
            tableRef.value.clearRadioRow()
          }

        },
      )

      async function onSearch() {
        openModal()

      }
      function previewFile(row,key) {
        console.log(row,key)
        const files = row.row[key] ? row.row[key].split('|') : []
        console.log(uploadModalRef)
        uploadModalRef.value.setFiles(files)
        openPreviewModal()
      }
      function search(page, form) {
        console.log(page, form.value)
        let condition = []
        for (let key in form) {
          if (form[key]) {
            condition.push({key, value: form[key]})
          }
        }
        const query = {
          pageIndex: page.currentPage,
          pageSize: page.pageSize,
          condition
        }
        return refQueryApi(className,refClassName, query)
      }
      /*function search() {
          console.log(pagination)
          let condition = []
          for (let key in formState.value) {
            if (formState.value[key]) {
              condition.push({key,value:formState.value[key]})
            }
          }
          const query = {
            pageIndex: pagination.value.current,
            pageSize: pagination.value.pageSize,
            condition
          }
        refQueryApi(className,refClassName,query).then(res => {
            dataSource.value = res.records
          })
      }*/
      function handleSubmit() {
        const selected = tableRef.value.getRadioRecord()
        rowkey.value = selected.key
        selectedRows.value = selected
        label.value = selected[labelField]
        closeModal()
      }

      return {
        onSearch,
        register,
        dataSource,
        pagination,
        columns,
        querys,
        label,
        handleSubmit,
        formState,
        componentMap,
        search,
        selectedRowKeys,
        openPreviewModal,
        uploadModalRef,
        registerPreviewModal,
        tableRef,
        previewFile,
        close: () => {
          if (!label.value) {
            rowkey.value = ''
            selectedRows.value = ''
            label.value = ''
            tableRef.value.clearRadioRow()
          }
          //resetFields()
          closeModal()
        }
      };
    },
  });

</script>
