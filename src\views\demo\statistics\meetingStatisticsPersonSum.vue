<template>
  <div style="padding: 10px">
    <Card title="会议管理视图 - 月度每日会议统计" :loading="loading">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- 查询区域 -->
        <a-row justify="end" style="margin-bottom: 20px">
          <a-col>
            <a-space>
              <span style="font-weight: bold">选择月份：</span>
              <a-date-picker
                v-model:value="selectedMonth"
                picker="month"
                format="YYYY-MM"
                @change="fetchData"
              />
              <a-button type="primary" @click="fetchData">查询</a-button>
              <a-button @click="refreshData">刷新</a-button>
            </a-space>
          </a-col>
        </a-row>

        <!-- 摘要卡片 -->
        <a-card type="inner" style="width: 100%">
          <a-row :gutter="16">
            <a-col :span="24">
              <div style="font-size: 16px; margin-bottom: 15px; font-weight: bold">
                {{ summary.currentMonth }} 会议统计摘要
              </div>
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic
                    title="会议总场次"
                    :value="summary.totalMeetings"
                    style="text-align: center"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="参会总人数"
                    :value="summary.totalParticipants"
                    style="text-align: center"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="最高会议场次"
                    :value="summary.maxMeetingsPerDay"
                    style="text-align: center"
                  >
                    <template #suffix>场</template>
                  </a-statistic>
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="最高参会人数"
                    :value="summary.maxParticipantsPerDay"
                    style="text-align: center"
                  >
                    <template #suffix>人</template>
                  </a-statistic>
                </a-col>
              </a-row>
            </a-col>
          </a-row>
        </a-card>

        <!-- 图表卡片 -->
        <a-card type="inner" style="width: 100%">
          <a-row :gutter="16">
            <a-col :span="24">
              <div ref="chartRef" v-loading="chartLoading" style="width: 100%; height: 500px">
                <a-empty v-if="isEmptyData" description="暂无数据" style="margin-top: 100px" />
              </div>
            </a-col>
          </a-row>
        </a-card>
      </a-space>
    </Card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Card, DatePicker, Button, Statistic, Row, Col, Space, Empty } from 'ant-design-vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { getMeetingDailyStats } from '/@/views/demo/statistics/api/statistics';
  import dayjs from 'dayjs';

  const AEmpty = Empty;

  const props = defineProps({
    loading: Boolean,
  });

  const chartRef = ref(null);
  const { setOptions } = useECharts(chartRef);
  const chartLoading = ref(false);
  const selectedMonth = ref(dayjs());
  const isEmptyData = ref(false);

  const chartData = reactive({
    days: [] as string[],
    meetingCounts: [] as number[],
    participantCounts: [] as number[],
  });

  const summary = reactive({
    currentMonth: '',
    totalMeetings: 0,
    totalParticipants: 0,
    maxMeetingsPerDay: 0,
    maxParticipantsPerDay: 0,
  });

  // 计算是否有数据 (修复后的逻辑)
  const hasData = () => {
    return (
      chartData.days.length > 0 &&
      (chartData.meetingCounts.some((count) => count > 0) ||
        chartData.participantCounts.some((count) => count > 0))
    );
  };

  // 初始化图表
  const initChart = () => {
    if (!hasData()) {
      isEmptyData.value = true;
      return;
    }

    isEmptyData.value = false;
    setOptions({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        formatter: (params: any[]) => {
          let result = `${params[0].axisValue}<br>`;
          params.forEach((item) => {
            result += `${item.marker} ${item.seriesName}: ${item.value}`;
            result += item.seriesName === '会议场次' ? '场' : '人';
            result += '<br>';
          });
          return result;
        },
      },
      legend: {
        data: ['会议场次', '参会人数'],
        bottom: 10,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: chartData.days,
        axisLabel: {
          interval: 0,
          rotate: 45,
        },
        axisPointer: {
          type: 'shadow',
        },
        name: '日期',
      },
      yAxis: [
        {
          type: 'value',
          name: '会议场次',
          position: 'left',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#5470C6',
            },
          },
          axisLabel: {
            formatter: '{value} 场',
          },
        },
        {
          type: 'value',
          name: '参会人数',
          position: 'right',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#91CC75',
            },
          },
          axisLabel: {
            formatter: '{value} 人',
          },
        },
      ],
      series: [
        {
          name: '会议场次',
          type: 'line',
          smooth: true,
          yAxisIndex: 0,
          data: chartData.meetingCounts,
          itemStyle: {
            color: '#5470C6',
          },
          lineStyle: {
            width: 3,
          },
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' },
            ],
          },
        },
        {
          name: '参会人数',
          type: 'line',
          smooth: true,
          yAxisIndex: 1,
          data: chartData.participantCounts,
          itemStyle: {
            color: '#91CC75',
          },
          lineStyle: {
            width: 3,
          },
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' },
            ],
          },
        },
      ],
    });
  };

  // 获取数据
  const fetchData = async () => {
    chartLoading.value = true;
    try {
      const month = selectedMonth.value ? dayjs(selectedMonth.value).format('YYYY-MM') : '';
      const res = await getMeetingDailyStats(month);

      // 重置数据
      chartData.days = [];
      chartData.meetingCounts = [];
      chartData.participantCounts = [];

      // 更新数据
      if (res && res.chart) {
        chartData.days = res.chart.days || [];
        chartData.meetingCounts = res.chart.meetingCounts || [];
        chartData.participantCounts = res.chart.participantCounts || [];
      }

      // 更新摘要数据
      if (res && res.summary) {
        Object.assign(summary, res.summary);
      } else {
        // 清空摘要数据
        Object.assign(summary, {
          currentMonth: month,
          totalMeetings: 0,
          totalParticipants: 0,
          maxMeetingsPerDay: 0,
          maxParticipantsPerDay: 0,
        });
      }

      // 初始化或更新图表
      initChart();
    } catch (error) {
      console.error('获取数据失败:', error);
      isEmptyData.value = true;
    } finally {
      chartLoading.value = false;
    }
  };

  // 刷新数据
  const refreshData = () => {
    fetchData();
  };

  // 初始化
  onMounted(() => {
    fetchData();
  });
</script>
