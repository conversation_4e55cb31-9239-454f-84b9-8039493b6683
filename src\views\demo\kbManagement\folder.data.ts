import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '文件名',
    dataIndex: 'fileName',
    width: 200,
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    width: 100,
  },
  {
    title: '文件类型',
    dataIndex: 'type',
    width: 180,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'fileName',
    label: '文件名',
    component: 'Input',
    colProps: { span: 8 },
  },
];
