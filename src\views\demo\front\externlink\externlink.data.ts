import { BasicColumn, FormSchema } from '/@/components/Table';
import { uploadApi } from '/@/api/demo/ExternLinkUpload';

export const columns: BasicColumn[] = [
  {
    title: '排序',
    dataIndex: 'pos',
    width: 50,
  },
  {
    title: '外部链接名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '外部链接地址',
    dataIndex: 'url',
    width: 200,
  },
  {
    title: '外部链接图标',
    dataIndex: 'icon',
    width: 300,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '外部链接名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'url',
    label: '外部链接',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    required: false,
    component: 'Input',
    show: false,
  },
  {
    field: 'name',
    label: '外部链接名称',
    required: true,
    component: 'Input',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'pos',
    label: '外部链接排序',
    required: true,
    component: 'Input',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'url',
    label: '外部链接地址',
    required: true,
    component: 'Input',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'icon',
    label: '外部链接图标',
    required: false,
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'file',
    label: '文件上传',
    required: false,
    component: 'Upload',
    componentProps: ({ formModel }) => {
      return {
        api: uploadApi,
        maxSize: 10,
        onChange: (list: string[]) => {
          console.log(JSON.stringify(list));
          // or
          formModel.icon = list[0];
        },
      };
    },
    colProps: {
      span: 12,
    },
    suffix: '最佳图片分辨率 718*84 px',
  },
  {
    field: 'note',
    label: '备注',
    required: false,
    component: 'InputTextArea',
    componentProps: {
      rows: 8,
    },
    colProps: {
      span: 24,
    },
  },
];
