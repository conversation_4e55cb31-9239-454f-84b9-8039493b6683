import { BasicColumn, FormSchema } from '/@/components/Table';
import { uploadApi } from '/@/api/demo/BannerItemUpload';

export const columns: BasicColumn[] = [
  {
    title: '横幅排序',
    dataIndex: 'pos',
    width: 50,
  },
  {
    title: '横幅名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '横幅跳转地址',
    dataIndex: 'imgUrl',
    width: 200,
  },
  {
    title: '提示',
    dataIndex: 'tip',
    width: 400,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '横幅名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'tip',
    label: '提示',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    required: false,
    component: 'Input',
    show: false,
  },
  {
    field: 'name',
    label: '横幅名称',
    required: true,
    component: 'Input',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'pos',
    label: '排序',
    required: true,
    component: 'Input',
    colProps: {
      span: 12,
    },
  },
  {
    field: 'tip',
    label: '提示',
    required: true,
    component: 'InputTextArea',
    componentProps: {
      rows: 8,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'tipEnable',
    label: '是否提示',
    required: false,
    component: 'RadioGroup',
    defaultValue: '1',
    componentProps: {
      options: [
        { label: '开启', value: '0' },
        { label: '关闭', value: '1' },
      ],
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'buttonEnable',
    label: '是否跳转',
    required: false,
    component: 'RadioGroup',
    defaultValue: '1',
    componentProps: {
      options: [
        { label: '开启', value: '0' },
        { label: '关闭', value: '1' },
      ],
    },
    colProps: {
      span: 12,
    },
  },
  {
    field: 'imgUrl',
    label: '图片',
    required: false,
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'file',
    label: '上传图片',
    required: false,
    component: 'Upload',
    componentProps: ({ formModel }) => {
      return {
        api: uploadApi,
        maxSize: 10,
        onChange: (list: string[]) => {
          console.log(JSON.stringify(list));
          // or
          formModel.imgUrl = list[0];
        },
      };
    },
    colProps: {
      span: 12,
    },
    suffix: '最佳图片分辨率 1920*354 px',
  },
  {
    field: 'url',
    label: '跳转地址',
    required: true,
    component: 'Input',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'note',
    label: '备注',
    required: false,
    component: 'InputTextArea',
    componentProps: {
      rows: 8,
    },
    colProps: {
      span: 24,
    },
  },
];
