import {
  create<PERSON>dit<PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  createForm<PERSON>temR<PERSON>,
  createDefaultFilterR<PERSON>,
  createDefaultRender,
} from './common';

export default {
  autofocus: 'textarea.ant-input',
  renderDefault: createDefaultRender(),
  renderEdit: createEdit<PERSON>ender(),
  renderFilter: createFilterRender(),
  defaultFilterMethod: createDefaultFilterRender(),
  renderItemContent: createFormItemRender(),
};
