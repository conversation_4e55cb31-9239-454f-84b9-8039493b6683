<template>
  <div class="lg:flex">
    <Avatar :src="userinfo.avatar || headerImg" :size="72" class="!mx-auto !block" />
    <div class="md:ml-6 flex flex-col justify-center md:mt-0 mt-2">
      <a-space direction="horizontal" align="center">
        <h1 class="md:text-lg text-md mr-4">
          你好，{{ userinfo.realName }}，欢迎使用南京地铁新线管理系统！
        </h1>
        <div class="flex items-center justify-end text-right">
          <h1 class="mr-2 md:text-lg text-md">已处理问题：</h1>
          <h1 class="mr-2 md:text-lg text-md"><a-statistic :value="questionCount" /></h1>
        </div>
        <div class="flex items-center justify-end text-right ml-4">
          <h1 class="mr-2 md:text-lg text-md">已处理汇编：</h1>
          <h1 class="mr-2 md:text-lg text-md"><a-statistic :value="assemblyCount" /></h1>
        </div>
      </a-space>
    </div>
    <div class="flex flex-1 justify-end md:mt-0 mt-4">
      <div class="flex flex-col justify-center text-right md:mx-16 mx-12">
        <a-statistic
          title="我的待办"
          :value="todoCount"
          @click="handleTodoClick"
          class="cursor-pointer"
        />
      </div>
      <div class="flex flex-col justify-center text-right">
        <a-statistic
          title="我的已办"
          :value="doneCount"
          @click="handleDoneClick"
          class="cursor-pointer"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, ref } from 'vue';
  import { Avatar } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  import headerImg from '/@/assets/images/header.jpg';
  import { getFinishData, getTodoData } from '/@/views/dashboard/workbench/components/api/home';

  const userStore = useUserStore();
  const userinfo = computed(() => userStore.getUserInfo);
  const router = useRouter();

  const todoCount = ref(0);
  const doneCount = ref(0);
  const assemblyCount = ref(0);
  const questionCount = ref(0);

  onMounted(() => {
    getTodo();
    getAssembly();
    getQuestion();
  });

  const handleTodoClick = () => {
    router.push('/todo');
  };

  const handleDoneClick = () => {
    router.push('/done');
  };
  const handleQuestionClick = () => {
    router.push('/questionTodo');
  };

  const handleAssemblyClick = () => {
    router.push('/assemblyTodo');
  };

  const getTodo = async () => {
    const data = await getTodoData();
    todoCount.value = data.todo;
    doneCount.value = data.done;
  };

  const getAssembly = async () => {
    assemblyCount.value = await getFinishData('Assembly');
  };

  const getQuestion = async () => {
    questionCount.value = await getFinishData('Question');
  };
</script>
