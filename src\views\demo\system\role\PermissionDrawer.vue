<template>
  <div>
    <BasicDrawer
      v-bind="$attrs"
      @register="registerDrawer"
      showFooter
      :title="getTitle"
      width="50%"
      @ok="handleSubmit"
    >
      <BasicTable
        :columns="columns"
        :dataSource="tableData"
        :pagination="false"
        :showIndexColumn="false"
      />
    </BasicDrawer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, h } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicTable, BasicColumn } from '/@/components/Table';
  import { Checkbox } from 'ant-design-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getPermissionList, saveRolePermission, getPermissionByRoleId } from './role';
  import { rolePermissionItem } from './model/permission';

  const emit = defineEmits(['success', 'register']);
  const { createConfirm, createMessage } = useMessage();

  const isUpdate = ref(true);
  const roleId = ref<number | undefined>(undefined);
  const tableData = ref<any[]>([]);
  const selectedValues = ref<Record<string, Set<string>>>({});

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    roleId.value = data?.record?.id;
    selectedValues.value = {};
    loadPermissionList();
    if (roleId.value) {
      await loadRolePermissions(roleId.value);
    }
  });

  async function loadRolePermissions(id: number) {
    try {
      const res = await getPermissionByRoleId(id);
      if (res) {
        const permissions = new Set(res);
        tableData.value.forEach((item) => {
          const selected = new Set<string>();
          item.children.forEach((child) => {
            if (permissions.has(child.value)) {
              selected.add(child.value);
            }
          });
          if (selected.size > 0) {
            selectedValues.value[item.field] = selected;
          }
        });
      }
    } catch (error) {
      console.error('加载角色权限失败', error);
      createMessage.error('加载角色权限失败，请稍后再试');
    }
  }

  async function loadPermissionList() {
    try {
      const res = await getPermissionList();
      if (res && Array.isArray(res)) {
        tableData.value = res.map((item) => ({
          field: item.name,
          label: item.name,
          children:
            item.children?.map((child) => ({
              label: child.name,
              value: child.permission,
            })) || [],
        }));
        res.forEach((item) => {
          if (!selectedValues.value[item.name]) {
            selectedValues.value[item.name] = new Set();
          }
        });
      }
    } catch (error) {
      console.error('加载权限列表失败', error);
      createMessage.error('加载权限列表失败，请稍后再试');
    }
  }

  const isParentChecked = (field) => {
    return (
      selectedValues.value[field]?.size ===
      tableData.value.find((item) => item.field === field)?.children.length
    );
  };

  const isChildChecked = (field, value) => {
    return selectedValues.value[field]?.has(value);
  };
  const isParentIndeterminate = (field: string) => {
    const item = tableData.value.find((item) => item.field === field);
    if (!item) return false;
    const allChildren = item.children || [];
    const selectedChildren = selectedValues.value[field] || [];
    return selectedChildren.size > 0 && selectedChildren.size < allChildren.length;
  };
  const toggleParent = (field) => {
    const parent = tableData.value.find((item) => item.field === field);
    if (isParentChecked(field)) {
      selectedValues.value[field].clear();
    } else {
      parent?.children.forEach((child) => {
        selectedValues.value[field].add(child.value);
      });
    }
  };

  const toggleChild = (field, value) => {
    if (selectedValues.value[field].has(value)) {
      selectedValues.value[field].delete(value);
    } else {
      selectedValues.value[field].add(value);
    }
  };

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '一级标题',
      dataIndex: 'label',
      width: '20%',
      customRender: ({ record }) => {
        return h('div', [
          h(
            Checkbox,
            {
              checked: isParentChecked(record.field),
              indeterminate: isParentIndeterminate(record.field),
              onChange: () => toggleParent(record.field),
            },
            () => record.label,
          ),
        ]);
      },
    },
    {
      title: '子项',
      dataIndex: 'children',
      align: 'left',
      customRender: ({ record }) => {
        return h('div', [
          record.children.map((child) =>
            h(
              Checkbox,
              {
                value: child.value,
                key: child.value,
                checked: isChildChecked(record.field, child.value),
                onChange: () => toggleChild(record.field, child.value),
              },
              {
                default: () => child.label,
              },
            ),
          ),
        ]);
      },
    },
  ];

  const getTitle = computed(() => '权限管理');

  // 提交表单
  async function handleSubmit() {
    try {
      setDrawerProps({ confirmLoading: true });
      const permissions = Object.values(selectedValues.value).flatMap((set) => Array.from(set));
      const saveData: rolePermissionItem = {
        roleId: roleId.value!,
        permissions,
      };
      createConfirm({
        iconType: 'warning',
        title: '提示',
        content: '表单有未保存的修改，是否保存？',
        onOk: async () => {
          await saveRolePermission(saveData);
          closeDrawer();
          emit('success');
        },
        onCancel: () => {
          closeDrawer();
        },
      });
    } catch (error) {
      console.error('提交权限失败', error);
      createMessage.error('提交权限失败，请稍后再试');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>

<style>
  /* 隐藏默认展开图标 */
  .ant-table-row-expand-icon {
    display: none;
  }
</style>
