import { useUserStore } from '/@/store/modules/user';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  SaveItems = '/newline/question/save/items/',
  taskList = '/newline/question/process/taskList/',
  taskInfoList = '/newline/question/process/form/taskList/',
  taskInfoListBatch = '/newline/question/process/form/taskList/batch/',
  GetType = '/newline/question/getType',
  TerminateTask = '/newline/question/process/terminateTask/',
}

export const getTasksList = () => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.taskList + userName });
};

export const getType = async () => {
  return await defHttp.get({ url: Api.GetType });
};
export const getFormTaskList = (page, form) => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.taskInfoList + userName, params: { ...page, ...form } });
};

export const getFormTaskListBatch = (page, form) => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.taskInfoListBatch + userName, params: { ...page, ...form } });
};
export const TerminateTask = (taskId, id) => {
  return defHttp.get({ url: Api.TerminateTask + `${taskId}/${id}` });
};

export const SaveItems = (id, form) => {
  return defHttp.post({ url: Api.SaveItems + `${id}`, params: form });
};
