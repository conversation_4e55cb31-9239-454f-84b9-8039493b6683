import { defineComponent, reactive } from 'vue';
import { computed, ref } from 'vue';
import { BasicTableProps, VxeBasicTable, VxeGridInstance } from '/@/components/VxeTable';
import { ActionItem, TableAction } from '/@/components/Table';
const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();
import { useMessage } from '/@/hooks/web/useMessage';
import { BasicForm, useForm } from '/@/components/Form/index';
import { BasicDrawer, useDrawer } from '/@/components/Drawer';
import {
  buildApi,
  tableQueryApi,
  remove,
  batchRemove,
  save,
  getOne,
  update,
  getLeftTree,
} from '/@/api/erupt/erupt';
import TableAdd from '/@/components/Form/src/components/TableAdd';
import TableSelectAdd from '/@/components/Form/src/components/TableSelectAdd';
import TreeSeletAdd from '/@/components/Form/src/components/TreeSeletAdd';
import {
  checkEmpty,
  getColFomatter,
  getComponent,
  getComponentProps,
  getSearchComponent,
} from '/@/components/EruptTable/componets';
import { formatDate } from '@vueuse/shared';
import leftTree from '/@/components/EruptTreeTable/leftTree.vue';
import { Modal, Tooltip, Tabs, TabPane, Col, Tag, Card } from 'ant-design-vue';
import EruptUploadPreviewModal from '/@/components/Form/src/components/EruptUploadPreviewModal.vue';
import { Icon } from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import dynamicForm from '/@/views/demo/form/DynamicForm.vue';
import LeftTree from '/@/components/EruptTreeTable/leftTree.vue';

export default defineComponent({
  name: 'eruptTable',
  props: {
    className: {},
    overwriteAction: { add: {}, edit: {}, detail: {}, delete: {}, batchDelete: {} },
    extraAction: {
      default: {
        nums: 0,
        actions: (row) => {
          return [];
        },
      },
    },
    formDynamicControl: { type: Object, default: {} },
  },
  setup(props, { emit, attrs }) {
    const { className, overwriteAction, formDynamicControl, extraAction } = props;
    const tabs = ref([]);
    const tableRef = ref<VxeGridInstance>();
    const treeRef = ref<VxeGridInstance>();
    const powerRef = ref({});
    const tabValues = ref({});
    const uploadModalRef = ref();
    const columns = ref([]);
    const treeColumns = ref([]);
    const modalTitle = ref('');
    const showFooter = ref(true);
    const base = ref({ id: '', version: '' });
    const [registerDrawer, { openDrawer, closeDrawer, setDrawerProps }] = useDrawer();
    const defaultSpaceRows = ref([]);
    const addSchema = ref([]);
    const editSchema = ref([]);
    const lookSchema = ref([]);
    const drawerWidth = ref('');
    const treeData = ref([]);
    const treeToolbarConfig = ref({});
    const linkTreeVal = ref('');

    const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
      labelWidth: 100,
      schemas: editSchema,
      showActionButtonGroup: false,
    });
    const toolbarConfig = ref({
      buttons: [],
    });
    let querys = ref([]);
    const { createMessage } = useMessage();

    function refresh() {
      document.querySelector('button[content="查询"]').click();
    }

    function handleCancel() {
      resetFields();
    }
    async function handleBatchRemove() {
      const selectedRows = tableRef.value.getCheckboxRecords();
      let ids = [];
      selectedRows.forEach((item) => {
        ids.push(item.id);
      });
      const { status, message } = await batchRemove(className, ids);
      if (status == 'SUCCESS') {
        createMessage.success('删除成功');
        refresh();
      } else {
        createMessage.error('删除失败' + message);
      }
    }
    async function handleSubmit() {
      try {
        let values = await validate();

        setDrawerProps({ confirmLoading: true });
        if (base.value.id) {
          const data = { ...values, ...base.value, ...tabValues.value };
          const { status, message } = await update(className, data, null);
          if (status == 'SUCCESS') {
            createMessage.success('保存成功');
            resetFields;
            refresh();
          } else {
            createMessage.error('保存失败:' + message);
          }
        } else {
          const data = { ...values, ...tabValues.value };
          const { status, message } = await save(className, data, null);
          if (status == 'SUCCESS') {
            createMessage.success('保存成功');
            resetFields;
            refresh();
          } else {
            createMessage.error('保存失败:' + message);
          }
        }
        // TODO custom api

        closeDrawer();
        emit('success');
      } finally {
        setDrawerProps({ confirmLoading: false });
      }
    }

    function updateForm(schema) {
      updateSchema(schema);
    }
    function search(page, form) {
      let condition = [];
      for (let key in form) {
        if (form[key]) {
          condition.push({ key, value: form[key] });
        }
      }
      if (linkTreeVal.value) {
        const query = {
          linkTreeVal: linkTreeVal.value,
          pageIndex: page.currentPage,
          pageSize: page.pageSize,
          condition,
        };
        return tableQueryApi(className, query);
      } else {
        const query = {
          pageIndex: page.currentPage,
          pageSize: page.pageSize,
          condition,
        };
        return tableQueryApi(className, query);
      }
    }

    async function getDetail(id) {
      modalTitle.value = '详情';
      showFooter.value = false;
      openDrawer();
      let schema = [];
      lookSchema.value.forEach((item) => {
        let sc = JSON.parse(JSON.stringify(item));
        const props = item.componentProps();
        sc.componentProps = (form) => {
          return {
            ...props,
            onChange: (e) =>
              formDynamicControl[item.field] ? formDynamicControl[item.field](form, e) : () => {},
          };
        };
        schema.push(sc);
      });

      await getOne(className, id).then((res) => {
        setTimeout(() => {
          updateSchema(schema);
        }, 300);
        setFieldsValue(res);
        let tabV = {};
        for (let key in res) {
          tabs.value.forEach((item) => {
            if (item.key == key) {
              tabV[key] = res[key];
            }
          });
        }
        tabValues.value = tabV;
      });
    }
    async function add() {
      if (overwriteAction && overwriteAction.add) {
        overwriteAction.add();
      } else {
        modalTitle.value = '新增';
        showFooter.value = true;
        openDrawer();
        base.value.id = '';
        base.value.version = '';
        let schema = [];
        debugger;
        console.log(addSchema.value);
        addSchema.value.forEach((item) => {
          let sc = JSON.parse(JSON.stringify(item));
          const props = item.componentProps();
          sc.componentProps = (form) => {
            return {
              ...props,
              onChange: (e) =>
                formDynamicControl[item.field] ? formDynamicControl[item.field](form, e) : () => {},
            };
          };
          schema.push(sc);
        });

        setTimeout(() => {
          updateForm(schema);
        }, 200);
        //resetFields()
        for (let tabValuesKey in tabValues.value) {
          tabValues.value[tabValuesKey] = [];
        }
      }
    }
    async function edit(id) {
      modalTitle.value = '编辑';
      showFooter.value = true;
      openDrawer();
      const res = await getOne(className, id);
      base.value.id = res.id;
      base.value.version = res.version;
      JSON.parse(JSON.stringify(editSchema.value));
      let schema = [];
      editSchema.value.forEach((item) => {
        let sc = JSON.parse(JSON.stringify(item));
        const props = item.componentProps();
        sc.componentProps = (form) => {
          return {
            ...props,
            onChange: (e) =>
              formDynamicControl[item.field] ? formDynamicControl[item.field](form, e) : () => {},
          };
        };
        schema.push(sc);
      });
      setTimeout(() => {
        updateSchema(schema);
      }, 200);

      setFieldsValue(res);
      let tabV = {};
      for (let key in res) {
        tabs.value.forEach((item) => {
          if (item.key == key) {
            tabV[key] = res[key] ? res[key] : [];
          }
        });
      }
      tabValues.value = tabV;
    }

    async function deleted(id) {
      const { status, message } = await remove(className, id);
      if (status == 'SUCCESS') {
        createMessage.success('删除成功');
        refresh();
      } else {
        createMessage.error('删除失败:' + message);
      }
    }

    function generateTab(tab) {
      if (tab.type == 'TAB_TABLE_ADD')
        return (
          <TableAdd
            readOnly={modalTitle.value == '详情'}
            onChange={(v) => {
              tabValues.value[tab.key] = v;
            }}
            value={tabValues.value[tab.key]}
            className={className}
            tabName={tab.key}
            models={tab.eruptModel.eruptFieldModels}
          ></TableAdd>
        );
      if (tab.type == 'TAB_TABLE_REFER')
        return (
          <TableSelectAdd
            readOnly={modalTitle.value == '详情'}
            onChange={(v) => {
              tabValues.value[tab.key] = v;
            }}
            value={tabValues.value[tab.key]}
            className={className}
            tabName={tab.key}
            models={tab.eruptModel.eruptFieldModels}
          ></TableSelectAdd>
        );
      if (tab.type == 'TAB_TREE')
        return (
          <TreeSeletAdd
            readOnly={modalTitle.value == '详情'}
            onChange={(v) => {
              tabValues.value[tab.key] = v;
            }}
            value={tabValues.value[tab.key]}
            className={className}
            tabName={tab.key}
          ></TreeSeletAdd>
        );
    }

    getLeftTree(className).then((res) => {
      const $table = treeRef.value;
      treeData.value = res;
      setTimeout(() => {
        $table?.setAllTreeExpand(true);
      }, 30);
    });

    buildApi(className).then((res) => {
      const {
        eruptModel: { eruptFieldModels, eruptJson },
        tabErupts,
        power,
      } = res;
      let cols = [{ type: 'checkbox' }];
      let qs = [];
      let edits = [];
      let adds = [];
      let details = [];
      let tabItems = [];
      let buttons = [];
      powerRef.value = power;
      drawerWidth.value = eruptJson.drawerWidth;
      if (power.add == true) {
        buttons.push({
          content: '新增',
          buttonRender: {
            name: 'AButton',
            props: {
              type: 'primary',
            },
            events: {
              click: () => add(),
            },
          },
        });
      }
      if (power.delete == true)
        buttons.push({
          content: '删除',
          buttonRender: {
            name: 'AButton',
            props: {
              type: 'warning',
            },
            events: {
              click: () => {
                Modal.confirm({
                  title: '提示',
                  content: '是否确认删除',
                  okText: '确认',
                  cancelText: '取消',
                  onOk() {
                    if (overwriteAction && overwriteAction.batchDelete) {
                      const selectedRows = tableRef.value.getCheckboxRecords();
                      let ids = [];
                      selectedRows.forEach((item) => {
                        ids.push(item.id);
                      });
                      overwriteAction.batchDelete(ids);
                    } else {
                      handleBatchRemove();
                    }
                  },
                });
              },
            },
          },
        });
      let tabV = {};
      eruptFieldModels.forEach((item) => {
        const key = item.fieldName;
        const title = item.eruptFieldJson.edit.title;
        item.eruptFieldJson.views.forEach((v) => {
          if (v.show) {
            cols.push(getColFomatter(key, v, item.eruptFieldJson.edit));
          }
        });
        //  formState[key] = null
        if (item.eruptFieldJson.edit.search.value) {
          qs.push(getSearchComponent(key, title, item, 4, className));
        }
        if (item.eruptFieldJson.edit.show.edit_show && key !== 'id') {
          if (
            item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
            item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
            item.eruptFieldJson.edit.type !== 'TAB_TREE'
          ) {
            const e = {
              field: key,
              label: title,
              component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
              componentProps: (form) => {
                return {
                  ...getComponentProps(item, className),
                  disabled: item.eruptFieldJson.edit.readOnly.edit,
                  onChange: (e) =>
                    formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {},
                };
              },
              required: item.eruptFieldJson.edit.notNull,
              colProps: {
                span: item.eruptFieldJson.edit.colSpan,
              },
            };
            const a = {
              field: key,
              label: title,
              component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
              componentProps: (form) => {
                return {
                  ...getComponentProps(item, className),
                  disabled: item.eruptFieldJson.edit.readOnly.add,
                  onChange: (e) =>
                    formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {},
                };
              },
              required: item.eruptFieldJson.edit.notNull,
              colProps: {
                span: item.eruptFieldJson.edit.colSpan,
              },
            };
            const d = {
              field: key,
              label: title,
              component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
              componentProps: (form) => {
                return {
                  ...getComponentProps(item, className),
                  disabled: true,
                  onChange: (e) =>
                    formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {},
                };
              },
              required: item.eruptFieldJson.edit.notNull,
              colProps: {
                span: item.eruptFieldJson.edit.colSpan,
              },
            };
            edits.push(e);
            adds.push(a);
            details.push(d);
          } else {
            if (tabErupts) {
              for (let tabEruptsKey in tabErupts) {
                if (item.fieldName == tabEruptsKey) {
                  tabErupts[tabEruptsKey].title = item.eruptFieldJson.edit.title;
                  tabErupts[tabEruptsKey].key = tabEruptsKey;
                  tabErupts[tabEruptsKey].type = item.eruptFieldJson.edit.type;
                  tabItems.push(tabErupts[tabEruptsKey]);
                  tabV[tabEruptsKey] = [];
                }
              }
            }
          }
        }
      });
      tabs.value = tabItems;
      tabValues.value = tabV;
      console.log('tabs', tabs.value, tabValues.value);
      qs.push({
        span: 4,
        align: 'right',
        className: '!pr-0',
        itemRender: {
          name: 'AButtonGroup',
          children: [
            {
              props: { type: 'primary', content: '查询', htmlType: 'submit' },
              attrs: { class: 'mr-2' },
            },
            { props: { type: 'default', htmlType: 'reset', content: '重置' } },
          ],
        },
      });

      let Awidth = 60;
      if (powerRef.value.edit) {
        Awidth += 60;
      }
      if (powerRef.value.delete) {
        Awidth += 60;
      }
      Awidth += 60 * extraAction.nums;
      debugger;
      cols.push({
        title: '操作',
        align: 'center',
        width: Awidth,
        slots: { default: 'action' },
        fixed: 'right',
      });
      querys.value = qs;
      columns.value = cols;
      editSchema.value = edits;
      addSchema.value = adds;
      lookSchema.value = details;
      toolbarConfig.value.buttons = buttons;
      //gridOptions.columns = columns
      //search()
    });
    function getActions(row) {
      let actions = [
        {
          label: '详情',
          onClick: () => {
            this.getDetail(row.row.id);
          },
        },
      ];
      if (powerRef.value.edit) {
        actions.push({
          label: '编辑',
          onClick: () => {
            if (overwriteAction && overwriteAction.edit) {
              overwriteAction.edit(row.row);
            } else {
              this.edit(row.row.id);
            }
          },
        });
      }
      if (powerRef.value.delete) {
        actions.push({
          label: '删除',
          color: 'error',
          popConfirm: {
            title: '是否确认删除',
            confirm: () => {
              if (overwriteAction && overwriteAction.delete) {
                overwriteAction.delete(row.row.id);
              } else {
                this.deleted(row.row.id);
              }
              // tableRef.value?.remove(record);
            },
          },
        });
      }
      extraAction.actions(row).forEach((action) => {
        actions.push(action);
      });
      return <TableAction outside actions={actions} />;
    }
    return {
      tableRef,
      add,
      onChange: (val) => {
        linkTreeVal.value = val;
        refresh();
      },
      //createActions,
      treeColumns,
      columns,
      querys,
      toolbarConfig,
      search,
      registerDrawer,
      openDrawer,
      closeDrawer,
      registerForm,
      modalTitle,
      showFooter,
      handleSubmit,
      setDrawerProps,
      getDetail,
      edit,
      deleted,
      handleCancel,
      defaultSpaceRows,
      uploadModalRef,
      tabs,
      tabValues,
      powerRef,
      getActions,
      generateTab,
      drawerWidth,
      updateForm,
      treeToolbarConfig,
      treeData,
    };
  },
  render() {
    return (
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <Card>
          <LeftTree className={this.className} onChange={this.onChange}></LeftTree>
        </Card>
        <Card>
          <VxeBasicTable
            style={{ marginLeft: '1%' }}
            ref="tableRef"
            columns={this.columns}
            formConfig={{
              enabled: true,
              items: this.querys,
            }}
            toolbarConfig={this.toolbarConfig}
            columnConfig={{ isCurrent: false, isHover: false }}
            rowConfig={{ isCurrent: false, isHover: false }}
            proxyConfig={{
              ajax: {
                query: async ({ page, form }) => {
                  return this.search(page, form);
                },
              },
            }}
            // checked 为控制勾选的变量；
            // defaultSpaceRows 为默认需要勾选的行；
            v-slots={{
              action: (row) => {
                return this.getActions(row);
              },
              upload: (row, key) => {
                return (
                  <div>
                    <Tooltip placement={'bottom'}>
                      <a-button
                        onClick={() => {
                          const files = row.row[key] ? row.row[key].split('|') : [];
                          this.uploadModalRef.setFiles(files);
                          openPreviewModal();
                        }}
                      >
                        <Icon icon="bi:eye" />
                      </a-button>
                    </Tooltip>
                  </div>
                );
              },
              tags: (row, key) => {
                return (
                  <div>
                    {row.row[key]
                      ? row.row[key].split(row.column.slots.joinSeparator).map((item) => {
                          return <Tag>{item}</Tag>;
                        })
                      : ''}
                  </div>
                );
              },
            }}
          ></VxeBasicTable>
        </Card>
        <BasicDrawer
          onRegister={this.registerDrawer}
          showFooter={this.showFooter}
          title={this.modalTitle}
          width={this.drawerWidth}
          onOk={this.handleSubmit}
          onClose={this.handleCancel}
        >
          <BasicForm
            onRegister={this.registerForm}
            v-slots={{
              formFooter: () => {
                if (!checkEmpty(this.tabs)) {
                  console.log(this.tabs);
                  return (
                    <Col span={24}>
                      <Tabs>
                        {this.tabs.map((item) => {
                          console.log('tab', item);
                          return (
                            <TabPane key={item.key} tab={item.title} forceRender={true}>
                              {this.generateTab(item)}
                            </TabPane>
                          );
                        })}
                      </Tabs>
                    </Col>
                  );
                } else {
                  return '';
                }
              },
            }}
          ></BasicForm>
        </BasicDrawer>
        <EruptUploadPreviewModal readOnly ref="uploadModalRef" onRegister={registerPreviewModal} />
      </div>
    );
  },
});
