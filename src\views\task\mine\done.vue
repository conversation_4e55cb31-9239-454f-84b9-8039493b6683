<template>
  <div style="height: 900px">
    <VxeBasicTable ref="tableRef" v-bind="gridOptions">
      <template #action="{ row }">
        <TableAction outside :actions="createActions(row)" />
      </template>
    </VxeBasicTable>
    <BasicModal
      @register="register"
      :showFooter="true"
      defaultFullscreen
      title="详情"
      @close="handleCancel"
    >
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="详细信息">
          <Description
            class="mt-4"
            layout="vertical"
            :column="4"
            :data="descData"
            :schema="lookSchema"
          />
        </a-collapse-panel>
        <a-collapse-panel key="2" header="审批历史">
          <a-steps status="finish" direction="vertical" size="small" v-for="step in steps">
            <a-step :title="step.name">
              <template #description>
                <div>意见：{{ step.opinion }}</div>
                <div>办理人：{{ step.person }}</div>
                <p>办理时间：{{ step.createDateTime }}</p>
              </template>
            </a-step>
          </a-steps>
        </a-collapse-panel>
      </a-collapse>
      <EruptUploadPreviewModal readOnly ref="uploadModalRef" @register="registerPreviewModal" />
      <template #footer> </template>
    </BasicModal>
  </div>
</template>

<script lang="tsx">
  import {
    defineComponent,
    PropType,
    ref,
    onMounted,
    watchEffect,
    computed,
    unref,
    watch,
    h,
  } from 'vue';
  import { VXETable } from 'vxe-table';
  import BpmnViewer from '/@/views/process/bpmn/index1.vue';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import {
    getApprovalRecords,
    getClassName,
    getDoneListByUser,
    getProcessComponent,
    getProcessOne,
  } from '/@/api/process/process';
  import { ActionItem, TableAction } from '/@/components/Table';
  import { BasicTableProps, VxeBasicTable } from '/@/components/VxeTable';
  import {
    Tag,
    Tooltip,
    Steps,
    Step,
    Tabs,
    TabPane,
    Card,
    Collapse,
    CollapsePanel,
  } from 'ant-design-vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { done, task } from '/@/views/process/definition/processDefData';
  import { useForm } from '/@/components/Form';

  import { Description } from '/@/components/Description';
  import BasicUpload from '/@/components/Upload/src/BasicUpload.vue';
  import EruptUploadPreviewModal from '/@/components/Form/src/components/EruptUploadPreviewModal.vue';
  import { buildApi, getDetailOne } from '/@/api/erupt/erupt';
  import Icon from '/@/components/Icon';

  export default defineComponent({
    components: {
      EruptUploadPreviewModal,
      BasicUpload,
      Description,
      Tooltip,
      BpmnViewer,
      Tag,
      Tabs,
      TabPane,
      [Steps.name]: Steps,
      [Step.name]: Step,
      [Card.name]: Card,
      [Collapse.name]: Collapse,
      [CollapsePanel.name]: CollapsePanel,
      BasicModal,
      VxeBasicTable,
      TableAction,
      BasicForm,
      BasicDrawer,
      VXETable,
      Icon,
    },
    setup(props, { attrs, emit }) {
      const lookSchema = ref([]);
      const taskSchema = ref([]);
      const [register, { openModal, closeModal }] = useModal();
      const activeKey = ref('1');
      const acts = ref([]);
      const cName = ref('');
      const step = ref('');
      const steps = ref([]);
      const actionStep = ref('');
      const descData = ref([]);
      const current = ref(0);
      const uploadModalRef = ref();
      const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();
      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        schemas: taskSchema,
        showActionButtonGroup: false,
      });
      const gridOptions = ref<BasicTableProps>({
        id: 'VxeTable',
        keepSource: true,
        editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
        rowConfig: { isHover: true, useKey: true, isCurrent: false },
        columnConfig: { isHover: true, isCurrent: false },
        formConfig: {
          enabled: true,
          items: [
            {
              field: 'defName',
              title: '流程名称',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'name',
              title: '当前步骤',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'description',
              title: '描述',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'endTimes',
              title: '完成时间',
              itemRender: {
                name: 'ARangePicker',
                props: {
                  showTime: false, // 设置为 false 只显示年月日
                  valueFormat: 'YYYY-MM-DD', // 设置日期格式
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              span: 24,
              align: 'center',
              className: '!pr-0',
              collapseNode: true,
              itemRender: {
                name: 'AButtonGroup',
                children: [
                  {
                    props: {
                      type: 'primary',
                      content: '查询',
                      htmlType: 'submit',
                    },
                    attrs: {
                      class: 'mr-2',
                    },
                  },
                  {
                    props: {
                      type: 'default',
                      htmlType: 'reset',
                      content: '重置',
                    },
                  },
                ],
              },
            },
          ],
        },
        columns: done,
        toolbarConfig: {
          refresh: true, // 显示刷新按钮
          import: false, // 显示导入按钮
          export: false, // 显示导出按钮
          print: false, // 显示打印按钮
          zoom: false, // 显示全屏按钮
          custom: true,
        },
        height: 'auto',
        proxyConfig: {
          ajax: {
            query: async ({ page, form }) => {
              const bpmns = await getDoneListByUser(page, form);
              return bpmns;
            },
            queryAll: async ({ form }) => {
              const bpmns = await getDoneListByUser(form);
              let rows = [];
              bpmns.forEach((bpmn) => {
                rows.push({ name: bpmn.split('.bpmn')[0] });
              });
              return rows;
            },
          },
        },
      });

      interface RowVO {
        name: string;
      }

      const tableData = ref<Array<RowVO>>([]);

      return {
        tableData,
        gridOptions,
        register,
        openModal,
        closeModal,
        registerForm,
        descData,
        activeKey,
        acts,
        lookSchema,
        taskSchema,
        steps,
        actionStep,
        setFieldsValue,
        uploadModalRef,
        current,
        cancel: () => {
          actionStep.value = '';
          closeModal();
        },

        registerPreviewModal,
        openPreviewModal,
        handleSubmit: () => {},
        handleCancel: () => {},
        createActions: (record) => {
          const actions: ActionItem[] = [
            {
              label: '详情',
              onClick: async () => {
                buildApi(record.className).then(async (res) => {
                  const {
                    eruptModel: { eruptFieldModels, eruptJson },
                  } = res;

                  let details = [];
                  eruptFieldModels.forEach((item) => {
                    const key = item.fieldName;
                    const title = item.eruptFieldJson.edit.title;

                    if (item.eruptFieldJson.views.length > 0) {
                      item.eruptFieldJson.views.forEach((v) => {
                        if (v.show) {
                          const k = v.column ? key + '_' + v.column : key;
                          const d = {
                            field: k,
                            label: v.title,
                          };
                          details.push(d);
                        }
                      });
                    } else {
                      if (item.eruptFieldJson.edit.show.detail_show && key !== 'id') {
                        if (item.eruptFieldJson.edit.type == 'ATTACHMENT') {
                          const d = {
                            field: key,
                            label: title,
                            render: (val) => {
                              return (
                                <div>
                                  <Tooltip placement={'bottom'}>
                                    <a-button
                                      onClick={() => {
                                        const files = val ? val.split('|') : [];
                                        uploadModalRef.value.setFiles(files);
                                        openPreviewModal();
                                      }}
                                    >
                                      <Icon icon={'bi:eye'} />
                                    </a-button>
                                  </Tooltip>
                                </div>
                              );
                            },
                          };
                          details.push(d);
                        } else {
                          const d = {
                            field: key,
                            label: title,
                          };
                          details.push(d);
                        }
                      }
                    }
                  });
                  lookSchema.value = details;

                  const entity = await getDetailOne(record.className, record.bussinessId);
                  descData.value = entity;

                  //gridOptions.columns = columns
                  //search()
                });
                const records = await getApprovalRecords(record.processInstanceId);
                steps.value = records;
                current.value = records.length;
                openModal();
              },
            },
          ];

          return actions;
        },
      };
    },
  });
</script>
<style scoped>
  .ant-select-disabled {
    color: #303030;
    background: #fff;
    cursor: not-allowed;
  }

  .attachment-container {
    display: flex;
    align-items: center;
  }

  .attachment-container span {
    margin-left: 60px; /* 增加间距 */
    margin-right: 10px; /* 增加间距 */
  }
</style>
