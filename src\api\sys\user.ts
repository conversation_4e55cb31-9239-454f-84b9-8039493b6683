import { defHttp } from '/@/utils/http/axios';
import {
  LoginParams,
  CasLoginParams,
  LoginResultModel,
  GetUserInfoModel,
  ForgetParams,
  ForgetGetResultModel,
} from './model/userModel';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  Login = '/login',
  Logout = '/logout',
  CasLogin = '/cas/auth',
  GetUserInfo = '/getUserInfo',
  GetPermCode = '/getPermCode',
  savePermCode = '/savePermCode',
  TestRetry = '/testRetry',
  SendSms = '/sendSms',
  ResetPassword = '/resetPassword',
  ModifyPassword = '/modifyPassword',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export function casLoginApi(params: CasLoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>({ url: Api.CasLogin, params }, { errorMessageMode: mode });
}

/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.post<GetUserInfoModel>({ url: Api.GetUserInfo }, { errorMessageMode: 'none' });
}

export function getPermCode() {
  return defHttp.post<string[]>({ url: Api.GetPermCode });
}

export function savePermCode(id, params) {
  return defHttp.post({ url: Api.savePermCode + `/${id}`, params });
}

export function getPermCodeById(id) {
  return defHttp.post<string[]>({ url: Api.GetPermCode + `/${id}` });
}

export function doLogout() {
  return defHttp.post({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.post(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    },
  );
}

export const getSendSms = (params?: ForgetParams) => {
  return defHttp.post({ url: Api.SendSms, params });
};

export const getResetPassword = (params?: ForgetParams) =>
  defHttp.post({ url: Api.ResetPassword, params });

export const modifyPassword = (params?: ForgetParams) =>
  defHttp.post({ url: Api.ModifyPassword, params });
