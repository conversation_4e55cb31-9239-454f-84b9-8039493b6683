<template>
  <div>
    <erupt-table class-name="TransferTemplate" 
    :overwrite-action="overwriteAction"></erupt-table>
  </div>
</template>
<script lang="ts">
  import { NColorPicker } from 'naive-ui';
import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router';
import eruptTable from '../../../../components/EruptTable/eruptTable';
import { remove } from '/@/api/erupt/erupt'; // 导入原删除接口
import BasicForm from '/@/components/Form/src/BasicForm.vue';
import { useModal } from '/@/components/Modal';
import BasicModal from '/@/components/Modal/src/BasicModal.vue';
import { useMessage } from '/@/hooks/web/useMessage';
  const { createConfirm } = useMessage();

  export default defineComponent({
    name: 'TransferTemplate',
    components: { BasicModal, BasicForm, eruptTable, NColorPicker },
    setup() {
      const router = useRouter();
      const [register, { openModal, closeModal }] = useModal();
      const schema = ref([
        {
          field: 'file',
          label: '附件',
          required: true,
          component: 'Attachment',
          colProps: { span: 12 },
        },
      ]);
      const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
      const { createMessage } = useMessage();
      function refresh() {
            document.querySelector('button[content="查询"]').click()
        }

      const overwriteAction = ({
    detail: (row: any) => {
      router.push({
                    path: '/newLine/transfer/detail',
                    query: {
                      tplId: row.id,
                    },
                  });
      setTimeout(() => {
            openModal();
          }, 200);
    },
    delete: (row: any) => {
    createConfirm({
    content: '请确认是否删除移交接管项目（删除后移交接管详情相关内容同步删除）',
    onOk: async () => {
      try {
        debugger;
        const { status } = await remove('TransferTemplate', row);
        debugger;
        if (status === 'SUCCESS') {
          createMessage.success('删除成功');
          close();
          refresh();
        }
      } catch (error) {
        createMessage.error('删除失败');
      }
    },
    onCancel: () => {
    },
    iconType: 'warning',
  });
},
  });

      return {
        overwriteAction,
        register,
        openModal,
        closeModal,
        register1,
        openModal1,
        closeModal1,
        handleCancel: () => {},
      };
    },
  });
</script>
