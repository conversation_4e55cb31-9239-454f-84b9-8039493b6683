<template>
  <Card title="汇编管理状态及类型完成率" :loading="loading">
    <template #extra>
      <a-space direction="horizontal">
        <a-select
          v-model="line"
          style="width: 200px"
          placeholder="请选择线路"
          :options="lineOptions"
          @change="setLine"
          :loading="loading"
          :disabled="loading"
          allowClear
        ></a-select>
        <a-select
          v-if="userStore.getUserInfo.dept === '新线管理部'"
          v-model="dept"
          style="width: 200px"
          placeholder="请选择部门"
          :options="deptOptions"
          @change="setDept"
          :loading="loading"
          :disabled="loading"
          allowClear
        ></a-select>
      </a-space>
    </template>
    <!-- 柱状图容器 -->
    <div v-loading="chartLoading" ref="chartRef" :style="{ width: '100%', height: '400px' }"></div>
    <!-- 数据表格 -->
    <div v-loading="tableLoading" style="margin-top: 20px">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        bordered
        class="custom-striped-table"
      ></a-table>
    </div>
  </Card>
</template>

<script setup lang="ts">
  import { Card } from 'ant-design-vue';
  import { onMounted, Ref, ref, watch } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { useUserStore } from '/@/store/modules/user';
  import { getAssembly1 } from '/@/views/demo/statistics/api/statistics';
  import { getAssembly } from '/@/api/demo/home';
  import { useRouter } from 'vue-router';
  import { PropType } from 'vue';
  import { useLineStoreWithOut } from '/@/store/modules/line';
  import { useDeptStoreWithOut } from '/@/store/modules/dept';

  // 类型定义
  interface ChartData {
    state: string[];
    data: number[];
    stateMap: Record<string, number>;
  }

  interface TableData {
    dept: string;
    unresolved_open: number;
    resolved_closed: number;
    resolved_feedback_closed: number;
    completed: number;
    other_closed: number;
    closure: number;
    not_applicable: number;
    pending_operation: number;
    total_clauses: number;
    completion_rate: string;
  }

  interface SelectOption {
    label: string;
    value: string | number;
  }

  // 组件属性
  const props = defineProps({
    loading: Boolean,
  });

  // 响应式状态
  const line = ref('');
  const dept = ref('');
  const deptLabel = ref('');
  const lineOptions = ref<SelectOption[]>([]);
  const deptOptions = ref<SelectOption[]>([]);
  const loading = ref(true);
  const chartLoading = ref(true);
  const tableLoading = ref(true);
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const userStore = useUserStore();
  const router = useRouter();
  const lineStore = useLineStoreWithOut();
  const deptStore = useDeptStoreWithOut();

  // 表格列定义（已添加已完成列）
  const columns = [
    {
      title: '部门',
      dataIndex: 'dept',
      key: 'dept',
      width: 150,
    },
    {
      title: '已落实-闭口',
      dataIndex: 'resolved_closed',
      key: 'resolved_closed',
      width: 150,
    },
    {
      title: '未落实-开口',
      dataIndex: 'unresolved_open',
      key: 'unresolved_open',
      width: 150,
    },
    {
      title: '已反馈-闭口',
      dataIndex: 'resolved_feedback_closed',
      key: 'resolved_feedback_closed',
      width: 150,
    },

    {
      title: '其他-闭口',
      dataIndex: 'other_closed',
      key: 'other_closed',
      width: 150,
    },
    {
      title: '本条线路不适用',
      dataIndex: 'not_applicable',
      key: 'not_applicable',
      width: 150,
    },
    {
      title: '待操作',
      dataIndex: 'pending_operation',
      key: 'pending_operation',
      width: 150,
    },
    {
      title: '闭口',
      dataIndex: 'closure',
      key: 'closure',
      width: 150,
    },
    {
      title: '已完成', // 新增列
      dataIndex: 'completed',
      key: 'completed',
      width: 150,
    },
    {
      title: '条款总数',
      dataIndex: 'total_clauses',
      key: 'total_clauses',
      width: 150,
    },
    {
      title: '本期落实率',
      dataIndex: 'completion_rate',
      key: 'completion_rate',
      width: 150,
    },
  ];

  const tableData = ref<TableData[]>([]);

  // 处理筛选条件变化
  const handleFilterChange = () => {
    chartLoading.value = true;
    tableLoading.value = true;
    fetchChartData();
    fetchTableData();
  };

  // 获取柱状图数据
  const fetchChartData = async () => {
    try {
      const params = {
        line: line.value,
        dept: dept.value,
      };
      const res = await getAssembly(params);
      if (res && res.state && res.data && res.stateMap) {
        updateChart(res);
      } else {
        console.error('柱状图数据格式不正确:', res);
      }
    } catch (error) {
      console.error('柱状图数据获取失败:', error);
    } finally {
      chartLoading.value = false;
    }
  };

  // 获取表格数据
  const fetchTableData = async () => {
    try {
      const params = {
        line: line.value,
        dept: dept.value,
      };
      const res = await getAssembly1(params);
      if (res && res.data) {
        updateTable(res);
      } else {
        console.error('表格数据格式不正确:', res);
      }
    } catch (error) {
      console.error('表格数据获取失败:', error);
    } finally {
      tableLoading.value = false;
    }
  };

  // 初始化数据
  /*const initData = async () => {
  await  Promise.all([deptStore.getDeptOptions(), lineStore.getLineOptions()]).then(
    ([deptRes, lineRes]) => {
      deptOptions.value = deptRes;
      lineOptions.value = lineRes;
      loading.value = false;
    },
  );
  fetchChartData();
  fetchTableData();
};*/

  // 更新柱状图
  function updateChart(value: ChartData) {
    setOptions({
      tooltip: {},
      xAxis: {
        type: 'category',
        data: value.state,
        name: '状态',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#000',
          fontSize: 14,
          padding: [10, 0, 0, 0],
        },
        axisLabel: {
          formatter: function (value) {
            return value.length > 8 ? value.slice(0, 8) + '...' : value;
          },
        },
      },
      yAxis: {
        name: '数量',
        nameLocation: 'end',
        nameGap: 21,
        nameTextStyle: {
          color: '#333',
          fontSize: 14,
          fontWeight: 'bold',
          backgroundColor: '#f8f8f8', // 添加背景色
          borderColor: '#d9d9d9', // 边框颜色
          borderWidth: 1, // 边框宽度
          borderRadius: 4, // 圆角边框
          padding: [4, 8], // 内边距
        },
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
      },
      grid: {
        left: '10%',
        right: '12%',
        top: '10%',
        bottom: '10%',
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          data: value.data || [0, 0, 0, 0, 0, 0, 0],
          barWidth: '30%',
          itemStyle: {
            color: function (params) {
              const colorList = [
                '#ff7a45',
                '#6A4C93',
                '#F7B731',
                '#3867D6',
                '#34C759',
                '#FF9F1C',
                '#A3CB38',
                '#FF5252',
              ];
              return colorList[params.dataIndex % colorList.length];
            },
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c}',
            color: '#000',
            fontSize: 14,
          },
        },
      ],
      graphic: [
        {
          type: 'group',
          right: 20,
          top: 25,
          children: value.state
            .map((state, index) => {
              const colorList = [
                '#ff7a45',
                '#6A4C93',
                '#F7B731',
                '#3867D6',
                '#34C759',
                '#FF9F1C',
                '#A3CB38',
              ];
              const color = colorList[index % colorList.length];
              return [
                {
                  type: 'text',
                  style: {
                    text: state,
                    fill: '#000000D9',
                    fontSize: 12,
                    textAlign: 'left',
                  },
                  top: `${index * 20}px`,
                  right: 45,
                  onclick: () => {
                    router.push({
                      path: '/newLine/assembly/details',
                      query: {
                        line: line.value,
                        dept: deptLabel.value,
                        state: value.stateMap[state],
                      },
                    });
                  },
                },
                {
                  type: 'rect',
                  shape: {
                    width: 25,
                    height: 14,
                    r: 5,
                  },
                  style: {
                    fill: color,
                  },
                  top: `${index * 20}px`,
                  right: 19,
                  onclick: () => {
                    router.push({
                      path: '/newLine/assembly/details',
                      query: {
                        line: line.value,
                        dept: deptLabel.value,
                        state: value.stateMap[state],
                      },
                    });
                  },
                },
              ];
            })
            .flat(),
        },
      ],
    });
  }

  // 更新表格数据
  function updateTable(value: { data: Record<string, any> }) {
    const data = value?.data || {};
    const transformedData: TableData[] = [];
    let totals = {
      resolved_closed: 0,
      unresolved_open: 0,
      resolved_feedback_closed: 0,
      completed: 0,
      other_closed: 0,
      closure: 0,
      not_applicable: 0,
      pending_operation: 0,
      total_clauses: 0, // 这个总数现在不包含已完成数
    };

    for (const [deptName, states] of Object.entries(data)) {
      if (!states || typeof states !== 'object') continue;

      const stateValues = {
        '1': Number(states['1'] || 0),
        '2': Number(states['2'] || 0),
        '3': Number(states['3'] || 0),
        '4': Number(states['4'] || 0),
        '5': Number(states['5'] || 0),
        '6': Number(states['6'] || 0),
        '7': Number(states['7'] || 0),
        completed: Number(states['已完成'] || 0),
      };

      // 计算总数时排除已完成数
      const totalClauses = Object.values({
        '1': stateValues['1'],
        '2': stateValues['2'],
        '3': stateValues['3'],
        '4': stateValues['4'],
        '5': stateValues['5'],
        '6': stateValues['6'],
        '7': stateValues['7'],
      }).reduce((sum, count) => sum + count, 0);

      // 计算落实率时使用不包含已完成数的总数
      const completionRate =
        totalClauses > 0 ? `${((stateValues.completed / totalClauses) * 100).toFixed(2)}%` : '0%';

      // 更新合计（总数累加时不包含已完成数）
      totals.resolved_closed += stateValues['1'];
      totals.unresolved_open += stateValues['2'];
      totals.resolved_feedback_closed += stateValues['3'];
      totals.completed += stateValues.completed;
      totals.other_closed += stateValues['4'];
      totals.closure += stateValues['7'];
      totals.not_applicable += stateValues['5'];
      totals.pending_operation += stateValues['6'];
      totals.total_clauses += totalClauses; // 这里累加的是不包含已完成数的总数

      transformedData.push({
        dept: deptName,
        resolved_closed: stateValues['1'],
        unresolved_open: stateValues['2'],
        resolved_feedback_closed: stateValues['3'],
        completed: stateValues.completed,
        other_closed: stateValues['4'],
        closure: stateValues['7'],
        not_applicable: stateValues['5'],
        pending_operation: stateValues['6'],
        total_clauses: totalClauses, // 存储不包含已完成数的总数
        completion_rate: completionRate,
      });
    }

    // 添加合计行
    if (transformedData.length > 0) {
      const totalCompletionRate =
        totals.total_clauses > 0
          ? `${((totals.completed / totals.total_clauses) * 100).toFixed(2)}%`
          : '0%';

      transformedData.push({
        dept: '合计',
        resolved_closed: totals.resolved_closed,
        unresolved_open: totals.unresolved_open,
        resolved_feedback_closed: totals.resolved_feedback_closed,
        completed: totals.completed,
        other_closed: totals.other_closed,
        closure: totals.closure,
        not_applicable: totals.not_applicable,
        pending_operation: totals.pending_operation,
        total_clauses: totals.total_clauses, // 显示不包含已完成数的总合计
        completion_rate: totalCompletionRate,
      });
    }

    tableData.value = transformedData;
  }
  // 设置线路
  const setLine = (val) => {
    line.value = val;
    handleFilterChange();
  };

  // 设置部门
  const setDept = (val) => {
    const selectedOption = deptOptions.value.find((option) => option.value === val);
    if (selectedOption) {
      deptLabel.value = selectedOption.label;
    }
    dept.value = val;
    handleFilterChange();
  };

  // 初始化加载数据
  onMounted(() => {
    Promise.all([deptStore.getDeptOptions(), lineStore.getLineOptions()]).then(
      ([deptRes, lineRes]) => {
        deptOptions.value = deptRes;
        lineOptions.value = lineRes;
        loading.value = false;
      },
    );
    fetchChartData();
    fetchTableData();
  });
</script>
