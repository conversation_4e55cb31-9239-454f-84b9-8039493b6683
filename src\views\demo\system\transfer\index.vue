<template>
  <erupt-table
    title="移交管理"
    seq="true"
    ref="eruptTableRef"
    class-name="Transfer"
    :pre-conditions="preCondition"
    :query-sort="querySort"
    >
  <template #state="{ row }">
        <div v-if="row.state == '未完成'" style="align-self: center">
          <a-tag color="red" style="width: 60px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
        <div v-if="row.state == '已完成'" style="align-self: center">
          <a-tag color="green" style="width: 60px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
        <div v-if="row.state == '已超时'" style="align-self: center">
          <a-tag color="yellow" style="width: 60px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
      </template>
      </erupt-table>
</template>

<script lang="ts">
import eruptTable from '/@/components/EruptTable/eruptTable';
import { ref, computed, defineComponent, onMounted } from 'vue';
import { Button, Popconfirm } from 'ant-design-vue';
import { save, update } from '/@/api/erupt/erupt';
import { useMessage } from '/@/hooks/web/useMessage';
import { useRouter, useRoute } from 'vue-router';

export default defineComponent({ 
  name: 'Transfer',
  components: {
    eruptTable
  },
  setup() {
    const eruptTableRef = ref();
    const { createMessage } = useMessage();
    const route = useRoute();
    const router = useRouter();
    const preCondition = new Map<string, any>(); // 明确类型
    const tplId = computed(() => route.query.tplId);

    onMounted(() => {
      if (tplId.value) {
        preCondition.set('template', tplId.value);
      }
    });

    const querySort = (querys: any[]) => {
      for (let i = 0; i < querys.length; i++) {
        querys[i].span = 8;
        if (i >= 3) {
          querys[i].folding = true;
        }
      }
      console.log('afterSort', querys);
      return querys;
    };

    return {
      eruptTableRef,
      preCondition,
      querySort
    };
  }
});
</script>