import {
  BannerItemPageParams,
  BannerItemInfo,
  ExternalLinkPageParams,
  ExternalLinkInfo,
  NewsPageParams,
  NewsInfo,
  NewsCategoryPageParams,
  NewsCategoryInfo,
  FileCategoryPageParams,
  FileCategoryInfo,
  FileAttachmentPageParams,
  FileAttachmentInfo,
  ExternalLinkListGetResultModel,
  BannerItemListGetResultModel,
  NewsListGetResultModel,
  NewsCategoryListGetResultModel,
  FileCategoryListGetResultModel,
  FileAttachmentListGetResultModel,
} from './model/frontModel';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  BannerItemPageList = '/system/getBannerItemPageList',
  BannerItemSaveOrUpdate = '/system/banneritem/saveOrUpdate',
  BannerItemDelete = '/system/banneritem/delete',
  ExternalLinkPageList = '/system/getExternalLinkPageList',
  ExternalLinkSaveOrUpdate = '/system/externallink/saveOrUpdate',
  ExternalLinkDelete = '/system/externallink/delete',
  NewsPageList = '/system/getNewsPageList',
  NewsSaveOrUpdate = '/system/news/saveOrUpdate',
  NewsDelete = '/system/news/delete',
  NewsCategoryPageList = '/system/getNewsCategoryPageList',
  NewsCategorySaveOrUpdate = '/system/newscategory/saveOrUpdate',
  NewsCategoryDelete = '/system/newscategory/delete',
  FileCategoryPageList = '/system/getFileCategoryPageList',
  FileCategorySaveOrUpdate = '/system/filecategory/saveOrUpdate',
  FileCategoryDelete = '/system/filecategory/delete',
  FileAttachmentPageList = '/system/getFileAttachmentPageList',
  FileAttachmentSaveOrUpdate = '/system/fileattachment/saveOrUpdate',
  FileAttachmentDelete = '/system/fileattachment/delete',
}

export const getBannerItemListByPage = (params: BannerItemPageParams) =>
  defHttp.post<BannerItemListGetResultModel>({ url: Api.BannerItemPageList, params });

export const bannerItemSaveOrUpdate = (params?: BannerItemInfo) =>
  defHttp.post({ url: Api.BannerItemSaveOrUpdate, params });

export const bannerItemDeleteByIds = (params?: number) =>
  defHttp.post({ url: Api.BannerItemDelete + '/', params });

export const getExternalLinkListByPage = (params?: ExternalLinkPageParams) =>
  defHttp.post<ExternalLinkListGetResultModel>({ url: Api.ExternalLinkPageList, params });

export const externallinkSaveOrUpdate = (params?: ExternalLinkInfo) =>
  defHttp.post({ url: Api.ExternalLinkSaveOrUpdate, params });

export const externallinkDeleteByIds = (params?: number) =>
  defHttp.post({ url: Api.ExternalLinkDelete + '/', params });

export const getNewsPageListByPage = (params?: NewsPageParams) =>
  defHttp.post<NewsListGetResultModel>({ url: Api.NewsPageList, params });

export const newsSaveOrUpdate = (params?: NewsInfo) =>
  defHttp.post({ url: Api.NewsSaveOrUpdate, params });

export const newsDeleteByIds = (params?: number) =>
  defHttp.post({ url: Api.NewsDelete + '/', params });

export const getNewsCategoryListByPage = (params?: NewsCategoryPageParams) =>
  defHttp.post<NewsCategoryListGetResultModel>({ url: Api.NewsCategoryPageList, params });

export const newsCategorySaveOrUpdate = (params?: NewsCategoryInfo) =>
  defHttp.post({ url: Api.NewsCategorySaveOrUpdate, params });

export const newsCategoryDeleteByIds = (params?: number) =>
  defHttp.post({ url: Api.NewsCategoryDelete + '/', params });

export const getFileCategoryListByPage = (params?: FileCategoryPageParams) =>
  defHttp.post<FileCategoryListGetResultModel>({ url: Api.FileCategoryPageList, params });

export const fileCategorySaveOrUpdate = (params?: FileCategoryInfo) =>
  defHttp.post({ url: Api.FileCategorySaveOrUpdate, params });

export const fileCategoryDeleteByIds = (params?: number) =>
  defHttp.post({ url: Api.FileCategoryDelete + '/', params });

export const getFileAttachmentListByPage = (params?: FileAttachmentPageParams) =>
  defHttp.post<FileAttachmentListGetResultModel>({ url: Api.FileAttachmentPageList, params });

export const fileAttachmentSaveOrUpdate = (params?: FileAttachmentInfo) =>
  defHttp.post({ url: Api.FileAttachmentSaveOrUpdate, params });

export const fileAttachmentDeleteByIds = (params?: number) =>
  defHttp.post({ url: Api.FileAttachmentDelete + '/', params });
