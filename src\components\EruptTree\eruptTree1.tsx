import {defineComponent, onMounted} from 'vue';
import {ref} from 'vue';
import { VxeBasicTable, VxeGridInstance} from "/@/components/VxeTable";
import {TableAction} from "/@/components/Table";

const [registerPreviewModal, {openModal: openPreviewModal}] = useModal();
import {useMessage} from "/@/hooks/web/useMessage";
import {BasicForm, useForm} from '/@/components/Form/index';
import {BasicDrawer, useDrawer} from '/@/components/Drawer';
import {
    buildApi,
    remove,
    save, getOne, update, treeQueryApi
} from "/@/api/erupt/erupt";
import TableAdd from "/@/components/Form/src/components/TableAdd";
import TableSelectAdd from "/@/components/Form/src/components/TableSelectAdd";
import TreeSeletAdd from "/@/components/Form/src/components/TreeSeletAdd";
import {
    checkEmpty,
    getColFomatter,
    getComponent,
    getComponentProps, getSearchComponent,
} from "/@/components/EruptTable/componets";
import {Modal, Tooltip, Tabs, TabPane, Col, Tag} from "ant-design-vue";
import EruptUploadPreviewModal from "/@/components/Form/src/components/EruptUploadPreviewModal.vue";
import {Icon} from "/@/components/Icon";
import {useModal} from "/@/components/Modal";

export default defineComponent({
    name: 'eruptTable',
    props: {className: {}, overwriteAction: {add: {}, edit: {}, detail: {}, delete: {}, batchDelete: {}}, formDynamicControl: {type: Object, default: {}}},
    setup(props, {emit, attrs}) {
        const {className, overwriteAction, formDynamicControl} = props
        const tabs = ref([])
        const tableRef = ref<VxeGridInstance>();
        const powerRef = ref({})
        const tabValues = ref({})
        const uploadModalRef = ref()
        const columns = ref([]);
        const modalTitle = ref('')
        const showFooter = ref(true)
        const base = ref({id: '', version: ''})
        const [registerDrawer, {openDrawer, closeDrawer, setDrawerProps}] = useDrawer();
        const defaultSpaceRows = ref([])
        const addSchema = ref([])
        const editSchema = ref([])
        const lookSchema = ref([])

        const tableData = ref([])

        onMounted(() => {
            search()
        })
        const [registerForm, {resetFields, setFieldsValue, updateSchema, validate}] = useForm({
            labelWidth: 100,
            schemas: editSchema,
            showActionButtonGroup: false,
        });
        const toolbarConfig = ref({
            buttons: [],
        })
        let querys = ref([]);
        const {createMessage} = useMessage();

        function refresh() {
            search()
        }

        function handleCancel() {
            resetFields()
        }

        function search() {
            treeQueryApi(className).then(res => {
                tableData.value = res
            })
        }

        async function handleSubmit() {
            try {
                let values = await validate();

                setDrawerProps({confirmLoading: true});
                if (base.value.id) {
                    const data = {...values, ...base.value, ...tabValues.value}
                    const {status, message} = await update(className, data, null)
                    if (status == 'SUCCESS') {
                        createMessage.success('保存成功')
                        resetFields
                        refresh()
                    } else {
                        createMessage.error('保存失败:' + message)
                    }
                } else {
                    const data = {...values, ...tabValues.value}
                    const {status, message} = await save(className, data, null)
                    if (status == 'SUCCESS') {
                        createMessage.success('保存成功')
                        resetFields
                        refresh()
                    } else {
                        createMessage.error('保存失败:' + message)
                    }
                }
                // TODO custom api


                closeDrawer();
                emit('success');
            } finally {
                setDrawerProps({confirmLoading: false});
            }
        }


        async function getDetail(id) {
            modalTitle.value = '详情'
            showFooter.value = false
            openDrawer()
            await getOne(className, id).then(res => {
                updateSchema(lookSchema.value)
                setFieldsValue(res)
                let tabV = {}
                for (let key in res) {
                    tabs.value.forEach(item => {
                        if (item.key == key) {
                            tabV[key] = res[key]
                        }
                    })
                }
                tabValues.value = tabV
            })

        }

        async function edit(id) {
            modalTitle.value = '编辑'
            showFooter.value = true
            openDrawer()
            const res = await getOne(className, id)
            base.value.id = res.id
            base.value.version = res.version
            updateSchema(editSchema.value)
            setFieldsValue(res)
            let tabV = {}
            for (let key in res) {
                tabs.value.forEach(item => {
                    if (item.key == key) {
                        tabV[key] = res[key] ? res[key] : []
                    }
                })
            }
            tabValues.value = tabV

        }

        async function deleted(id) {
            const {status, message} = await remove(className, id)
            if (status == 'SUCCESS') {
                createMessage.success('删除成功')
                refresh()
            } else {
                createMessage.error('删除失败:' + message)
            }

        }

        function generateTab(tab) {
            if (tab.type == 'TAB_TABLE_ADD') return <TableAdd readOnly={(modalTitle.value == '详情')} onChange={(v) => {
                tabValues.value[tab.key] = v
            }} value={tabValues.value[tab.key]} className={className} tabName={tab.key}
                                                              models={tab.eruptModel.eruptFieldModels}></TableAdd>
            if (tab.type == 'TAB_TABLE_REFER') return <TableSelectAdd readOnly={modalTitle.value == '详情'}
                                                                      onChange={(v) => {
                                                                          tabValues.value[tab.key] = v
                                                                      }} value={tabValues.value[tab.key]}
                                                                      className={className} tabName={tab.key}
                                                                      models={tab.eruptModel.eruptFieldModels}></TableSelectAdd>
            if (tab.type == 'TAB_TREE') return <TreeSeletAdd readOnly={modalTitle.value == '详情'} onChange={(v) => {
                tabValues.value[tab.key] = v
            }} value={tabValues.value[tab.key]} className={className} tabName={tab.key}></TreeSeletAdd>
        }


        buildApi(className).then(res => {
            const {eruptModel: {eruptFieldModels}, tabErupts, power} = res
            let cols = [{type: 'checkbox'}]
            let qs = []
            let edits = []
            let adds = []
            let details = []
            let tabItems = []
            let buttons = []
            powerRef.value = power
            if (power.add == true) {
                buttons.push({
                    content: '新增',
                    buttonRender: {
                        name: 'AButton',
                        props: {
                            type: 'primary',
                        },
                        events: {
                            click: () => {
                                if (overwriteAction && overwriteAction.add) {
                                    overwriteAction.add()
                                } else {
                                    modalTitle.value = '新增'
                                    showFooter.value = true
                                    openDrawer()
                                    base.value.id = ''
                                    base.value.version = ''
                                    updateSchema(addSchema.value)
                                    resetFields()
                                    for (let tabValuesKey in tabValues.value) {
                                        tabValues.value[tabValuesKey] = []
                                    }
                                }


                            },
                        },
                    },
                })
            }
            if (power.delete == true) buttons.push({
                content: '删除',
                buttonRender: {
                    name: 'AButton',
                    props: {
                        type: 'warning',
                    },
                    events: {
                        click: () => {
                            Modal.confirm({
                                title: '提示',
                                content: '是否确认删除',
                                okText: '确认',
                                cancelText: '取消',
                                onOk() {
                                    if (overwriteAction && overwriteAction.batchDelete) {
                                        const selectedRows = tableRef.value.getCheckboxRecords()
                                        let ids = []
                                        selectedRows.forEach(item => {
                                            ids.push(item.id)
                                        })
                                        overwriteAction.batchDelete(ids)
                                    } else {
                                        handleBatchRemove()
                                    }

                                },
                            });
                        },
                    },
                },
            },)
            let tabV = {}
            eruptFieldModels.forEach(item => {
                const key = item.fieldName
                const title = item.eruptFieldJson.edit.title
                item.eruptFieldJson.views.forEach(v => {
                    if (v.show) {
                        cols.push(getColFomatter(key, v, item.eruptFieldJson.edit))
                    }
                })
                //  formState[key] = null
                if (item.eruptFieldJson.edit.search.value) {
                    qs.push(getSearchComponent(key, title, item, 4))
                }
                if (item.eruptFieldJson.edit.show && key !== 'id') {
                    if (item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' && item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' && item.eruptFieldJson.edit.type !== 'TAB_TREE') {
                        const e = {
                            field: key,
                            label: title,
                            component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                            componentProps: (form) => {
                                return {
                                    ...getComponentProps(item, className), disabled: item.eruptFieldJson.edit.readOnly.edit, onChange: (e) => formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {
                                    }
                                }
                            },
                            required: item.eruptFieldJson.edit.notNull,
                            colProps: {
                                span: item.eruptFieldJson.edit.colSpan,
                            },
                        }
                        const a = {
                            field: key,
                            label: title,
                            component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                            componentProps: (form) => {
                                return {
                                    ...getComponentProps(item, className), disabled: item.eruptFieldJson.edit.readOnly.add, onChange: (e) => formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {
                                    }
                                }
                            },
                            required: item.eruptFieldJson.edit.notNull,
                            colProps: {
                                span: item.eruptFieldJson.edit.colSpan,
                            },
                        }
                        const d = {
                            field: key,
                            label: title,
                            component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                            componentProps: (form) => {
                                return {
                                    ...getComponentProps(item, className), disabled: true, onChange: (e) => formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {
                                    }
                                }
                            },
                            required: item.eruptFieldJson.edit.notNull,
                            colProps: {
                                span: item.eruptFieldJson.edit.colSpan,
                            },
                        }
                        edits.push(e)
                        adds.push(a)
                        details.push(d)
                    } else {
                        if (tabErupts) {
                            for (let tabEruptsKey in tabErupts) {
                                if (item.fieldName == tabEruptsKey) {
                                    tabErupts[tabEruptsKey].title = item.eruptFieldJson.edit.title
                                    tabErupts[tabEruptsKey].key = tabEruptsKey
                                    tabErupts[tabEruptsKey].type = item.eruptFieldJson.edit.type
                                    tabItems.push(tabErupts[tabEruptsKey])
                                    tabV[tabEruptsKey] = []
                                }
                            }

                        }
                    }
                }
            })
            tabs.value = tabItems
            tabValues.value = tabV
            console.log('tabs', tabs.value, tabValues.value)

            cols.push({
                width: 160,
                title: '操作',
                align: 'center',
                slots: {default: 'action'},
                fixed: 'right',
            },)
            columns.value = cols
            editSchema.value = edits
            addSchema.value = adds
            lookSchema.value = details
            toolbarConfig.value.buttons = buttons
        })

        function getActions(row) {
            let actions = [{
                label: '详情',
                onClick: () => {
                    this.getDetail(row.row.id)
                },
            }]
            if (powerRef.value.edit) {
                actions.push({
                    label: '编辑',
                    onClick: () => {
                        if (overwriteAction && overwriteAction.edit) {
                            overwriteAction.edit(row.row.id)
                        } else {
                            this.edit(row.row.id)
                        }

                    },
                })
            }
            if (powerRef.value.delete) {
                actions.push({
                    label: '删除',
                    color: 'error',
                    popConfirm: {
                        title: '是否确认删除',
                        confirm: () => {
                            if (overwriteAction && overwriteAction.delete) {
                                overwriteAction.delete(row.row.id)
                            } else {
                                this.deleted(row.row.id)
                            }

                            // tableRef.value?.remove(record);
                        },
                    },
                })
            }
            return <TableAction outside actions={actions}/>
        }

        return {
            tableRef,
            tableData,
            columns,
            querys,
            toolbarConfig,
            registerDrawer,
            openDrawer,
            closeDrawer,
            registerForm,
            modalTitle,
            showFooter,
            handleSubmit,
            setDrawerProps,
            getDetail,
            edit,
            deleted,
            handleCancel,
            defaultSpaceRows,
            uploadModalRef,
            tabs,
            tabValues,
            powerRef,
            getActions,
            generateTab
        };
    },
    render() {
    //     show-overflow
    //     ref="xTreeRef"
    //     border="inner"
    // :row-config="{isHover: true, useKey: true}"
    // :show-header="false"
    // :data="tableData"
    // :checkbox-config="{labelField: 'name'}"

        return (
            <div>
                <vxe-table ref="tableRef"
                               showOverflow={true}
                               border={"inner"}
                               toolbarConfig={this.toolbarConfig}
                               showHeader={false}
                               checkboxConfig={{labelField: 'name'}}
                               treeConfig={{transform: true,rowField: 'id', parentField: 'pid', accordion: true, line: true, iconOpen: 'vxe-icon-square-minus-fill', iconClose: 'vxe-icon-square-plus-fill'}}
                               rowConfig={{useKey: true, isHover: false}}
                               tableData={this.tableData}
                               v-slots={{
                                   action: (row) => {
                                       return this.getActions(row)
                                   },
                                   upload: (row, key) => {
                                       return (
                                           <div>
                                               <Tooltip placement={"bottom"}>
                                                   <a-button onClick={() => {
                                                       const files = row.row[key] ? row.row[key].split('|') : []
                                                       this.uploadModalRef.setFiles(files)
                                                       openPreviewModal()
                                                   }}>
                                                       <Icon icon="bi:eye"/>
                                                   </a-button>
                                               </Tooltip>

                                           </div>
                                       )

                                   },
                                   tags: (row, key) => {
                                       return (
                                           <div>
                                               {row.row[key] ? row.row[key].split('|').map(item => {
                                                   return <Tag>{item}</Tag>
                                               }) : ''}
                                           </div>
                                       )

                                   },
                               }
                               }>
                </vxe-table>
                <BasicDrawer
                    onRegister={this.registerDrawer}
                    showFooter={this.showFooter}
                    title={this.modalTitle}
                    width={"50%"}
                    onOk={this.handleSubmit}
                    onClose={this.handleCancel}
                >
                    <BasicForm onRegister={this.registerForm} v-slots={{
                        formFooter: () => {


                            if (!checkEmpty(this.tabs)) {
                                console.log(this.tabs)
                                return <Col span={24}>
                                    <Tabs>
                                        {this.tabs.map((item) => {
                                            return <TabPane key={item.eruptModel.eruptName} tab={item.title}
                                                            forceRender={true}>
                                                {this.generateTab(item)}
                                            </TabPane>
                                        })}
                                    </Tabs>
                                </Col>
                            } else {
                                return ''
                            }
                        }
                    }}></BasicForm>
                </BasicDrawer>
                <EruptUploadPreviewModal readOnly ref="uploadModalRef" onRegister={registerPreviewModal}/>
            </div>
        );
    },
});
