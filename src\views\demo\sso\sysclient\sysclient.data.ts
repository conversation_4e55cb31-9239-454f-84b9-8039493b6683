import { BasicColumn, FormSchema } from '/@/components/Table';
import { uploadApi } from '/@/api/demo/SysclientUpload';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '显示排序',
    dataIndex: 'pos',
    width: 80,
  },
  {
    title: '系统名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '允许授权的url',
    dataIndex: 'allowUrl',
    width: 200,
  },
  {
    title: '是否显示',
    dataIndex: 'isFront',
    width: 80,
    customRender: ({ record }) => {
      const isPublic = record.isPublic;
      const enable = ~~isPublic === 1;
      const color = enable ? 'green' : 'red';
      const text = enable ? '显示' : '不显示';
      return h(Tag, { color: color }, () => text);
    },
  },
  {
    title: '状态',
    dataIndex: 'isPublic',
    width: 80,
    customRender: ({ record }) => {
      const status = record.status;
      const enable = ~~status === 1;
      const color = enable ? 'green' : 'red';
      const text = enable ? '启用' : '禁用';
      return h(Tag, { color: color }, () => text);
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '系统名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'isFront',
    label: '是否显示',
    component: 'Select',
    componentProps: {
      options: [
        { label: '显示', value: 1 },
        { label: '不显示', value: 0 },
      ],
    },
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    required: false,
    component: 'Input',
    show: false,
  },
  {
    field: 'name',
    label: '系统名称',
    required: true,
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'pos',
    label: '显示排序',
    required: false,
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'intro',
    label: '系统介绍',
    required: true,
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'logo',
    label: '系统图标',
    required: false,
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: { span: 24 },
  },
  {
    field: 'file',
    label: '上传图片',
    required: false,
    component: 'Upload',
    componentProps: ({ formModel }) => {
      return {
        api: uploadApi,
        maxSize: 10,
        onChange: (list: string[]) => {
          console.log(JSON.stringify(list));
          // or
          formModel.logo = list[0];
        },
      };
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'allowUrl',
    label: '允许授权的url',
    required: true,
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'isFront',
    label: '是否显示',
    required: true,
    component: 'RadioGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '显示', value: 1 },
        { label: '不显示', value: 0 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'status',
    label: '状态',
    required: true,
    component: 'RadioGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 2 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'isPublic',
    label: '是否公开',
    required: true,
    component: 'RadioGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '公开', value: 1 },
        { label: '私有', value: 2 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'type',
    label: '类型',
    required: true,
    component: 'RadioGroup',
    defaultValue: 'sso',
    componentProps: {
      options: [
        { label: 'sso', value: 'sso' },
        { label: 'oauth2', value: 'oauth2' },
        { label: 'link', value: 'link' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'secretkey',
    label: '密钥',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
  },
  {
    field: 'ssoRedirect',
    label: '跳转链接',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
  },
];
