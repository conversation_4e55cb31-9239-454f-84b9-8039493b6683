<template>
  <BasicTable @register="registerTable">
    <template #bodyCell="{ column, record }">
      <!--                    <template v-if="column.key === 'action'">-->
      <!--                      <TableAction :actions="createActions(record, column)" />-->
      <!--                    </template>-->
    </template>
  </BasicTable>
  <div>
    1111
  </div>
</template>

<script lang="ts">
  import {useTable,BasicTable} from "/@/components/Table";
  import {getDeptList} from "../../../../api/demo/system";
  import {columns, searchFormSchema} from "../../../../views/system/dept/dept.data";
  import { defineComponent, PropType, ref, watchEffect, computed, unref, watch } from 'vue';
    export default defineComponent({
      name: "EntityTable",
      components: {
        //BasicTable,
      },
      setup() {
        var [registerTable, { reload }] = useTable({
          title: '部门列表',
          api: getDeptList,
          columns,
          formConfig: {
            labelWidth: 120,
            schemas: searchFormSchema,
          },
          pagination: false,
          striped: false,
          useSearchForm: true,
          showTableSetting: true,
          bordered: true,
          showIndexColumn: false,
          canResize: false,
          actionColumn: {
            width: 80,
            title: '操作',
            dataIndex: 'action',
            // slots: { customRender: 'action' },
            fixed: undefined,
          },
        });
        return {
          registerTable
        };
      },
    })
</script>

<style scoped>

</style>
