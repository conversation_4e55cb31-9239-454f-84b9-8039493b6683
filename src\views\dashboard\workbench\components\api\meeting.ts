import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetItems = '/newline/meeting/items/',
  GetDetails = '/newline/meeting/details/',
  GetAnnualCount = '/newline/meeting/annual-count',
}

// 获取本周会议列表
export const getItemsApi = async (userId: number) => {
  return await defHttp.get({ url: Api.GetItems + userId });
};

// 获取会议详情
export const getItemDetailsApi = async (id: number) => {
  return await defHttp.get({ url: Api.GetDetails + id });
};

// 获取年度会议数量
export const getAnnualMeetingCountApi = async () => {
  return await defHttp.get({ url: Api.GetAnnualCount });
};
