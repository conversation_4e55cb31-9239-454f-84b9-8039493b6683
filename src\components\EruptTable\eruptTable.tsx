import { defineComponent, onMounted, reactive } from 'vue';
import { computed, ref } from 'vue';
import { BasicTableProps, VxeBasicTable, VxeGridInstance } from '/@/components/VxeTable';
import { ActionItem, TableAction } from '/@/components/Table';
//import './style/style.css';
const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();
import { useMessage } from '/@/hooks/web/useMessage';
import { BasicForm, useForm } from '/@/components/Form/index';
import { BasicDrawer, useDrawer } from '/@/components/Drawer';
import {
  buildApi,
  tableQueryApi,
  remove,
  batchRemove,
  save,
  getOne,
  update,
  getDetailOne,
  excel_export,
  getTemplate,
} from '/@/api/erupt/erupt';
import TableAdd from '/@/components/Form/src/components/TableAdd';
import TableSelectAdd from '/@/components/Form/src/components/TableSelectAdd';
import TreeSeletAdd from '/@/components/Form/src/components/TreeSeletAdd';
import {
  checkEmpty,
  getColFomatter,
  getComponent,
  getComponentProps,
  getSearchComponent,
} from '/@/components/EruptTable/componets';

import {
  Modal,
  Tooltip,
  Tabs,
  TabPane,
  Col,
  Tag,
  Collapse,
  CollapsePanel,
  Steps,
  Step,
  Upload,
  Card,
} from 'ant-design-vue';
import EruptUploadPreviewModal from '/@/components/Form/src/components/EruptUploadPreviewModal.vue';
import { Icon } from '/@/components/Icon';
import { BasicModal, useModal } from '/@/components/Modal';
import { bool } from 'vue-types';
import { getApprovalRecordsByBussiness, GetLatestStep, getNextNode } from '/@/api/process/process';
import { Description } from '/@/components/Description';
import { CollapseStatus } from 'vxe-table/types/form';
import { basicProps } from '/@/components/VxeTable/src/props';
import { useGlobSetting } from '/@/hooks/setting';
import { getToken } from '/@/utils/auth';
import { Base64 } from 'js-base64';
import { useUserStore } from '/@/store/modules/user';

export default defineComponent({
  name: 'eruptTable',
  props: {
    className: {},
    title: {},
    preConditions: { default: new Map([]) }, //前置条件，默认条件
    overwriteAction: { add: {}, edit: {}, detail: {}, delete: {}, batchDelete: {} },
    buttonRenders: {
      default: [],
    },
    ...basicProps,
    querySort: {
      type: Function,
      default: (querys) => {
        return querys;
      },
    },
    isProcess: { type: bool, default: false },
    seq: { type: bool, default: true },
    extraAction: {
      default: {
        nums: 0,
        actions: (row) => {
          return [];
        },
      },
    },
    rowDynamicController: {
      type: Object,
      default: {
        detail: (row) => {
          return true;
        },
        edit: (row) => {
          return true;
        },
        delete: (row) => {
          return true;
        },
      },
    },
    formDynamicControl: { type: Object, default: {} },
    formExtraAction: {
      default: () => {
        return null;
      },
    },
    editLabelWidth: { type: Number, default: 100 },
  },
  setup(props, { emit, attrs, slots }) {
    const {
      className,
      title,
      overwriteAction,
      formDynamicControl,
      extraAction,
      preConditions,
      formExtraAction,
      isProcess,
      rowDynamicController,
      seq,
      buttonRenders,
      cellStyle,
      querySort,
      editLabelWidth,
    } = props;
    const globSetting = useGlobSetting();
    const token = getToken();
    const firstSearch = ref(false);
    const conditions = ref([]);
    const [registerModal, { openModal: openModalPreview }] = useModal();
    const [register, { openModal, closeModal }] = useModal();
    const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
    const exportColumn = ref([]);
    const modalUrl = ref('');
    const key = ref(0);
    const tabs = ref([]);
    const tableRef = ref<VxeGridInstance>();
    const powerRef = ref({});
    const tabValues = ref({});
    const uploadModalRef = ref();
    const columns = ref([]);
    const modalTitle = ref('');
    const showFooter = ref(true);
    const base = ref({ id: '', version: '' });
    const [registerDrawer, { openDrawer, closeDrawer, setDrawerProps }] = useDrawer();
    const defaultSpaceRows = ref([]);
    const addSchema = ref([]);
    const editSchema = ref([]);
    const lookSchema = ref([]);
    const activeKey = ref('1');
    const drawerWidth = ref('');
    const approveRecords = ref([]);
    const detailVal = ref({});
    const latestStep = ref('');
    const collapseStatus = ref(true);
    const currentStep = ref(0);
    const labelCol = ref(null);
    const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
      labelWidth: editLabelWidth,
      schemas: editSchema,
      showActionButtonGroup: false,
    });
    const toolbarConfig = ref({
      buttons: [],
    });
    let querys = ref([]);
    const { createMessage } = useMessage();

    function refresh() {
      document.querySelector('button[content="查询"]').click();
    }

    function handleCancel() {
      resetFields();
    }

    async function handleBatchRemove() {
      const selectedRows = tableRef.value.getCheckboxRecords();
      let ids = [];
      selectedRows.forEach((item) => {
        ids.push(item.id);
      });
      const { status, message } = await batchRemove(className, ids);
      if (status == 'SUCCESS') {
        createMessage.success('删除成功');
        refresh();
      } else {
        createMessage.error('删除失败' + message);
      }
    }

    async function getFormData() {
      let values = await validate();
      return { ...base.value, ...values, ...tabValues.value };
    }
    async function handleSubmit() {
      debugger;
      try {
        let values = await validate();
        console.log(values);
        // setDrawerProps({confirmLoading: true});
        if (base.value.id) {
          const data = { ...base.value, ...values, ...tabValues.value };
          console.log(data);
          const { status, message } = await update(className, data, null);
          if (status == 'SUCCESS') {
            createMessage.success('保存成功');
            resetFields();
            console.log('data', data);
            refresh();
          } else {
            createMessage.error('保存失败:' + message);
          }
        } else {
          const data = { ...values, ...tabValues.value };
          const { status, message } = await save(className, data, null);
          if (status == 'SUCCESS') {
            createMessage.success('保存成功');
            resetFields();
            refresh();
          } else {
            createMessage.error('保存失败:' + message);
          }
        }
        // TODO custom api

        closeModal1();
        emit('success');
      } finally {
        // setDrawerProps({confirmLoading: false});
      }
    }

    function updateForm(schema) {
      updateSchema(schema);
    }

    function handleShow(downloadUrl, index) {
      console.log(downloadUrl);
      const userStore = useUserStore();
      const watermarkTxt = userStore.userInfo.username + ' ' + userStore.userInfo.realName;
      modalUrl.value = ''; // 清空旧缓存
      modalUrl.value =
        globSetting.previewUrl +
        '/onlinePreview?url=' +
        downloadUrl +
        '&watermarkTxt=' +
        watermarkTxt; // 设置预览 URL
      key.value = index; // 重新加载 iframe
      openModalPreview(true); // 打开 modal 窗口
    }

    function search(page, form) {
      debugger;
      let condition = [];
      if (!firstSearch.value) {
        preConditions.entries().forEach((item) => {
          if (form[item[0]] == undefined) {
            form[item[0]] = item[1];
          }
        });
      }
      for (let key in form) {
        if (form[key]) {
          condition.push({ key, value: form[key] });
        }
      }
      const query = {
        pageIndex: page.currentPage,
        pageSize: page.pageSize,
        condition,
      };
      conditions.value = condition;
      firstSearch.value = true;
      return tableQueryApi(className, query);
    }

    async function getDetail(id) {
      modalTitle.value = '详情';
      showFooter.value = false;
      openModal();
      /* let schema = []
             lookSchema.value.forEach(item => {
               let sc = JSON.parse(JSON.stringify(item))
               const props = item.componentProps()
               sc.componentProps = (form) => {
                 return {...props}
               }
               schema.push(sc)
             })*/

      getDetailOne(className, id).then((res) => {
        if (res) detailVal.value = res;
        console.log('detail', res);
        /*setTimeout(() => {
                  updateSchema(schema)
                },300)
                setFieldsValue(res)
                let tabV = {}
                for (let key in res) {
                  tabs.value.forEach(item => {
                    if (item.key == key) {
                      tabV[key] = res[key]
                    }
                  })
                }
                tabValues.value = tabV*/
      });
      getApprovalRecordsByBussiness(className + '.' + id).then((res) => {
        getNextNode(className, id).then((r) => {
          if (r) {
            res.push({ person: r.users, name: r.task });
            approveRecords.value = res;
            currentStep.value = approveRecords.value.length - 2;
          } else {
            approveRecords.value = res;
          }
        });
      });
      /*GetLatestStep(className + '.' + id).then(res => {
              if (res) {
                latestStep.value = res
              } else {
                latestStep.value = ''
              }
            })*/
    }

    async function add() {
      if (overwriteAction && overwriteAction.add) {
        overwriteAction.add();
      } else {
        modalTitle.value = '新增';
        showFooter.value = true;
        openModal1();
        base.value.id = '';
        base.value.version = '';
        let schema = [];
        debugger;
        console.log(addSchema.value);
        addSchema.value.forEach((item) => {
          let sc = JSON.parse(JSON.stringify(item));
          const props = item.componentProps();
          sc.componentProps = (form) => {
            return {
              ...props,
              onChange: (e) =>
                formDynamicControl[item.field] ? formDynamicControl[item.field](form, e) : () => {},
            };
          };
          schema.push(sc);
        });

        setTimeout(() => {
          updateForm(schema);
        }, 200);
        resetFields();
        for (let tabValuesKey in tabValues.value) {
          tabValues.value[tabValuesKey] = [];
        }
      }
    }

    async function edit(id) {
      debugger;
      modalTitle.value = '编辑';
      showFooter.value = true;
      resetFields();
      openModal1();
      const res = await getOne(className, id);
      // base.value.id = res.id
      // base.value.version = res.version
      base.value = res;
      JSON.parse(JSON.stringify(editSchema.value));
      let schema = [];
      editSchema.value.forEach((item) => {
        let sc = JSON.parse(JSON.stringify(item));
        const props = item.componentProps();
        sc.componentProps = (form) => {
          return {
            ...props,
            onChange: (e) =>
              formDynamicControl[item.field] ? formDynamicControl[item.field](form, e) : () => {},
          };
        };
        schema.push(sc);
      });
      setTimeout(() => {
        updateSchema(schema);
      }, 200);

      setFieldsValue(res);
      let tabV = {};
      for (let key in res) {
        tabs.value.forEach((item) => {
          if (item.key == key) {
            tabV[key] = res[key] ? res[key] : [];
          }
        });
      }
      tabValues.value = tabV;
    }

    async function deleted(id) {
      const { status, message } = await remove(className, id);
      if (status == 'SUCCESS') {
        createMessage.success('删除成功');
        refresh();
      } else {
        createMessage.error('删除失败:' + message);
      }
    }

    function generateTab(tab) {
      if (tab.type == 'TAB_TABLE_ADD')
        return (
          <TableAdd
            readOnly={modalTitle.value == '详情'}
            onChange={(v) => {
              tabValues.value[tab.key] = v;
            }}
            value={tabValues.value[tab.key]}
            className={className}
            tabClassName={tab.eruptModel.eruptName}
            tabName={tab.key}
            models={tab.eruptModel.eruptFieldModels}
          ></TableAdd>
        );
      if (tab.type == 'TAB_TABLE_REFER')
        return (
          <TableSelectAdd
            readOnly={modalTitle.value == '详情'}
            onChange={(v) => {
              tabValues.value[tab.key] = v;
            }}
            value={tabValues.value[tab.key]}
            className={className}
            tabName={tab.key}
            models={tab.eruptModel.eruptFieldModels}
          ></TableSelectAdd>
        );
      if (tab.type == 'TAB_TREE')
        return (
          <TreeSeletAdd
            readOnly={modalTitle.value == '详情'}
            onChange={(v) => {
              tabValues.value[tab.key] = v;
            }}
            value={tabValues.value[tab.key]}
            className={className}
            tabName={tab.key}
          ></TreeSeletAdd>
        );
    }
    onMounted(() => {
      buildApi(className).then((res) => {
        const {
          eruptModel: { eruptFieldModels, eruptJson },
          tabErupts,
          power,
        } = res;
        let cols = [];
        let exportCols = [];
        if (seq) {
          cols.push({ type: 'seq', width: '50px', title: '序号', align: 'center' });
        }
        let qs = [];
        let edits = [];
        let adds = [];
        let details = [];
        let tabItems = [];
        let buttons = [];
        powerRef.value = power;
        drawerWidth.value = eruptJson.drawerWidth;
        labelCol.value = eruptJson.labelCol;
        if (power.add == true) {
          buttons.push({
            content: '新增',
            buttonRender: {
              name: 'AButton',
              props: {
                type: 'primary',
              },
              events: {
                click: () => add(),
              },
            },
          });
        }
        if (power.importable == true) {
          buttons.push({
            content: '导入',
            buttonRender: {
              name: 'AButton',
              props: {
                type: 'primary',
              },
              events: {
                click: () => {
                  document.getElementById('upload').click();
                },
              },
            },
          });
        }
        if (power.export == true) {
          buttons.push({
            content: '下载模板',
            buttonRender: {
              name: 'AButton',
              props: {
                type: 'primary',
              },
              events: {
                click: () => {
                  getTemplate(className).then((response) => {
                    if (res.message) {
                      createMessage.error(res.message);
                    } else {
                      const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
                      const link = document.createElement('a');
                      link.href = URL.createObjectURL(blob);
                      link.download = title + '模板' + new Date().toLocaleDateString() + '.xls';
                      link.click();
                      URL.revokeObjectURL(link.href);
                    }
                  });
                },
              },
            },
          });
          buttons.push({
            content: '导出',
            buttonRender: {
              name: 'AButton',
              props: {
                type: 'primary',
              },
              events: {
                click: () => {
                  excel_export(className, conditions.value).then((response) => {
                    if (res.message) {
                      createMessage.error(res.message);
                    } else {
                      const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
                      const link = document.createElement('a');
                      link.href = URL.createObjectURL(blob);
                      link.download = title + new Date().toLocaleDateString() + '.xls';
                      link.click();
                      URL.revokeObjectURL(link.href);
                    }
                  });
                },
              },
            },
          });
        }
        if (power.delete == true)
          /*buttons.push({
              content: '删除',
              buttonRender: {
                name: 'AButton',
                props: {
                  type: 'warning',
                },
                events: {
                  click: () => {
                    Modal.confirm({
                      title: '提示',
                      content: '是否确认删除',
                      okText: '确认',
                      cancelText: '取消',
                      onOk() {
                        if (overwriteAction && overwriteAction.batchDelete) {
                          const selectedRows = tableRef.value.getCheckboxRecords();
                          let ids = [];
                          selectedRows.forEach((item) => {
                            ids.push(item.id);
                          });
                          overwriteAction.batchDelete(ids);
                        } else {
                          handleBatchRemove();
                        }
                      },
                    });
                  },
                },
              },
            });*/
          buttonRenders.forEach((bt) => {
            buttons.push(bt);
          });

        let tabV = {};

        eruptFieldModels.forEach((item) => {
          const key = item.fieldName;
          const title = item.eruptFieldJson.edit.title;
          if (item.eruptFieldJson.views.length > 0) {
            item.eruptFieldJson.views.forEach((v) => {
              if (v.show) {
                const k = v.column ? key + '_' + v.column : key;
                console.log('key', k, slots[k]);
                exportCols.push(getColFomatter(key, v, item.eruptFieldJson.edit));
                cols.push(getColFomatter(key, v, item.eruptFieldJson.edit, slots[k]));
                if (item.eruptFieldJson.edit.show.detail_show) {
                  debugger;
                  const d = {
                    field: k,
                    label: v.title,
                    span: item.eruptFieldJson.edit.detailSpan,
                  };
                  details.push(d);
                }
              }
            });
          } else {
            if (item.eruptFieldJson.edit.show.detail_show && key !== 'id') {
              if (item.eruptFieldJson.edit.type == 'ATTACHMENT') {
                if (!item.eruptFieldJson.edit.attachmentType.isgBigFile) {
                  const d = {
                    field: key,
                    label: title,
                    render: (val) => {
                      return (
                        <div>
                          {val
                            ? val.split('|').map((item, index) => {
                                return (
                                  <div>
                                    <a
                                      onClick={() =>
                                        handleShow(
                                          encodeURIComponent(
                                            Base64.encode(globSetting.eruptAttachment + item),
                                          ),
                                          index,
                                        )
                                      }
                                    >
                                      {item.substring(item.lastIndexOf('/') + 1)}
                                    </a>{' '}
                                    <a
                                      style={{ marginLeft: '15px', color: '#ff6118' }}
                                      href={globSetting.eruptAttachment + item}
                                    >
                                      下载
                                    </a>
                                  </div>
                                );
                              })
                            : ''}

                          {/*<Tooltip placement={'bottom'}>
                          <a-button
                            onClick={() => {
                              const files = val ? val.split('|') : [];
                              uploadModalRef.value.setFiles(files);
                              openPreviewModal();
                            }}
                          >
                            <Icon icon="bi:eye" />
                          </a-button>
                        </Tooltip>*/}
                        </div>
                      );
                    },
                  };
                  details.push(d);
                } else {
                  const d = {
                    field: key,
                    label: title,
                    render: (val) => {
                      return (
                        <div>
                          {val
                            ? val.split('|').map((item, index) => {
                                return (
                                  <div>
                                    <a
                                      style={{ marginLeft: '15px', color: '#ff6118' }}
                                      href={globSetting.eruptAttachment + item}
                                    >
                                      {item.substring(item.lastIndexOf('/') + 1)}
                                    </a>
                                  </div>
                                );
                              })
                            : ''}

                          {/*<Tooltip placement={'bottom'}>
                          <a-button
                            onClick={() => {
                              const files = val ? val.split('|') : [];
                              uploadModalRef.value.setFiles(files);
                              openPreviewModal();
                            }}
                          >
                            <Icon icon="bi:eye" />
                          </a-button>
                        </Tooltip>*/}
                        </div>
                      );
                    },
                  };
                  details.push(d);
                }
              } else {
                const d = {
                  field: key,
                  label: title,
                  span: item.eruptFieldJson.edit.detailSpan,
                };
                details.push(d);
              }
            }
          }
          //  formState[key] = null
          if (item.eruptFieldJson.edit.search.value) {
            const qComponent = getSearchComponent(
              key,
              title,
              item,
              6,
              className,
              preConditions.get(key),
            );
            qs.push(qComponent);
          }
          if (item.eruptFieldJson.edit.show.edit_show && key !== 'id') {
            if (
              item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
              item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
              item.eruptFieldJson.edit.type !== 'TAB_TREE'
            ) {
              const e = {
                field: key,
                label: item.eruptFieldJson.edit.type == 'DIVIDE' ? '' : title,
                component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                componentProps: (form) => {
                  return {
                    ...getComponentProps(item, className),
                    disabled: item.eruptFieldJson.edit.readOnly.edit,
                    onChange: (e) =>
                      formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {},
                  };
                },
                required: item.eruptFieldJson.edit.notNull,
                colProps: {
                  span:
                    item.eruptFieldJson.edit.type == 'DIVIDE'
                      ? 24
                      : item.eruptFieldJson.edit.colSpan,
                },
              };
              console.log(title, getComponentProps(item, className));
              const a = {
                field: key,
                label: item.eruptFieldJson.edit.type == 'DIVIDE' ? '' : title,
                component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                componentProps: (form) => {
                  return {
                    ...getComponentProps(item, className),
                    disabled: item.eruptFieldJson.edit.readOnly.add,
                    onChange: (e) =>
                      formDynamicControl[key] ? formDynamicControl[key](form, e) : () => {},
                  };
                },
                required: item.eruptFieldJson.edit.notNull,
                colProps: {
                  span:
                    item.eruptFieldJson.edit.type == 'DIVIDE'
                      ? 24
                      : item.eruptFieldJson.edit.colSpan,
                },
              };
              /* const k = v.column ? (key + "_" + v.column) : key
                           const d = {
                             field: k,
                             label: title,
                             component: getComponent(item.eruptFieldJson.edit.type,item.eruptFieldJson.edit),
                             componentProps: (form) => {
                               return {...getComponentProps(item, className), disabled: true,onChange: (e) => formDynamicControl[key]? formDynamicControl[key](form,e) : () => {}}
                             },
                             required: item.eruptFieldJson.edit.notNull,
                             colProps: {
                               span:item.eruptFieldJson.edit.colSpan,
                             },
                           }*/
              edits.push(e);
              adds.push(a);

              console.log('edits', edits);
            } else {
              if (tabErupts) {
                for (let tabEruptsKey in tabErupts) {
                  if (item.fieldName == tabEruptsKey) {
                    tabErupts[tabEruptsKey].title = item.eruptFieldJson.edit.title;
                    tabErupts[tabEruptsKey].key = tabEruptsKey;
                    tabErupts[tabEruptsKey].type = item.eruptFieldJson.edit.type;
                    tabItems.push(tabErupts[tabEruptsKey]);
                    tabV[tabEruptsKey] = [];
                  }
                }
              }
            }
          }
        });
        tabs.value = tabItems;
        tabValues.value = tabV;
        console.log('tabs', tabs.value, tabValues.value);
        let qsort = querySort(qs);

        for (let i = 0; i < qsort.length; i++) {
          if (i >= 4) {
            qsort[i].folding = true;
          }
        }
        qsort.push({
          span: 24,
          align: 'center',
          className: '!pr-0',
          collapseNode: true,
          itemRender: {
            name: 'AButtonGroup',
            children: [
              {
                props: { type: 'primary', content: '查询', htmlType: 'submit' },
                attrs: { class: 'mr-2' },
              },
              { props: { type: 'default', htmlType: 'reset', content: '重置' } },
            ],
          },
        });
        console.log('qsort', qsort);
        let Awidth = 110;
        if (powerRef.value.edit) {
          Awidth += 60;
        }
        if (powerRef.value.delete) {
          Awidth += 60;
        }
        Awidth += 60 * extraAction.nums;
        debugger;
        cols.push({
          title: '操作',
          align: 'center',
          width: Awidth,
          slots: { default: 'action' },
          fixed: 'right',
        });

        querys.value = qsort;

        columns.value = cols;
        exportColumn.value = exportCols;
        editSchema.value = edits;
        addSchema.value = adds;
        lookSchema.value = details;
        console.log('look', lookSchema.value);
        toolbarConfig.value.buttons = buttons;
        //gridOptions.columns = columns
        //search()
      });
    });

    function getActions(row) {
      let actions = [
        /*{
          label: '详情',
          onClick: () => {
            if (overwriteAction && overwriteAction.detail) {
              overwriteAction.detail(row.row);
            } else {
              this.getDetail(row.row.id);
            }
          },
        },*/
      ];

      if (
        powerRef.value.viewDetails &&
        (!rowDynamicController.detail || rowDynamicController.detail(row.row))
      ) {
        actions.push({
          label: '详情',
          onClick: () => {
            if (overwriteAction && overwriteAction.detail) {
              overwriteAction.detail(row.row);
            } else {
              this.getDetail(row.row.id);
            }
          },
        });
      }

      if (
        powerRef.value.edit &&
        (!rowDynamicController.edit || rowDynamicController.edit(row.row))
      ) {
        actions.push({
          label: '编辑',
          onClick: () => {
            if (overwriteAction && overwriteAction.edit) {
              overwriteAction.edit(row.row);
            } else {
              this.edit(row.row.id);
            }
          },
        });
      }
      if (
        powerRef.value.delete &&
        (!rowDynamicController.delete || rowDynamicController.delete(row.row))
      ) {
        actions.push({
          label: '删除',
          color: 'error',
          popConfirm: {
            title: '是否确认删除',
            confirm: () => {
              if (overwriteAction && overwriteAction.delete) {
                overwriteAction.delete(row.row.id);
              } else {
                this.deleted(row.row.id);
              }
              // tableRef.value?.remove(record);
            },
          },
        });
      }
      if (extraAction.nums > 0)
        extraAction.actions(row).forEach((action) => {
          actions.push(action);
        });
      return <TableAction outside actions={actions} />;
    }

    return {
      tableRef,
      add,
      //createActions,
      modalUrl,
      columns,
      querys,
      register1,
      registerModal,
      handleShow,
      openModal,
      openModal1,
      closeModal,
      closeModal1,
      toolbarConfig,
      search,
      registerDrawer,
      openDrawer,
      closeDrawer,
      registerForm,
      modalTitle,
      showFooter,
      handleSubmit,
      setDrawerProps,
      getDetail,
      edit,
      deleted,
      handleCancel,
      defaultSpaceRows,
      uploadModalRef,
      tabs,
      refresh,
      tabValues,
      powerRef,
      getActions,
      generateTab,
      drawerWidth,
      updateForm,
      getFormData,
      register,
      lookSchema,
      activeKey,
      approveRecords,
      detailVal,
      isProcess,
      latestStep,
      exportColumn,
      cellStyle,
      collapseStatus,
      globSetting,
      token,
      conditions,
      openModalPreview,
      afterUpload: ({ file }) => {
        if (file.status !== 'uploading') {
          if (file.status == 'error') {
            createMessage.error(file.response.message);
          } else {
            createMessage.success('导入成功');
            refresh();
          }
        }
      },
    };
  },
  render() {
    return (
      <div>
        <div style={{ display: 'none' }}>
          <Upload
            id={'upload'}
            name={'file'}
            action={`${this.globSetting.eruptUrl}/excel/import/${this.className}`}
            style={{ display: 'none' }}
            showUploadList={false}
            accept={'.xls,.xlsx'}
            headers={{ satoken: this.token.tokenValue }}
            onChange={this.afterUpload}
          >
            <button style={{ display: 'none' }}></button>
          </Upload>
        </div>
        <VxeBasicTable
          ref="tableRef"
          columns={this.columns}
          formConfig={{
            enabled: true,
            items: this.querys,
            titleAlign: 'right',
            titleWidth: '20%',
            collapseStatus: this.collapseStatus,
            titleColon: true,
          }}
          cellStyle={this.cellStyle}
          toolbarConfig={this.toolbarConfig}
          columnConfig={{ isCurrent: false, isHover: false }}
          rowConfig={{ isCurrent: false, isHover: false }}
          exportConfig={{
            type: 'xlsx',
            columns: this.exportColumn,
          }}
          proxyConfig={{
            ajax: {
              query: async ({ page, form }) => {
                console.log(form);
                return this.search(page, form);
              },
            },
          }}
          // checked 为控制勾选的变量；
          // defaultSpaceRows 为默认需要勾选的行；
          v-slots={{
            level: (row) => {
              return (
                <div class={'level-' + row.row.level}>
                  {row.row.level > 0 ? (
                    <Icon icon="ri:checkbox-blank-circle-fill" />
                  ) : (
                    <Icon icon="ri:checkbox-blank-circle-line" />
                  )}
                </div>
              );
            },
            action: (row) => {
              return this.getActions(row);
            },
            upload: (row, key) => {
              return (
                <div>
                  <Tooltip placement={'bottom'}>
                    <a-button
                      onClick={() => {
                        const files = row.row[key] ? row.row[key].split('|') : [];
                        this.uploadModalRef.setFiles(files);
                        openPreviewModal();
                      }}
                    >
                      <Icon icon="bi:eye" />
                    </a-button>
                  </Tooltip>
                </div>
              );
            },
            tags: (row, key) => {
              return (
                <div>
                  {row.row[key]
                    ? row.row[key].split(row.column.slots.joinSeparator).map((item) => {
                        return <Tag>{item}</Tag>;
                      })
                    : ''}
                </div>
              );
            },
          }}
        ></VxeBasicTable>
        <BasicModal
          onRegister={this.register}
          width={'80%'}
          defaultFullscreen={true}
          showFooter={false}
          footer={[]}
          title="详情"
        >
          <div style="display: flex; flex-direction: row">
            <div style="width: 78%">
              <Card title="详细信息">
                <Description
                  layout={'vertical'}
                  column={4}
                  data={this.detailVal}
                  schema={this.lookSchema}
                ></Description>
              </Card>
            </div>
            {this.isProcess ? (
              <div style="width: 20%; height: 100%">
                <Card
                  title="流程信息"
                  bordered={false}
                  key="2"
                  header={
                    '审批历史' /*+ (this.latestStep ? "(当前步骤：" + this.latestStep + ")" : "")*/
                  }
                >
                  <Steps status={'finish'} direction={'vertical'}>
                    {this.approveRecords.map((item) => {
                      return (
                        <Step
                          status={!item.action ? 'process' : 'finish'}
                          title={item.name}
                          style={!item.action ? { color: 'green' } : {}}
                          v-slots={{
                            description: () => (
                              <div
                                style={{ color: !item.action ? 'rgba(0, 0, 0, 0.45)' : 'black' }}
                              >
                                <div>
                                  {!item.action ? '待办理人' : '办理人'} ：{item.person}
                                </div>
                                {item.action ? <div>处理方式：{item.action}</div> : ''}
                                {item.opinion ? <div>审批意见：{item.opinion}</div> : ''}
                                {item.createDateTime ? <p>办理时间：{item.createDateTime}</p> : ''}
                              </div>
                            ),
                          }}
                        ></Step>
                      );
                    })}
                  </Steps>
                </Card>
              </div>
            ) : (
              ''
            )}
          </div>
        </BasicModal>
        <BasicModal
          onRegister={this.register1}
          showFooter={this.showFooter}
          title={this.modalTitle}
          f
          defaultFullscreen={true}
          width={this.drawerWidth}
          onOk={this.handleSubmit}
          onClose={this.handleCancel}
          okText={'保存'}
          v-slots={{
            appendFooter: () => {
              if (this.modalTitle != '详情') {
                console.log('form-action', this.formExtraAction);
                return this.formExtraAction;
              }
              return null;
            },
          }}
        >
          <BasicForm
            onRegister={this.registerForm}
            v-slots={{
              formFooter: () => {
                if (!checkEmpty(this.tabs)) {
                  console.log(this.tabs);
                  return (
                    <Col span={24}>
                      <Tabs>
                        {this.tabs.map((item) => {
                          console.log('tab', item);
                          return (
                            <TabPane key={item.key} tab={item.title} forceRender={true}>
                              {this.generateTab(item)}
                            </TabPane>
                          );
                        })}
                      </Tabs>
                    </Col>
                  );
                } else {
                  return '';
                }
              },
            }}
          ></BasicForm>
        </BasicModal>

        <EruptUploadPreviewModal readOnly ref="uploadModalRef" onRegister={registerPreviewModal} />
        <BasicModal
          onRegister={this.registerModal}
          title="预览文件"
          width={1000}
          height={600}
          showFooter={null}
        >
          <iframe key="key" src={this.modalUrl} width="1000" height="800"></iframe>
        </BasicModal>
      </div>
    );
  },
});
