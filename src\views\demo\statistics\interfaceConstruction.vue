<template>
  <div class="interface-construction-container">
    <Card
      title="接口施工管理统计"
      :headStyle="{ fontSize: '18px', fontWeight: 'bold', borderBottom: '1px solid #f0f0f0' }"
    >
      <!-- 筛选区域 -->
      <template #extra>
        <div style="height: 32px" />
        <a-space direction="horizontal">
          <a-select
            v-model="line"
            style="width: 200px"
            placeholder="请选择线路"
            :options="lineOptions"
            @change="setLine"
            :loading="loading"
            :disabled="loading"
            allowClear
          ></a-select>
        </a-space>
      </template>

      <!-- 柱状图 -->
      <div class="chart-legend"></div>
      <div ref="chartRef" class="chart-container"></div>

      <!-- 数据表格 -->
      <div class="table-header">
        <h3>施工阶段详细数据</h3>
        <div class="table-summary">共 {{ tableData.length - 1 }} 个线路数据</div>
      </div>
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        bordered
        row-key="line"
        size="middle"
        class="data-table"
      >
        <template #bodyCell="{ column, record }">
          <!-- 突出显示小计行 -->
          <template v-if="column.dataIndex === 'line' && record.line === '小计'">
            <strong style="color: #1890ff">{{ record.line }}</strong>
          </template>
          <template v-else-if="column.dataIndex === '总数'">
            <span style="font-weight: 500">{{ record[column.dataIndex] }}</span>
          </template>
          <template v-else>
            {{ record[column.dataIndex] }}
          </template>
        </template>
      </a-table>
    </Card>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { getInterfaceConstruction, getTableData } from '/@/views/demo/statistics/api/statistics';
  import { Card } from 'ant-design-vue';
  import { useLineStoreWithOut } from '/@/store/modules/line'; // 引入线路store

  // 类型定义
  interface ChartData {
    [key: string]: number; // 部门名称 -> 非空字段数量
  }

  interface TableDataItem {
    line: string;
    前期管理: number;
    材料准备: number;
    过程管理: number;
    已完工: number;
    总数: number;
  }

  // 响应式数据
  const chartData = ref<ChartData>({});
  const tableData = ref<TableDataItem[]>([]);
  const line = ref(''); // 线路筛选值
  const lineOptions = ref<any[]>([]); // 线路选项
  const loading = ref(true); // 加载状态
  const lineStore = useLineStoreWithOut(); // 线路store实例

  // ECharts 实例
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef);

  // 表格列配置
  const columns = [
    {
      title: '类别',
      dataIndex: 'line',
      key: 'line',
      width: 150,
      fixed: 'left',
      align: 'center',
    },
    {
      title: '前期管理',
      dataIndex: '前期管理',
      key: '前期管理',
      align: 'center',
      sorter: (a, b) => a.前期管理 - b.前期管理,
    },
    {
      title: '施工过程',
      dataIndex: '施工过程',
      key: '施工过程',
      align: 'center',
      sorter: (a, b) => a.施工过程 - b.施工过程,
    },
    {
      title: '施工验收',
      dataIndex: '施工验收',
      key: '施工验收',
      align: 'center',
      sorter: (a, b) => a.施工验收 - b.施工验收,
    },
    {
      title: '其他',
      dataIndex: '其他',
      key: '其他',
      align: 'center',
      sorter: (a, b) => a.其他 - b.其他,
    },
    {
      title: '总数',
      dataIndex: '总数',
      key: '总数',
      align: 'center',
      sorter: (a, b) => a.总数 - b.总数,
      defaultSortOrder: 'descend',
    },
  ];

  // 初始化
  onMounted(async () => {
    // 获取线路选项
    try {
      const options = await lineStore.getLineOptions();
      lineOptions.value = options;
      loading.value = false;
    } catch (error) {
      console.error('获取线路选项失败:', error);
      loading.value = false;
    }

    // 加载初始数据
    await loadData();
  });

  // 设置线路筛选
  const setLine = (val: string) => {
    line.value = val;
    loadData();
  };

  // 加载数据（柱状图和表格）
  const loadData = async () => {
    try {
      // 加载柱状图数据
      const chartRes = await getInterfaceConstruction({ line: line.value });
      chartData.value = chartRes;
      updateChart();

      // 加载表格数据
      const tableRes = await getTableData({ line: line.value });
      tableData.value = tableRes;
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  };

  // 更新柱状图
  const updateChart = () => {
    if (!chartData.value) return;

    const departments = Object.keys(chartData.value);
    const counts = Object.values(chartData.value);

    setOptions({
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(50, 50, 50, 0.7)',
        borderColor: '#333',
        textStyle: {
          color: '#fff',
        },
        formatter: function (params) {
          const name = params[0].name;
          const value = params[0].value;
          return `${name}<br/>接口数量: ${value}`;
        },
      },
      xAxis: {
        type: 'category',
        data: departments,
        axisLabel: {
          rotate: departments.length > 5 ? 30 : 0,
          interval: 0,
        },
        axisLine: {
          lineStyle: {
            color: '#666',
          },
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666',
            width: 1,
          },
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      grid: {
        top: 80,
        bottom: 30,
        left: 50,
        right: 20,
      },
      series: [
        {
          name: '接口数量',
          type: 'bar',
          barMaxWidth: 60,
          data: counts,
          itemStyle: {
            color: '#1890ff',
            borderRadius: [4, 4, 0, 0],
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c}',
            color: '#666',
          },
        },
      ],
      dataZoom:
        departments.length > 8
          ? [
              {
                type: 'slider',
                show: true,
                xAxisIndex: [0],
                start: 0,
                end: 100,
                height: 20,
                bottom: 5,
                handleSize: 8,
                handleStyle: {
                  color: '#1890ff',
                },
              },
            ]
          : [],
    });
  };
</script>

<style lang="less" scoped>
  .interface-construction-container {
    padding: 16px;
    background: #f5f7fa;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      :deep(.ant-card-head) {
        border-bottom: 1px solid #f0f0f0;
      }
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 0 8px;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }

      .chart-legend {
        display: flex;
        align-items: center;

        .legend-item {
          display: flex;
          align-items: center;
          margin-left: 16px;
          font-size: 14px;
          color: #666;

          .legend-color {
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-right: 6px;
            border-radius: 2px;
          }
        }
      }
    }

    .chart-container {
      height: 400px;
      margin-bottom: 32px;
      background: #fff;
      border-radius: 4px;
      padding: 16px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 24px 0 16px;
      padding: 0 8px;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }

      .table-summary {
        font-size: 14px;
        color: #666;
      }
    }

    .data-table {
      :deep(.ant-table) {
        border-radius: 4px;
        overflow: hidden;

        .ant-table-thead > tr > th {
          background-color: #fafafa;
          font-weight: 600;
          color: #333;
        }

        .ant-table-tbody > tr:hover > td {
          background-color: #f5f7fa !important;
        }
      }
    }
  }
</style>
