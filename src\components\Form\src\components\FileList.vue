<script lang="tsx">
  import { defineComponent, CSSProperties, watch, nextTick, ref } from 'vue';
  import { fileListProps } from '/@/components/Upload/src/props';
  import { useModalContext } from '/@/components/Modal/src/hooks/useModalContext';
  import EruptUpload from '/@/components/Form/src/components/EruptUpload.vue';
  import { getUpLoadApi } from '/@/components/EruptTable/componets';
  import { useUserStore } from '/@/store/modules/user';
  import { save } from '/@/api/erupt/erupt';
  import { Input, Button, DatePicker } from 'ant-design-vue';
  import { SaveFile } from '/@/components/Form/src/api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Modal } from 'ant-design-vue';
  import dayjs from 'dayjs';


  export default defineComponent({
    name: 'FileList',
    props: fileListProps,
    emits: ['change', 'update:value'],
    setup(props, { attrs, emit }) {
      const userStore = useUserStore();
      const modalFn = useModalContext();
      const dataSource = ref(props.dataSource);
      const userDept = userStore.getUserInfo.dept;
      const allowedDepts = ['新线管理部', '资产管理部'];
      const needProject = [
        '外单位作业安全、消防、治安内安全协议',
        '施工负责人证',
        '外部施工作业许可单',
      ];

      watch(
        () => props.dataSource,
        (v) => {
          nextTick(() => {
            emit('update:value', v);
            emit('change', v);
            modalFn?.redoModalHeight?.();
          });
        },
      );

      watch(
        () => props.value,
        (v) => {
          if (v) {
            console.log('vchange', v);
            dataSource.value = v;
          }
        },
      );
      return () => {
        const columns = [
          {
            fieldName: 'stage',
            dataIndex: 0,
            width: '180px',
            title: '阶段',
            align: 'center',
          },
          {
            fieldName: 'name',
            dataIndex: 1,
            width: '180px',
            title: '文件分类',
            align: 'center',
          },
          {
            fieldName: 'content',
            dataIndex: 2,
            width: '180px',
            title: '文件',
            align: 'center',
          },
          {
            fieldName: 'des',
            dataIndex: 3,
            width: '180px',
            title: '备注',
            align: 'center',
          },
          {
            fieldName: 'deadlineTime',
            dataIndex: 4,
            width: '180px',
            title: '有效截止时间',
            align: 'center',
          },
          {
            fieldName: 'uploader',
            dataIndex: 5,
            width: '180px',
            title: '上传人',
            align: 'center',
          },
          {
            fieldName: 'uploadTime',
            dataIndex: 6,
            width: '180px',
            title: '上传时间',
            align: 'center',
          },
          {
            fieldName: 'uploadDept',
            dataIndex: 7,
            width: '180px',
            title: '实施单位',
            align: 'center',
          },
          {
            fieldName: 'state',
            dataIndex: 8,
            width: '180px',
            title: '状态',
            align: 'center',
          },
        ];
        const { createMessage } = useMessage();
        const { title, readonly } = props;
        if (!readonly) {
          columns.push({
            fieldName: 'operation',
            dataIndex: 9,
            width: '180px',
            title: '操作',
            align: 'center',
          });
        }
        const columnList = [...columns];
        const onUploadChange = async (file, index, field) => {
          console.log(userStore.getUserInfo);
          dataSource.value[index].url = file;
          dataSource.value[index].uploader = userStore.getUserInfo.realName;
          dataSource.value[index].uploadTime = new Date().toLocaleString();
          document.getElementById(`${field}.uploader`).innerText = userStore.getUserInfo.realName;
          document.getElementById(`${field}.uploadTime`).innerText = new Date().toLocaleString();
          SaveFile(dataSource.value[index]).then((res) => {
            const obj = { ...dataSource.value[index], ...res };
            dataSource.value[index] = obj;
            emit('update:value', dataSource.value);
            emit('change', dataSource.value);
          });
        };
        const baseUrl = import.meta.env.VITE_GLOB_ERUPT_ATTACHMENT;
        const formatFileLinks = (filePaths) => {
          if (!filePaths) return ''; // 如果文件路径为空，返回空字符串

          return filePaths
            .split('|')
            .map((filePath) => {
              const fileName = filePath.substring(filePath.lastIndexOf('/') + 1); // 提取文件名
              const fileUrl = `${baseUrl}${filePath}`; // 拼接完整的URL
              return `<a href="${fileUrl}" download="${fileName}" target="_blank">${fileName}</a>`;
            })
            .join(' </br> '); // 用</br>连接多个文件链接
        };
        return (
          <table class="file-table">
            <colgroup>
              {columns.map((item) => {
                const { width = 0, dataIndex } = item;
                const style: CSSProperties = {
                  width: `${width}px`,
                  minWidth: `${width}px`,
                };
                return <col width={width} style={width ? style : {}} key={dataIndex} />;
              })}
            </colgroup>
            <thead>
              {readonly ? (
                <>
                  {' '}
                  <tr class="file-table-tr" style="height:45px">
                    <th colspan={columns.length}>{title}</th>
                  </tr>
                  <tr></tr>
                </>
              ) : (
                ''
              )}
              <tr class="file-table-tr">
                {columns.map((item) => {
                  const { title = '', align = 'center', dataIndex } = item;
                  return (
                    <th class={['file-table-th', align]} key={dataIndex}>
                      {title}
                    </th>
                  );
                })}
              </tr>
            </thead>
            <tbody>
              {dataSource.value.map((record = {}, index) => {
                return (
                  <tr class="file-table-tr" key={`${index + record.name || ''}`}>
                    {columnList.map((item) => {
                      const { dataIndex = '', align = 'center', fieldName } = item;
                      return dataIndex == 2 ? (
                        readonly ? (
                          <td align="center" v-html={formatFileLinks(record.url)}>
                            {' '}
                          </td>
                        ) : record.editable ? (
                          <td
                            class={['file-table-td', align]}
                            id={`${record.field}.${fieldName}`}
                            key={dataIndex}
                          >
                            {' '}
                            <EruptUpload
                              readonly={readonly}
                              value={record.url}
                              accept={[
                                'jpg',
                                'jpeg',
                                'png',
                                'gif',
                                'bmp',
                                'pdf',
                                'doc',
                                'docx',
                                'xls',
                                'xlsx',
                                'ppt',
                                'pptx',
                                'zip',
                                'rar',
                                'tar',
                                'gz',
                                'txt',
                                'md',
                              ]}
                              api={getUpLoadApi('InterfaceConstructionFile', 'url')}
                              onChange={(file) => {
                                onUploadChange(file, index, record.field, fieldName);
                              }}
                            ></EruptUpload>
                          </td>
                        ) : (
                          <td align="center" v-html={formatFileLinks(record.url)}>
                            {' '}
                          </td>
                        )
                      ) : dataIndex == 3 ? (
                        record.editable ? (
                          <td
                            class={['file-table-td', align]}
                            id={`${record.field}.${fieldName}`}
                            key={dataIndex}
                          >
                            {' '}
                            <Input v-model:value={record.des}></Input>
                          </td>
                        ) : (
                          <td
                            class={['file-table-td', align]}
                            id={`${record.field}.${fieldName}`}
                            key={dataIndex}
                          >
                            {' '}
                            {record.des}{' '}
                          </td>
                        )
                      ) : dataIndex == 4 ? (
                        needProject.includes(record.name) ? ( // 新增条件判断
                          record.editable ? (
                            <td
                              class={['file-table-td', align]}
                              id={`${record.field}.${fieldName}`}
                              key={dataIndex}
                            >
                            <DatePicker 
                              v-model:value={record.deadlineTime}
                              valueFormat="YYYY-MM-DD HH:mm:ss" 
                              showTime
                            ></DatePicker>
                            </td>
                          ) : (
                            <td
                              class={['file-table-td', align]}
                              id={`${record.field}.${fieldName}`}
                              key={dataIndex}
                            >
                              {record.deadlineTime
                                ? new Date(record.deadlineTime).toLocaleString()
                                : ''}
                            </td>
                          )
                        ) : (
                          <td
                            class={['file-table-td', align]}
                            id={`${record.field}.${fieldName}`}
                            key={dataIndex}
                          >
                            {/* 非指定分类显示空或提示 */}
                            {!readonly && ""}
                          </td>
                        )
                      ) : dataIndex == 9 ? (
                        <td
                          class={['file-table-td', align]}
                          id={`${record.field}.${fieldName}`}
                          key={dataIndex}
                        >
                        <a
                        style={{ 
                          display: !record.editable && userDept === record.uploadDept &&
                          record.state !== '已确认' // 新增状态判断
                            ? 'inline-block' 
                            : 'none',
                          marginRight: '8px' // 添加适当间距
                        }}
                        onClick={() => {
                          Modal.confirm({
                                  title: '请注意,确定后不能编辑该行数据',
                                  onOk: async () => {
                          record.state = '已确认';
                          SaveFile(record).then((res) => {
                            if (res) {
                              const obj = { ...record, ...res };
                              dataSource.value[index] = obj;
                              emit('update:value', dataSource.value);
                              emit('change', dataSource.value);
                              createMessage.success('确认成功');
                            } else {
                              createMessage.error('确认失败');
                            }
                          });
                        },
                        style: {
                              top: '40%',
                          }
                        })
                        }}
                          >
                            确认
                          </a>
                          <a
                          style={{ 
                            display: 
                              !record.editable &&
                              record.state !== '已确认' && (  // 仅非编辑状态时判断权限
                                allowedDepts.includes(userDept) || 
                                (
                                  !allowedDepts.includes(userDept) && 
                                  !allowedDepts.includes(record.uploadDept) && 
                                  userDept === record.uploadDept
                                )
                              ) 
                              ? 'inline-block' 
                              : 'none'
                          }}
                            onClick={() => {
                              record.editable = true;
                            }}
                          >
                            编辑
                          </a>
                          <a
                            style={{ display: !record.editable ? 'none' : 'inline-block' }}
                            onClick={() => {
                              record.uploader = userStore.getUserInfo.realName;
                              record.uploadTime = new Date().toLocaleString();
                              record.editable = false;
                              SaveFile(record).then((res) => {
                                if (res) {
                                  const obj = { ...record, ...res };
                                  dataSource.value[index] = obj;
                                  emit('update:value', dataSource.value);
                                  emit('change', dataSource.value);
                                  createMessage.success('保存成功');
                                } else {
                                  createMessage.error('保存失败');
                                }
                              });
                            }}
                          >
                            保存
                          </a>
                          &nbsp;&nbsp;
                          <a
                            style={{ display: !record.editable ? 'none' : 'inline-block' }}
                            onClick={() => {
                              record.editable = false;
                            }}
                          >
                            取消
                          </a>
                        </td>
                      ) : (
                        <td
                          class={['file-table-td', align]}
                          id={`${record.field}.${fieldName}`}
                          key={dataIndex}
                        >
                          {record[fieldName]}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        );
      };
    },
  });

  /*{render
                             ? customRender?.({ text: record[dataIndex], record })
                             : record[dataIndex]}*/
</script>
<style lang="less">
  .file-table {
    width: 100%;
    border-collapse: collapse;

    .center {
      text-align: center;
    }

    .left {
      text-align: left;
    }

    .right {
      text-align: right;
    }

    &-th,
    &-td {
      padding: 12px 8px;
    }

    thead {
      background-color: @background-color-light;
    }

    table,
    td,
    th {
      border: 1px solid @border-color-base;
    }
  }
</style>
