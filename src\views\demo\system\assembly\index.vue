<template>
  <erupt-table
    title="汇编详情"
    class-name="Assembly"
    :is-process="true"
    :form-dynamic-control="dynamicController"
    :pre-conditions="preCondition"
    :extra-action="extraAction"
    :cell-style="cellStyle"
  >
    <template #isLate="{ row }">
      <div v-if="row.isLate == '是'" style="align-self: center; color: red">
        {{ row.isLate }}
      </div>
      <div v-if="row.isLate == '否'" style="align-self: center; color: #00bb00">
        {{ row.isLate }}
      </div>
    </template>
    <!--    <template #issafe="{ row }">
      <div v-if="row.isLate == '是'" style="align-self: center">
        <a-tag color="red" style="width: 50px; text-align: center">
          {{ row.issafe }}
        </a-tag>
      </div>
      <div v-if="row.isLate == '否'" style="align-self: center">
        <a-tag color="red" style="width: 50px; text-align: center">
          {{ row.issafe }}
        </a-tag>
      </div>
    </template>-->
    <template #approveStatus="{ row }">
      <div v-if="row.approveStatus == '待提报'" style="align-self: center">
        <a-tag color="green" style="width: 50px; text-align: center">
          {{ row.approveStatus }}
        </a-tag>
      </div>
      <div v-if="row.approveStatus == '审核中'" style="align-self: center">
        <a-tag color="blue" style="width: 50px; text-align: center">
          {{ row.approveStatus }}
        </a-tag>
      </div>
      <div v-if="row.approveStatus == '完成'" style="align-self: center">
        <a-tag color="red" style="width: 50px; text-align: center">
          {{ row.approveStatus }}
        </a-tag>
      </div>
      <div v-if="row.approveStatus == '终止'" style="align-self: center">
        <a-tag color="#8b949e" style="width: 50px; text-align: center">
          {{ row.approveStatus }}
        </a-tag>
      </div>
    </template>
  </erupt-table>
</template>
<script lang="ts">
  import { defineComponent, onMounted } from 'vue';
  import eruptTable from '../../../../components/EruptTable/eruptTable';
  import { NColorPicker } from 'naive-ui';
  import { useUserStore } from '/@/store/modules/user';
  import { getUpLoadApi, startProcess } from '/@/views/demo/system/assembly/api/assembly';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { computed } from 'vue';
  import { useRoute } from 'vue-router';
  import Template from '/@/views/demo/system/quality/template.vue';
  export default defineComponent({
    name: 'Assembly',
    components: { Template, eruptTable, NColorPicker },
    setup() {
      const route = useRoute();
      const line = computed(() => route.query.line);
      const dept = computed(() => route.query.dept);
      const state = computed(() => route.query.state);
      const userStore = useUserStore();
      const { createMessage } = useMessage();
      const preCondition = new Map([]);
      const cellStyle = ({ row, column }) => {
        let style = {
          color: '606266',
          backgroundColor: 'white',
        };
        /* if (row.isLate) {
          style.backgroundColor = '#02ecc9';
        }*/
        if (column.field === 'state') {
          // 根据状态值设置不同的字体颜色
          if (row.state === '已落实-闭口') {
            style.color = '#1E90FF';
          } else if (row.state === '未落实-开口') {
            style.color = '#32CD32';
          } else if (row.state === '已反馈-闭口') {
            style.color = '#FFD700';
          } else if (row.state === '其他-闭口') {
            style.color = '#FF6347';
          } else if (row.state === '本条线路不适用') {
            style.color = '#228B22';
          } else if (row.state === '待操作') {
            style.color = '#DC143C';
          }
          // 默认字体颜色
        }
        return style;
      };
      const overwriteAction = {
        add: () => {
          console.log('add');
        },
      };
      onMounted(() => {
        debugger;
        if (dept.value != null && dept.value != '' && dept.value != '新线管理部') {
          preCondition.set('mainDeptNames', dept.value);
        } else if (userStore.userInfo.dept != '新线管理部') {
          preCondition.set('mainDeptNames', userStore.userInfo.dept);
        }
        if (state.value != null && state.value != '') {
          preCondition.set('state', state.value);
        }
        if (line.value != null && line.value != '') {
          preCondition.set('line', line.value);
        }
      });

      //三层组件动态change
      const dynamicController = {
        age: (form, e) => {
          const { formModel, formActionType } = form;
          console.log(form, e);
          formActionType.updateSchema({
            field: 'tags',
            componentProps: (f) => {
              //改变age值对tags的选项产生修改
              return {
                options: [{ label: '111', value: '123123' }],
                onChange: (e) => {
                  dynamicController.tags(f); //同样tags值改变，仍然保持onChange属性
                },
              };
            },
          });
        },
        tags: (form) => {
          console.log(form);
        },
      };
      return {
        overwriteAction,
        dynamicController,
        preCondition,
        cellStyle,
        extraAction: {
          nums: userStore.userInfo.username == 'admin1' ? 1 : 0,
          actions: (row) => {
            if (userStore.userInfo.username == 'admin1') {
              return [
                {
                  label: '启动流程',
                  popConfirm: {
                    title: '是否确认启动流程',
                    confirm: () => {
                      startProcess(row.row.id).then((res) => {
                        if (res) {
                          createMessage.success('启动流程成功');
                        }
                      });
                    },
                  },
                },
              ];
            }
          },
        },
      };
    },
  });
</script>
