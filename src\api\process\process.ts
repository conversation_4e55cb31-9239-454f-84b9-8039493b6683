import { ProcessDefinitionModel } from './model/processModel';
import { defHttp } from '/@/utils/http/axios';
import { useUserStore } from '/@/store/modules/user';

enum Api {
  ProcessDefinitionList = '/process/getBannerItemPageList',
  BannerItemSaveOrUpdate = '/system/banneritem/saveOrUpdate',
  BannerItemDelete = '/system/banneritem/delete',
  ExternalLinkPageList = '/system/getExternalLinkPageList',
  ExternalLinkSaveOrUpdate = '/system/externallink/saveOrUpdate',
  ExternalLinkDelete = '/system/externallink/delete',
  NewsPageList = '/system/getNewsPageList',
  NewsSaveOrUpdate = '/system/news/saveOrUpdate',
  NewsDelete = '/system/news/delete',
  NewsCategoryPageList = '/system/getNewsCategoryPageList',
  NewsCategorySaveOrUpdate = '/system/newscategory/saveOrUpdate',
  NewsCategoryDelete = '/system/newscategory/delete',
  FileCategoryPageList = '/system/getFileCategoryPageList',
  FileCategorySaveOrUpdate = '/system/filecategory/saveOrUpdate',
  FileCategoryDelete = '/system/filecategory/delete',
  FileAttachmentPageList = '/system/getFileAttachmentPageList',
  FileAttachmentSaveOrUpdate = '/system/fileattachment/saveOrUpdate',
  FileAttachmentDelete = '/system/fileattachment/delete',
  ModuleList = '/process/getProcessModuleList',
  Read = '/process/readXml',
  Deploy = '/process/deploy',
  Complete = '/newline/',
  getClassName = '/process/getClass',
  DeployedList = '/process/getDeployedPage',
  GetTasksListByUser = '/process/getTasksListByUser/',
  GetDoneListByUser = '/process/getDoneListByUser/',
  GetTasksListLimit5ByUser = '/process/getTasksListLimit5ByUser/',
  GetFlowsTask = '/process/getFlowsTask',
  GetComponent = '/process/component',
  GetComponentOnChange = '/process/component/onchange',
  GetApprovalRecord = '/process/getApproveRecord/',
  GetApprovalRecordByBussiness = '/process/getApproveRecordByBussinessKey/',
  GetLatestStep = '/process/getLatestStep/',
  GetNextNode = '/process/getNextNode',
  TerminateTask = '/process/terminateTask/',
}

export const moduleListApi = () => {
  return new Promise((resolve, reject) => {
    defHttp.get({ url: Api.ModuleList }).then((res) => {
      let rows = [];
      res.forEach((bpmn) => {
        rows.push({ name: bpmn.split('.bpmn')[0] });
      });

      resolve({
        items: rows,
        total: rows.length,
      });
    });
  });
};

export const read = (fileName) => {
  return defHttp.post({ url: Api.Read, params: { fileName } });
};

export const deployedList = (params) => {
  return defHttp.post({ url: Api.DeployedList, params });
};

export const complete = (pid, params, className) => {
  return defHttp.post({
    url: Api.Complete + `${className.toLowerCase()}/complete` + `/${pid}`,
    params,
  });
};

export const getProcessOne = (id, className) => {
  return defHttp.post({ url: `/newline/${className.toLowerCase()}/process/${id}` });
};

export const getClassName = (pid) => {
  return defHttp.get({ url: Api.getClassName + `/${pid}` });
};

export const getFlowsTask = (pid) => {
  return defHttp.get({ url: Api.GetFlowsTask + `/${pid}` });
};

export const getProcessComponent = (pid, name) => {
  return defHttp.get({ url: `/newline/${name.toLowerCase()}` + Api.GetComponent + `/${pid}` });
};

export const getProcessComponentOnChange = (pid, name, params) => {
  return defHttp.post({
    url: `/newline/${name.toLowerCase()}` + Api.GetComponentOnChange + `/${pid}`,
    params,
  });
};
export const getTasksListByUser = (page, form) => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.GetTasksListByUser + userName, params: { ...page, ...form } });
};

export const getDoneListByUser = (page, form) => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.GetDoneListByUser + userName, params: { ...page, ...form } });
};
export const getApprovalRecords = (key) => {
  return defHttp.get({ url: Api.GetApprovalRecord + `${key}` });
};

export const getApprovalRecordsByBussinessKey = (key) => {
  return defHttp.get({ url: Api.GetApprovalRecord + `${key}` });
};

export const getApprovalRecordsByBussiness = (key) => {
  return defHttp.get({ url: Api.GetApprovalRecordByBussiness + `${key}` });
};
export const GetLatestStep = (key) => {
  return defHttp.get({ url: Api.GetLatestStep + `${key}` });
};

export const getTasksListLimit5ByUser = () => {
  const userStore = useUserStore();
  const userName = userStore.getUserInfo.username;
  return defHttp.post({ url: Api.GetTasksListLimit5ByUser + userName });
};

export const deploy = (fileName) => {
  return defHttp.post({ url: Api.Deploy, params: { fileName } });
};

export const getNextNode = (name, id) => {
  return defHttp.get({ url: Api.GetNextNode + `/${name}/${id}` });
};

export const TerminateTask = (taskId) => {
  return defHttp.get({ url: Api.TerminateTask + taskId });
};
