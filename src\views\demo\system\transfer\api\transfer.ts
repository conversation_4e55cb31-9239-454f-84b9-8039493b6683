import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';
import { UploadApiResult } from '/@/api/sys/model/uploadModel';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  GetItems = '/newline/transfer/items/',
  GetOptions = '/newline/line/list',
  Issued = '/newline/transfer/issued',
  Upload = '/newline/transfer/upload/',
  Persist = '/newline/transfer/save/temp/',
  ItemRemove = '/newline/transfer/item/remove',
  ItemEdit = '/newline/transfer/item/edit',
  ItemAdd = '/newline/transfer/item/add',
  getTransferType = '/newline/transfer/getTransferType',
}

const globSetting = useGlobSetting();

export const getItemsApi = async (id) => {
  return await defHttp.get({ url: Api.GetItems + id });
};
export const getTransferTypeApi = async () => {
  return await defHttp.get({ url: Api.getTransferType });
};

export const getOptionsApi = async () => {
  return await defHttp.get({ url: Api.GetOptions });
};

export const issuedApi = async (id, params) => {
  return await defHttp.post({ url: Api.Issued + `/${id}`, params });
};

export const persistApi = async (key) => {
  return await defHttp.post({ url: Api.Persist + `${key}` });
};

export const itemRemove = async (id) => {
  return await defHttp.post({ url: Api.ItemRemove + `/${id}` });
};

export const itemEdit = async (params) => {
  return await defHttp.post({ url: Api.ItemEdit, params });
};

export const itemAdd = async (id, params) => {
  return await defHttp.post({ url: Api.ItemAdd + `/${id}`, params });
};

export const getUpLoadApi = (tplid) => {
  return (params: UploadFileParams, onUploadProgress: (progressEvent: ProgressEvent) => void) => {
    return defHttp.uploadFile<UploadApiResult>(
      {
        url: `${globSetting.apiUrl}${Api.Upload}${tplid}`,
        onUploadProgress,
      },
      params,
    );
  };
};
