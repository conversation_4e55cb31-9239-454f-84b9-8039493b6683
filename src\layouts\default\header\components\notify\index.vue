<template>
  <div :class="prefixCls">
    <Popover title="" trigger="click" :overlayClassName="`${prefixCls}__overlay`">
      <Badge :count="count" dot :numberStyle="numberStyle">
        <span style="display: flex; align-items: center">
          <BellOutlined /><span style="font-size: 12px; padding-right: 8px">待办</span>
        </span>
      </Badge>
      <template #content>
        <Tabs>
          <template v-for="item in listData" :key="item.key">
            <TabPane>
              <template #tab>
                {{ item.name }}
                <!--                <span v-if="item.list.length !== 0">({{ item.list.length }})</span>-->
              </template>
              <!-- 绑定title-click事件的通知列表中标题是“可点击”的-->
              <NoticeList
                style="width: 250px"
                :list="item.list"
                v-if="item.key === '1'"
                @title-click="onNoticeClick"
              />
              <NoticeList :list="item.list" @title-click="onNoticeClick" v-else />
            </TabPane>
          </template>
        </Tabs>
      </template>
    </Popover>

    <BasicModal @register="register" :showFooter="true" defaultFullscreen title="请办理">
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="详细信息">
          <Description
            class="mt-4"
            layout="vertical"
            :column="4"
            :data="descData"
            :schema="lookSchema"
          />
        </a-collapse-panel>
        <a-collapse-panel key="2" header="审批历史">
          <a-steps status="finish" direction="vertical" size="small" v-for="step in steps">
            <a-step :title="step.name">
              <template #description>
                <div>意见：{{ step.opinion }}</div>
                <div>办理人：{{ step.person }}</div>
                <p>办理时间：{{ step.createDateTime }}</p>
              </template>
            </a-step>
          </a-steps>
        </a-collapse-panel>
      </a-collapse>
      <v-form-render :form-json="formJson"></v-form-render>

      <a-card style="width: 100%; margin-top: 1%" title="审批处理">
        <!--        <VFormCreate
          :form-config="formJson"
          v-model:fApi="fApi"
          v-model:formModel="formModel"
          @submit="onSubmit"
        >
        </VFormCreate>-->
        <BasicForm style="height: 100%" @register="registerForm"></BasicForm>
      </a-card>

      <template #footer>
        <VxeButton type="primary" @click="approve">确定</VxeButton>
        <VxeButton type="primary" @click="cancel">取消</VxeButton>
      </template>
    </BasicModal>
  </div>
</template>
<script lang="ts">
  import { computed, defineComponent, onMounted, ref } from 'vue';
  import VFormCreate from '/@/views/form-design/components/VFormCreate/index.vue';
  import { Popover, Tabs, Badge, Steps, Card, Collapse, CollapsePanel } from 'ant-design-vue';
  import { BellOutlined } from '@ant-design/icons-vue';
  import { tabListData, ListItem } from './data';
  import NoticeList from './NoticeList.vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    complete,
    getApprovalRecords,
    getClassName,
    getFlowsTask,
    getProcessComponent,
    getProcessComponentOnChange,
    getProcessOne,
  } from '/@/api/process/process';
  import { useTaskStore } from '/@/store/modules/task';
  import { Description } from '/@/components/Description';
  import { BasicModal, useModal } from '/@/components/Modal';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { useForm } from '/@/components/Form';
  import { buildApi } from '/@/api/erupt/erupt';
  import { getComponent, getComponentProps } from '/@/components/EruptTable/componets';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  export default defineComponent({
    components: {
      BasicForm,
      BasicModal,
      Description,
      Popover,
      BellOutlined,
      Tabs,
      TabPane: Tabs.TabPane,
      [Steps.name]: Steps,
      [Steps.Step.name]: Steps.Step,
      [Card.name]: Card,
      [Collapse.name]: Collapse,
      [CollapsePanel.name]: CollapsePanel,
      Badge,
      NoticeList,
      VFormCreate,
    },
    setup() {
      const { prefixCls } = useDesign('header-notify');
      const pid = ref();
      const processInstanceId = ref('');
      const { createMessage } = useMessage();
      const lookSchema = ref([]);
      const taskSchema = ref([]);
      const [register, { openModal, closeModal }] = useModal();
      const userStore = useUserStore();
      const activeKey = ref('1');
      const acts = ref([]);
      const cName = ref('');
      const step = ref('');
      const steps = ref([]);
      const descData = ref([]);
      const current = ref(0);
      const router = useRouter();
      const bussinesKey = ref('');
      const formModel = ref({});
      const formJson = ref();
      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        schemas: taskSchema,
        showActionButtonGroup: false,
      });

      // const listData = ref([]);
      const taskStore = useTaskStore();

      let timer: any = '';

      function actionChange(e) {
        console.log(e, step.value);
        getProcessComponentOnChange(pid.value, cName.value, { option: e ? e : '' }).then((res) => {
          if (res) {
            res.forEach((item) => {
              if (item.field == 'option') item.componentProps.onChange = (e) => actionChange(e);
            });
            taskSchema.value = res;
          }
        });
      }
      const polling = () => {
        timer = setInterval(() => {
          setTimeout(() => {
            console.log('轮询');
            taskStore.setTaskList();
          }, 0);
        }, 60 * 10000);
      };
      const listData = computed(() => taskStore.getTaskList);
      onMounted(() => {
        if (userStore.getUserInfo.username) {
          taskStore.setTaskList();
          polling();
        }
      });
      const count = computed(() => {
        let count = 0;
        count =
          taskStore.taskList[0] && taskStore.taskList[0].list.length > 1
            ? taskStore.taskList[0].list.length - 1
            : 0;
        return count;
      });

      function onNoticeClick(record: ListItem) {
        if (record.id != 'more') {
          step.value = record.step;
          pid.value = record.id;
          processInstanceId.value = record.processInstanceId;
          //step.value = record.name
          pid.value = record.id;
          let schema = [];

          /*getFlowsTask(record.id).then(res => {
          let opts = []
          res.forEach(item => {
            if (item)
              opts.push({label:item,value:item})
          })

          acts.value = res
          schema.push({
            "field": "option",
            "label": "处理方式",
            "required": true,
            "component": "Select",
            "colProps": { "span": 16 },
            "componentProps": {
              "options": opts,
              "onChange": (e) => {
                actionChange(e)
              }
            },
          })
          schema.push({
            "field": "message",
            "label": "备注",
            "required": true,
            "component": "InputTextArea",
            "colProps": {"span": 24},
          })
          taskSchema.value = schema
          console.log('actions',acts)
        })*/
          getClassName(record.id).then(async (res) => {
            if (res) {
              bussinesKey.value = res;
              const arr = res.split('.');
              const className = arr[0];
              cName.value = className;
              const id = arr[1];
              getProcessComponent(record.id, className).then((res) => {
                console.log('component', res);
                if (res) {
                  res.forEach((item) => {
                    if (item.field == 'option')
                      item.componentProps.onChange = (e) => actionChange(e);
                  });
                  taskSchema.value = res;
                }
              });
              buildApi(className).then(async (res) => {
                const {
                  eruptModel: { eruptFieldModels, eruptJson },
                  tabErupts,
                  power,
                } = res;

                let details = [];
                eruptFieldModels.forEach((item) => {
                  const key = item.fieldName;
                  const title = item.eruptFieldJson.edit.title;

                  //  formState[key] = null
                  if (item.eruptFieldJson.edit.show.detail_show && key !== 'id') {
                    if (
                      item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
                      item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
                      item.eruptFieldJson.edit.type !== 'TAB_TREE'
                    ) {
                      const d = {
                        field: key,
                        label: title,
                        component: getComponent(
                          item.eruptFieldJson.edit.type,
                          item.eruptFieldJson.edit,
                        ),
                        componentProps: (form) => {
                          return {
                            ...getComponentProps(item, className),
                            readonly: true,
                            style: {
                              color: '#303030',
                              background: '#fff',
                              pointerEvents:
                                item.eruptFieldJson.edit.type == 'ATTACHMENT' ? 'auto' : 'none',
                            },
                          };
                        },
                        required: item.eruptFieldJson.edit.notNull,
                        colProps: {
                          span: item.eruptFieldJson.edit.colSpan,
                        },
                      };
                      details.push(d);
                    }
                  }
                });
                /*details.push({
                field: 'message',
                label: '备注',
                required: true,
                component: 'InputTextArea',
                colProps: {span: 24},
              })*/
                lookSchema.value = details;

                const entity = await getProcessOne(id, className);
                descData.value = entity;

                //gridOptions.columns = columns
                //search()
              });
              const records = await getApprovalRecords(record.processInstanceId);
              steps.value = records;
              current.value = records.length;
              openModal();
            }
          });
        } else {
          router.push('/todo');
        }
      }

      return {
        onClick: () => {
          console.log(listData.value);
        },
        cancel: () => {
          resetFields();
          closeModal();
        },
        approve: async () => {
          let values = await validate();
          const params = {
            step: step.value,
            ...values,
            bussinesKey: bussinesKey.value,
            processInstanceId: processInstanceId.value,
          };
          complete(pid.value, params, cName.value).then((res) => {
            if (res == 200) {
              createMessage.success('办理完成');
              resetFields();
            }
            taskStore.setTaskList();
            closeModal();
          });
        },
        prefixCls,
        listData,
        cName,
        step,
        pid,
        count,
        onNoticeClick,
        register,
        openModal,
        closeModal,
        registerForm,
        descData,
        activeKey,
        formJson,
        onSubmit: () => {},
        fApi: () => {},
        formModel,
        acts,
        lookSchema,
        taskSchema,
        steps,
        setFieldsValue,
        numberStyle: {},
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-notify';

  .@{prefix-cls} {
    padding-top: 2px;

    &__overlay {
      max-width: 360px;
    }

    .ant-tabs-content {
      width: 300px;
    }

    .ant-badge {
      font-size: 18px;

      .ant-badge-multiple-words {
        padding: 0 4px;
      }

      svg {
        width: 0.9em;
      }
    }
  }
</style>
