import { BasicColumn, FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { uploadApi } from '/@/api/demo/NewscategoryUpload';

export const columns: BasicColumn[] = [
  {
    title: '新闻栏目名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '新闻栏目描述',
    dataIndex: 'description',
    width: 300,
  },
  {
    title: '栏目图片地址',
    dataIndex: 'imgUrl',
    width: 200,
  },
  {
    title: '系统栏目',
    dataIndex: 'system',
    width: 80,
    customRender: ({ record }) => {
      const system = record.system;
      const enable = ~~system === 0;
      const color = enable ? 'green' : 'blue';
      const text = enable ? '系统' : '自定义';
      return h(Tag, { color: color }, () => text);
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '新闻栏目名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'system',
    label: '是否系统栏目',
    component: 'Select',
    componentProps: {
      options: [
        { label: '系统', value: 0 },
        { label: '自定义', value: 1 },
      ],
    },
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    required: false,
    component: 'Input',
    show: false,
  },
  {
    field: 'name',
    label: '新闻栏目名称',
    required: true,
    component: 'Input',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'description',
    label: '新闻栏目描述',
    required: false,
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'imgUrl',
    label: '图片',
    required: false,
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'file',
    label: '栏目图片地址',
    required: false,
    component: 'Upload',
    componentProps: ({ formModel }) => {
      return {
        api: uploadApi,
        maxSize: 10,
        onChange: (list: string[]) => {
          console.log(JSON.stringify(list));
          // or
          formModel.imgUrl = list[0];
        },
      };
    },
    colProps: {
      span: 12,
    },
    suffix: '最佳图片分辨率 306*237 px',
  },
  {
    field: 'system',
    label: '是否系统栏目',
    required: true,
    component: 'RadioGroup',
    defaultValue: 0,
    componentProps: {
      options: [
        { label: '系统', value: 0 },
        { label: '自定义', value: 1 },
      ],
    },
  },
];
