<template>
  <div>
    <div>
      <AssemblyByLineOne
        :loading="loading"
        :line-opt="lineOptions"
        :dept-opt="deptOptions"
        class="enter-y"
      />
    </div>
    <div>
      <AssemblyByDeptOne
        :loading="loading"
        :line-opt="lineOptions"
        :dept-opt="deptOptions"
        class="enter-y"
      />
    </div>

    <div>
      <AssemblyByDeptAndLineOne
        :loading="loading"
        :line-opt="lineOptions"
        :dept-opt="deptOptions"
        class="enter-y"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, ref } from 'vue';
  import { useLineStoreWithOut } from '/@/store/modules/line';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  import { useDeptStoreWithOut } from '/@/store/modules/dept';
  import AssemblyByLineOne from '/@/views/dashboard/workbench/components/AssemblyByLineOne.vue';
  import AssemblyByDeptOne from '/@/views/dashboard/workbench/components/AssemblyByDeptOne.vue';
  import AssemblyByDeptAndLineOne from '/@/views/dashboard/workbench/components/AssemblyByDeptAndLineOne.vue';

  const loading = ref(true);
  const lineOptions = ref([]);
  const deptOptions = ref([]);
  const lineStore = useLineStoreWithOut();
  const deptStore = useDeptStoreWithOut();
  const userStore = useUserStore();
  const router = useRouter();
  const isModalVisible = ref(false);

  onMounted(() => {
    Promise.all([deptStore.getDeptOptions(), lineStore.getLineOptions()]).then(
      ([deptRes, lineRes]) => {
        deptOptions.value = deptRes;
        lineOptions.value = lineRes;
        loading.value = false;
      },
    );
  });

  interface MeetingItem {
    id?: number;
    title: string;
    meetingDate: string;
    startMeetingDate: string;
    location: string;
    participants?: string[];
    departments?: string[];
    isModified: boolean;
    updateDateTime?: string;
    createDateTime?: string;
  }

  const handleOk = () => {
    isModalVisible.value = false;
  };

  const handleCancel = () => {
    isModalVisible.value = false;
  };
</script>
<style scoped lang="less">
  .table-container {
    max-height: 60vh;
    overflow-y: auto;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  }

  .meeting-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    border-left: 3px solid #e8e8e8;
    border-right: 3px solid #e8e8e8;

    th,
    td {
      padding: 12px 16px;
      border: 1px solid #e8e8e8;
      text-align: left;
      background-color: white;
    }

    th {
      background-color: #fafafa;
      font-weight: 600;
      position: sticky;
      top: 0;
      z-index: 1;
      color: rgba(0, 0, 0, 0.85);
    }

    tbody {
      display: block;
      max-height: 50vh;
      overflow-y: auto;
    }

    thead,
    tbody tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }
  }

  .cell-content {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: rgba(0, 0, 0, 0.85);
  }

  // 列宽设置保持原样
  .title-col {
    width: 100px;
    max-width: 150px;
  }

  .location-col {
    width: 100px;
    max-width: 150px;
  }

  .time-col {
    width: 100px;
    max-width: 150px;
  }

  .participants-col {
    width: 120px;
    max-width: 200px;
  }

  .status-col {
    width: 60px;
    max-width: 100px;
  }

  .past-time {
    td {
      background-color: #fafafa !important;

      .cell-content {
        color: #bfbfbf !important;
      }
    }

    .ant-tag {
      background: #f5f5f5 !important;
      border-color: #d9d9d9 !important;
      color: #bfbfbf !important;
    }
  }

  .new-row {
    td {
      background-color: #f6ffed !important;
    }
  }

  .modified-row {
    td {
      background-color: #fffbe6 !important;
    }
  }

  /* 滚动条优化 */
  .table-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .table-container::-webkit-scrollbar-track {
    background: #f8f8f8;
    border-radius: 4px;
  }

  .table-container::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 4px;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0 0;

    .ant-btn {
      min-width: 80px;
      border-radius: 4px;
      font-weight: 500;
    }
  }
</style>
