# Whether to open mock
VITE_USE_MOCK=false

# public path
VITE_PUBLIC_PATH=/

# Cross-domain proxy, you can configure multiple
# Please note that no line breaks
# VITE_PROXY = [["/basic-api","http://localhost:3000"],["/upload","http://localhost:3300/upload"]]
# VITE_PROXY=[["/api","https://vvbin.cn/test"]]

# Delete console
VITE_DROP_CONSOLE=false

# Basic interface address SPA
VITE_GLOB_API_URL=http://127.0.0.1:9000/basic-api
VITE_REPORT_BASE=http://127.0.0.1:9000
VITE_GLOB_ERUPT_URL=http://127.0.0.1:9000/erupt-api

VITE_GLOB_ERUPT_ATTACHMENT=http://127.0.0.1:9000/erupt-attachment
# File upload address， optional
VITE_GLOB_UPLOAD_URL=http://127.0.0.1:9000/basic-api/upload
# File Preview address
VITE_GLOB_FILE_PREVIEW_URL=http://*************:8012

# Interface prefix
VITE_GLOB_API_URL_PREFIX=
VITE_GLOB_FULL_SCREEN_URL=http://127.0.0.1:3000/
