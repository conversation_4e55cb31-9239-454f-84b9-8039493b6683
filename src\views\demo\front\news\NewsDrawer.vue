<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    :title="getTitle"
    width="50%"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
  import { defineComponent, ref, computed, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './news.data';
  import { formSchema1 } from './newsrole.data';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { newsSaveOrUpdate } from '/@/api/demo/front';
  import { usePermissionStore } from '/@/store/modules/permission';

  export default defineComponent({
    name: 'NewsDrawer',
    components: { BasicDrawer, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const isUpdate = ref(true);

      const permissionStore = usePermissionStore();

      const NEWS = 'news';

      console.log(permissionStore.getPermCodeList);

      console.log(permissionStore.getPermCodeList.includes(NEWS));

      const [registerForm, { resetFields, setFieldsValue, validate }] = useForm(
        permissionStore.getPermCodeList.includes(NEWS)
          ? {
              labelWidth: 100,
              schemas: formSchema1,
              showActionButtonGroup: false,
            }
          : {
              labelWidth: 100,
              schemas: formSchema,
              showActionButtonGroup: false,
            },
      );

      const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
        resetFields();
        setDrawerProps({ confirmLoading: false });
        isUpdate.value = !!data?.isUpdate;

        if (unref(isUpdate)) {
          setFieldsValue({
            ...data.record,
          });
        }
        //updateSchema({});
      });

      const getTitle = computed(() => (!unref(isUpdate) ? '新增新闻' : '编辑新闻'));

      async function handleSubmit() {
        try {
          const values = await validate();
          setDrawerProps({ confirmLoading: true });
          // TODO custom api
          console.log(values);
          const result = await newsSaveOrUpdate(values);
          console.log(result);

          closeDrawer();
          emit('success');
        } finally {
          setDrawerProps({ confirmLoading: false });
        }
      }

      return { registerDrawer, registerForm, getTitle, handleSubmit };
    },
  });
</script>
