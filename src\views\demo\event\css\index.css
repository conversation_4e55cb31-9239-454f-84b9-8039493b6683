.page {
    background-color: rgba(255, 255, 255, 1);
    position: relative;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
}

.box_1 {
    width: 1920px;
    height: 88px;
}

.group_1 {
    width: 253px;
    height: 41px;
    margin: 24px 0 0 26px;
}

.label_1 {
    width: 42px;
    height: 41px;
}

.text_1 {
    width: 200px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(112, 112, 112, 1);
    font-size: 20px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin-top: 11px;
}

.group_2 {
    width: 1920px;
    height: 1px;
    border: 1px solid rgba(112, 112, 112, 0.3100000023841858);
    margin: 21px 0 1px 0;
}

.box_2 {
    background-color: rgba(242, 242, 242, 1);
    height: 992px;
    width: 1648px;
    position: relative;
    margin: -1px 0 0 271px;
}

.box_3 {
    width: 1072px;
    height: 188px;
    margin: 13px 0 0 17px;
}

.group_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 4px;
    height: 188px;
    width: 1072px;
}

.box_4 {
    height: 188px;
    background: url(https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNGfb2307e0d1be8b524dcf7a1086fe73f4.png)
    100% no-repeat;
    background-size: 100% 100%;
    width: 1072px;
}

.text-wrapper_1 {
    width: 200px;
    height: 27px;
    margin: 10px 0 0 20px;
}

.text_2 {
    width: 200px;
    height: 27px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 18px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 25px;
}

.box_5 {
    width: 1047px;
    height: 40px;
    margin: 43px 0 68px 5px;
}

.image-wrapper_1 {
    background-color: rgba(0, 0, 0, 0.5);
    height: 40px;
    width: 40px;
}

.thumbnail_1 {
    width: 8px;
    height: 14px;
    margin: 14px 0 0 15px;
}

.thumbnail_2 {
    width: 8px;
    height: 14px;
    margin-top: 14px;
}

.box_6 {
    width: 1616px;
    height: 768px;
    margin: 16px 0 7px 17px;
}

.block_1 {
    width: 1072px;
    height: 768px;
}

.block_2 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 4px;
    width: 1072px;
    height: 428px;
}

.box_7 {
    width: 919px;
    height: 51px;
    margin: 18px 0 0 17px;
}

.group_4 {
    background-color: rgba(59, 126, 240, 1);
    width: 4px;
    height: 18px;
}

.text_3 {
    width: 64px;
    height: 16px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 16px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 16px;
    margin: 1px 0 0 8px;
}

.text_4 {
    width: 77px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(0, 150, 81, 1);
    font-size: 20px;
    font-family: D-DIN-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin: 31px 0 0 353px;
}

.group_5 {
    background-color: rgba(53, 121, 246, 1);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    border: 3px solid rgba(204, 222, 255, 1);
    margin: 20px 0 0 157px;
}

.text-wrapper_2 {
    width: 232px;
    height: 45px;
    margin: 2px 0 0 8px;
}

.text_5 {
    width: 66px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(59, 126, 240, 1);
    font-size: 20px;
    font-family: D-DIN-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
}

.text_6 {
    width: 232px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 14px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin-top: 5px;
}

.box_8 {
    width: 823px;
    height: 100px;
    margin-left: 185px;
}

.text-wrapper_3 {
    width: 338px;
    height: 95px;
    margin-top: 5px;
}

.text_7 {
    width: 98px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 14px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin-left: 240px;
}

.text_8 {
    width: 338px;
    height: 71px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 12px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    line-height: 17px;
    margin-top: 4px;
}

.text_9 {
    width: 304px;
    height: 49px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 12px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    line-height: 17px;
}

.box_9 {
    position: relative;
    width: 1057px;
    height: 266px;
    background: url(https://lanhu-oss-2537-2.lanhuapp.com/MasterDDSSlicePNG9a132ec66ada1e53b60b578e83953f19.png)
    100% no-repeat;
    background-size: 100% 100%;
    margin: -6px 0 1px 0;
}

.image-text_1 {
    width: 336px;
    height: 134px;
    margin: 83px 0 0 70px;
}

.text-group_1 {
    width: 317px;
    height: 120px;
}

.text_10 {
    width: 80px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(109, 106, 186, 1);
    font-size: 20px;
    font-family: D-DIN-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin-left: 237px;
}

.text_11 {
    width: 98px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 14px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin: 5px 0 0 218px;
}

.text_12 {
    width: 316px;
    height: 71px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 12px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    line-height: 17px;
    margin-top: 4px;
}

.image_1 {
    width: 16px;
    height: 132px;
    margin-top: 2px;
}

.image-text_2 {
    width: 288px;
    height: 140px;
    margin: 74px 0 0 74px;
}

.text-group_2 {
    width: 269px;
    height: 120px;
}

.text_13 {
    width: 80px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(240, 88, 46, 1);
    font-size: 20px;
    font-family: D-DIN-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin-left: 189px;
}

.text_14 {
    width: 98px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 14px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin: 5px 0 0 170px;
}

.text_15 {
    width: 268px;
    height: 71px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 12px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    line-height: 17px;
    margin-top: 4px;
}

.image_2 {
    width: 16px;
    height: 138px;
    margin-top: 2px;
}

.image-text_3 {
    width: 253px;
    height: 62px;
    margin: 18px -14px 0 50px;
}

.group_6 {
    width: 16px;
    height: 16px;
    margin-top: 2px;
}

.text-group_3 {
    width: 232px;
    height: 62px;
}

.text_16 {
    width: 74px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(219, 144, 70, 1);
    font-size: 20px;
    font-family: D-DIN-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
}

.text_17 {
    width: 232px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 14px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin-top: 5px;
}

.text_18 {
    width: 232px;
    height: 14px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 12px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 17px;
    margin-top: 3px;
}

.image_3 {
    position: absolute;
    left: 529px;
    top: -112px;
    width: 16px;
    height: 162px;
}

.block_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 4px;
    height: 324px;
    margin-top: 16px;
    width: 1072px;
}

.box_10 {
    width: 76px;
    height: 17px;
    margin: 15px 0 0 16px;
}

.group_7 {
    background-color: rgba(59, 126, 240, 1);
    width: 4px;
    height: 15px;
}

.text_19 {
    width: 64px;
    height: 16px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 16px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 16px;
    margin-top: 1px;
}

.box_11 {
    width: 1060px;
    height: 280px;
    margin: 8px 0 4px 6px;
}

.box_12 {
    background-color: rgba(255, 255, 255, 1);
    width: 350px;
    height: 280px;
    border: 1px solid rgba(243, 244, 246, 1);
}

.image-text_4 {
    width: 348px;
    height: 277px;
    margin: 1px 0 0 1px;
}

.image_4 {
    width: 348px;
    height: 192px;
}

.text-group_4 {
    width: 300px;
    height: 81px;
    margin: 4px 0 0 24px;
}

.text_20 {
    width: 196px;
    height: 28px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-family: Roboto-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 28px;
}

.text_21 {
    width: 300px;
    height: 36px;
    overflow-wrap: break-word;
    color: rgba(75, 85, 99, 1);
    font-size: 10px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    line-height: 18px;
    margin-top: -2px;
}

.text_22 {
    width: 61px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(107, 114, 128, 1);
    font-size: 12px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin-top: -1px;
}

.box_13 {
    background-color: rgba(255, 255, 255, 1);
    width: 350px;
    height: 280px;
    border: 1px solid rgba(243, 244, 246, 1);
    margin-left: 5px;
}

.image-text_5 {
    width: 348px;
    height: 277px;
    margin: 1px 0 0 1px;
}

.image_5 {
    width: 348px;
    height: 192px;
}

.text-group_5 {
    width: 300px;
    height: 81px;
    margin: 4px 0 0 25px;
}

.text_23 {
    width: 213px;
    height: 28px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-family: Roboto-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 28px;
}

.text_24 {
    width: 300px;
    height: 36px;
    overflow-wrap: break-word;
    color: rgba(75, 85, 99, 1);
    font-size: 10px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    line-height: 18px;
    margin-top: -2px;
}

.text_25 {
    width: 61px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(107, 114, 128, 1);
    font-size: 12px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin-top: -1px;
}

.box_14 {
    background-color: rgba(255, 255, 255, 1);
    width: 350px;
    height: 280px;
    border: 1px solid rgba(243, 244, 246, 1);
    margin-left: 5px;
}

.image-text_6 {
    width: 348px;
    height: 277px;
    margin: 1px 0 0 1px;
}

.image_6 {
    width: 348px;
    height: 192px;
}

.text-group_6 {
    width: 300px;
    height: 82px;
    margin: 3px 0 0 24px;
}

.text_26 {
    width: 162px;
    height: 28px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-family: Roboto-Medium;
    font-weight: 500;
    text-align: left;
    line-height: 28px;
}

.text_27 {
    width: 300px;
    height: 36px;
    overflow-wrap: break-word;
    color: rgba(75, 85, 99, 1);
    font-size: 10px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    line-height: 18px;
    margin-top: -1px;
}

.text_28 {
    width: 61px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(107, 114, 128, 1);
    font-size: 12px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin-top: -1px;
}

.block_4 {
    width: 528px;
    height: 768px;
}

.box_15 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 4px;
    height: 272px;
    width: 528px;
}

.group_8 {
    width: 130px;
    height: 18px;
    margin: 18px 0 0 16px;
}

.box_16 {
    background-color: rgba(59, 126, 240, 1);
    width: 4px;
    height: 18px;
}

.text_29 {
    width: 118px;
    height: 16px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 16px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 16px;
    margin-top: 1px;
}

.group_9 {
    width: 495px;
    height: 24px;
    margin: 24px 0 0 25px;
}

.image-text_7 {
    width: 495px;
    height: 24px;
}

.box_17 {
    width: 18px;
    height: 20px;
    margin-top: 2px;
}

.text-group_7 {
    width: 470px;
    height: 24px;
    overflow-wrap: break-word;
    color: rgba(75, 85, 99, 1);
    font-size: 16px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 24px;
}

.text-wrapper_4 {
    width: 470px;
    height: 24px;
    margin: 6px 0 0 50px;
}

.text_30 {
    width: 470px;
    height: 24px;
    overflow-wrap: break-word;
    color: rgba(75, 85, 99, 1);
    font-size: 16px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 24px;
}

.text-wrapper_5 {
    width: 470px;
    height: 24px;
    margin: 6px 0 0 50px;
}

.text_31 {
    width: 470px;
    height: 24px;
    overflow-wrap: break-word;
    color: rgba(75, 85, 99, 1);
    font-size: 16px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 24px;
}

.text-wrapper_6 {
    width: 470px;
    height: 24px;
    margin: 6px 0 0 50px;
}

.text_32 {
    width: 470px;
    height: 24px;
    overflow-wrap: break-word;
    color: rgba(75, 85, 99, 1);
    font-size: 16px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 24px;
}

.text-wrapper_7 {
    width: 470px;
    height: 24px;
    margin: 6px 0 0 50px;
}

.text_33 {
    width: 470px;
    height: 24px;
    overflow-wrap: break-word;
    color: rgba(75, 85, 99, 1);
    font-size: 16px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 24px;
}

.text-wrapper_8 {
    width: 470px;
    height: 24px;
    margin: 6px 0 38px 50px;
}

.text_34 {
    width: 470px;
    height: 24px;
    overflow-wrap: break-word;
    color: rgba(75, 85, 99, 1);
    font-size: 16px;
    font-family: Roboto-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 24px;
}

.box_18 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 4px;
    width: 528px;
    height: 480px;
    margin-top: 16px;
}

.group_10 {
    width: 108px;
    height: 18px;
    margin: 18px 0 0 16px;
}

.box_19 {
    background-color: rgba(59, 126, 240, 1);
    width: 4px;
    height: 18px;
}

.text_35 {
    width: 96px;
    height: 16px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 16px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 16px;
    margin-top: 1px;
}

.image_7 {
    width: 500px;
    height: 420px;
    margin: 14px 0 10px 14px;
}

.box_20 {
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    left: 1084px;
    top: 93px;
    width: 40px;
    height: 40px;
}

.box_21 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 4px;
    height: 188px;
    width: 528px;
    position: absolute;
    left: 1105px;
    top: 13px;
}

.text-wrapper_9 {
    width: 96px;
    height: 16px;
    margin: 18px 0 0 18px;
}

.text_36 {
    width: 96px;
    height: 16px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 16px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 16px;
}

.box_22 {
    width: 490px;
    height: 120px;
    margin: 14px 0 20px 19px;
}

.group_11 {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    width: 150px;
    height: 120px;
}

.text-wrapper_10 {
    width: 45px;
    height: 1px;
    overflow-wrap: break-word;
    font-size: 0;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 40px;
    margin: 38px 0 0 45px;
}

.text_37 {
    width: 45px;
    height: 1px;
    overflow-wrap: break-word;
    color: rgba(59, 126, 240, 1);
    font-size: 40px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 40px;
}

.text_38 {
    width: 45px;
    height: 1px;
    overflow-wrap: break-word;
    color: rgba(59, 126, 240, 1);
    font-size: 20px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
}

.text_39 {
    width: 53px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 20px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin: 31px 0 30px 37px;
}

.group_12 {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    width: 150px;
    height: 120px;
    margin-left: 20px;
}

.text-wrapper_11 {
    width: 45px;
    height: 1px;
    overflow-wrap: break-word;
    font-size: 0;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 40px;
    margin: 38px 0 0 45px;
}

.text_40 {
    width: 45px;
    height: 1px;
    overflow-wrap: break-word;
    color: rgba(59, 126, 240, 1);
    font-size: 40px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 40px;
}

.text_41 {
    width: 45px;
    height: 1px;
    overflow-wrap: break-word;
    color: rgba(59, 126, 240, 1);
    font-size: 20px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
}

.text_42 {
    width: 53px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(73, 73, 73, 1);
    font-size: 20px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin: 31px 0 30px 37px;
}

.group_13 {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    width: 150px;
    height: 120px;
    margin-left: 20px;
}

.text-wrapper_12 {
    width: 29px;
    height: 1px;
    overflow-wrap: break-word;
    font-size: 0;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 40px;
    margin: 38px 0 0 53px;
}

.text_43 {
    width: 29px;
    height: 1px;
    overflow-wrap: break-word;
    color: rgba(59, 126, 240, 1);
    font-size: 40px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 40px;
}

.text_44 {
    width: 29px;
    height: 1px;
    overflow-wrap: break-word;
    color: rgba(59, 126, 240, 1);
    font-size: 20px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
}

.text_45 {
    width: 53px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 20px;
    font-family: Source Han Sans-Bold;
    font-weight: 700;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin: 31px 0 30px 37px;
}

.box_23 {
    width: 1920px;
    height: 2px;
    margin-bottom: 1px;
}

.group_14 {
    background-color: rgba(0, 21, 41, 1);
    width: 270px;
    height: 994px;
    margin: -993px 0 0 2px;
}
