import type { ErrorMessageMode } from '/#/axios';
import { defineStore } from 'pinia';
import { store } from '/@/store';
import { RoleEnum } from '/@/enums/roleEnum';
import { PageEnum } from '/@/enums/pageEnum';
import { ROLES_KEY, TOKEN_KEY, USER_INFO_KEY } from '/@/enums/cacheEnum';
import { getAuthCache, setAuthCache } from '/@/utils/auth';
import { GetUserInfoModel, LoginParams } from '/@/api/sys/model/userModel';
import { doLogout, getUserInfo, loginApi } from '/@/api/sys/user';
import { useI18n } from '/@/hooks/web/useI18n';
import { useMessage } from '/@/hooks/web/useMessage';
import { router } from '/@/router';
import { usePermissionStore } from '/@/store/modules/permission';
import { RouteRecordRaw } from 'vue-router';
import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';
import { isArray } from '/@/utils/is';
import { h } from 'vue';
import {getTasksListLimit5ByUser} from "/@/api/process/process";

interface taskState {
  taskList:[];
}

export const useTaskStore = defineStore({
  id: 'app-task',
  state: (): taskState => ({
    // user info
    taskList: [],
  }),
  getters: {
    getTaskList(state) {
      return state.taskList
    }
  },
  actions: {
    setTaskList() {
      getTasksListLimit5ByUser().then(res => {
        console.log('notify',res)
        const notifys = []
        let list = []
        res.forEach(item => {
          list.push({
            id: item.id,
            avatar: '',
            title:item.defName,
            processInstanceId: item.processInstanceId,
            step: item.name,
            description:  '当前步骤：' + item.name,
            extra: '待处理',
            datetime: item.createTime,
            type: '1',
          })
        })
        list.push({
          id: 'more',
          title: '更多...'
        })
        let daban = {
          key: '1',
          name: '待办',
          list: list
        }
        notifys.push(daban)
        this.taskList = notifys

      })
    },

  },
});

// Need to be used outside the setup
export function useTaskStoreWithOut() {
  return useTaskStore(store);
}
