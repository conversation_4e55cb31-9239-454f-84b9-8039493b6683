<template>
  <Card title="运营汇编单线路完成率" :loading="loading">
    <template #extra>
      <a-space direction="horizontal">
        <!--                <a-select
          v-model="line"
          style="width: 200px"
          placeholder="请选择线路"
          :options="lineOpt"
          @change="setLine"
          :loading="loading"
          :disabled="loading"
          allowClear
        ></a-select>-->
        <a-select
          v-model="dept"
          style="width: 200px"
          placeholder="请选择部门"
          :options="deptOpt"
          @change="setDept"
          :loading="loading"
          :disabled="loading"
          allowClear
        ></a-select>
        <a-button type="primary" @click="showModal">详情</a-button>
      </a-space>
    </template>
    <div v-show="isShow > 0" v-loading="loadings" ref="chartRef" :style="{ width, height }"></div>
    <div v-show="isShow <= 0" style="text-align: center; margin-top: 100px; height: 300px">
      <a-empty />
    </div>
    <a-modal
      v-model:visible="visible"
      title="图表详情"
      @ok="handleOk"
      :width="'85%'"
      :style="{ top: '100px', left: '0', right: '0', margin: '0 auto' }"
    >
      <div ref="modalChartRef" :style="{ width: '100%', height: '650px' }"></div>
    </a-modal>
  </Card>
</template>

<script setup lang="ts">
  import { Card, Modal, Button } from 'ant-design-vue';
  import { Ref, ref, watch } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { useUserStore } from '/@/store/modules/user';
  import { getAssemblyByLine } from '/@/api/demo/home';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    loading: Boolean,
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: '400px',
    },
    lineOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    deptOpt: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });
  const line = ref('');
  const isShow = ref(0);
  const dept = ref('');
  const deptLabel = ref('');
  const loadings = ref(true);
  const chartRef = ref<HTMLDivElement | null>(null);
  const modalChartRef = ref<HTMLDivElement | null>(null);
  const { setOptions: setMainChartOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const { setOptions: setModalChartOptions } = useECharts(modalChartRef as Ref<HTMLDivElement>);
  const userStore = useUserStore();
  const router = useRouter();
  const visible = ref(false);
  let chartData: any = null;
  const status = ref('0'); // 添加 status 引用

  const setLine = (val) => {
    loadings.value = true;
    line.value = val;
    getChartDate();
  };
  const setDept = (val) => {
    loadings.value = true;
    const selectedOption = props.deptOpt.find((option) => option.value === val);
    if (selectedOption) {
      deptLabel.value = selectedOption.label;
    }
    dept.value = val;
    getChartDate();
  };

  watch(
    () => props.loading,
    () => {
      if (props.loading) {
        return;
      }
      getChartDate();
    },
    { immediate: true },
  );

  watch(
    () => visible.value,
    (newVal) => {
      if (newVal && chartData) {
        updateModalChart(chartData);
      }
    },
  );

  const getChartDate = async () => {
    const data = {
      line: line.value,
      dept: dept.value,
      status: status.value, // 使用 status 的值
    };
    try {
      const res = await getAssemblyByLine(data);
      chartData = res; // 保存数据以便在弹窗显示时使用
      if (data.status === '0') {
        updateChart(res);
      } else {
        updateModalChart(res);
      }
      isShow.value = res.line.length - 1;
    } catch (error) {
      console.log(error);
    } finally {
      loadings.value = false; // 确保在请求结束后将 loadings 设置为 false
    }
  };

  function updateChart(value) {
    // 处理 value.line，截取最后一个 _ 后的字符串作为颜色，并去掉 _ 和其后的字符串
    const processedLine = value.line.map((item) => {
      const parts = item.split('_');
      let colorPart = '#ED868C'; // 默认颜色
      let displayPart = item;
      if (parts.length > 1) {
        colorPart = parts.pop(); // 截取最后一个 _ 后的字符串作为颜色
        displayPart = parts.join('_'); // 去掉 _ 和其后的字符串
      }
      return { display: displayPart, color: colorPart };
    });

    setMainChartOptions({
      xAxis: {
        type: 'category',
        data: processedLine.map((item) => item.display), // 使用处理后的 display 部分
        name: '线路',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#000',
          fontSize: 14,
        },
        axisLabel: {
          interval: 0,
          rotate: 0,
          overflow: 'break',
          formatter: function (value) {
            const lines = [];
            for (let i = 0; i < value.length; i += 5) {
              lines.push(value.slice(i, i + 5));
            }
            return lines.join('\n');
          },
        },
      },
      yAxis: {
        name: '完成率',
        nameLocation: 'end',
        nameGap: 21,
        nameTextStyle: {
          color: '#333',
          fontSize: 14,
          fontWeight: 'bold',
          backgroundColor: '#f8f8f8', // 添加背景色
          borderColor: '#d9d9d9', // 边框颜色
          borderWidth: 1, // 边框宽度
          borderRadius: 4, // 圆角边框
          padding: [4, 8], // 内边距
        },
        axisLine: {
          show: true, // 显示轴线
          lineStyle: {
            color: '#666', // 轴线颜色
            width: 1, // 轴线宽度
          },
        },
        axisLabel: {
          formatter: function (value) {
            return value + '%';
          },
        },
      },
      grid: {
        left: '10%',
        right: '12%',
        top: '10%',
        bottom: '10%',
      },
      tooltip: {
        valueFormatter: function (value) {
          return parseFloat(value.toFixed(2)) + '%';
        },
      },
      series: [
        {
          name: '完成率',
          type: 'bar',
          data: value.data,
          barWidth: '30%',
          itemStyle: {
            color: function (params) {
              // 使用处理后的 color 部分
              return processedLine[params.dataIndex]?.color || '#5793f3';
            },
          },
          label: {
            show: true,
            position: 'top',
            formatter: function (params) {
              return params.value.toFixed(2) + '%';
            },
            color: '#000',
            fontSize: 14,
          },
        },
      ],
      graphic: [
        {
          type: 'group',
          right: 20,
          top: 25,
          children: processedLine
            .map((item, index) => {
              const color = item.color;
              return [
                {
                  type: 'text',
                  style: {
                    text: item.display,
                    fill: '#000000D9',
                    fontSize: 12,
                    textAlign: 'left',
                  },
                  top: `${index * 20}px`,
                  right: 45,
                  onclick: () => {
                    router.push({
                      path: '/newLine/assembly/details',
                      query: {
                        line: props.lineOpt.find((option) => option.label === item.display).value,
                        dept: deptLabel.value,
                        state: 6,
                      },
                    });
                  },
                },
                {
                  type: 'rect',
                  shape: {
                    width: 25,
                    height: 14,
                    r: 5,
                  },
                  style: {
                    fill: color,
                  },
                  top: `${index * 20}px`,
                  right: 19,
                  onclick: () => {
                    router.push({
                      path: '/newLine/assembly/details',
                      query: {
                        line: props.lineOpt.find((option) => option.label === item.display).value,
                        dept: deptLabel.value,
                        state: 6,
                      },
                    });
                  },
                },
              ];
            })
            .flat(),
        },
      ],
    });
  }

  function updateModalChart(value) {
    // 处理 value.line，截取最后一个 _ 后的字符串作为颜色，并去掉 _ 和其后的字符串
    const processedLine = value.line.map((item) => {
      const parts = item.split('_');
      let colorPart = '#ED868C'; // 默认颜色
      let displayPart = item;
      if (parts.length > 1) {
        colorPart = parts.pop(); // 截取最后一个 _ 后的字符串作为颜色
        displayPart = parts.join('_'); // 去掉 _ 和其后的字符串
      }
      return { display: displayPart, color: colorPart };
    });

    setModalChartOptions({
      xAxis: {
        type: 'category',
        data: processedLine.map((item) => item.display), // 使用处理后的 display 部分
        name: '线路',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#000',
          fontSize: 14,
        },
        axisLabel: {
          interval: 0,
          rotate: 0,
          overflow: 'break',
          formatter: function (value) {
            const lines = [];
            for (let i = 0; i < value.length; i += 5) {
              lines.push(value.slice(i, i + 5));
            }
            return lines.join('\n');
          },
        },
      },
      yAxis: {
        name: '完成率',
        nameLocation: 'end',
        nameGap: 21,
        nameTextStyle: {
          color: '#333',
          fontSize: 14,
          fontWeight: 'bold',
          backgroundColor: '#f8f8f8', // 添加背景色
          borderColor: '#d9d9d9', // 边框颜色
          borderWidth: 1, // 边框宽度
          borderRadius: 4, // 圆角边框
          padding: [4, 8], // 内边距
        },
        axisLabel: {
          formatter: function (value) {
            return value + '%';
          },
        },
      },
      grid: {
        left: '10%',
        right: '12%',
        top: '10%',
        bottom: '10%',
      },
      tooltip: {
        valueFormatter: function (value) {
          return parseFloat(value.toFixed(2)) + '%';
        },
      },
      series: [
        {
          name: '完成率',
          type: 'bar',
          data: value.data,
          barWidth: '30%',
          itemStyle: {
            color: function (params) {
              // 使用处理后的 color 部分
              return processedLine[params.dataIndex]?.color || '#5793f3';
            },
          },
          label: {
            show: true,
            position: 'top',
            formatter: function (params) {
              return params.value.toFixed(2) + '%';
            },
            color: '#000',
            fontSize: 14,
          },
        },
      ],
    });
  }

  const showModal = () => {
    status.value = '1'; // 设置 status 为 '1'
    loadings.value = true; // 设置 loadings 为 true
    getChartDate().then(() => {
      visible.value = true; // 在请求成功后显示弹窗
    });
  };

  const handleOk = () => {
    visible.value = false;
    status.value = '0'; // 设置 status 为 '0'
    getChartDate();
  };
</script>
