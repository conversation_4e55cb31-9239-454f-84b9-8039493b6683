<template>
  <div class="h-full flex flex-col bg-white " style="width: 300px;border-radius: 25px ">
    <vxe-toolbar ref="toolbarRef">
      <template #buttons>
        <div class="vxe-buttons--wrapper" style="margin-left:2%;display: flex;flex-direction: row">
          <vxe-input v-model="searchVal" size="small" placeholder="搜索" clearable>
            <template #prefix>
              <i class="vxe-icon-search my-red" @click="onSearch"></i>
            </template>
          </vxe-input>
        </div>
      </template>
      <template #tools>

      </template>
    </vxe-toolbar>

    <vxe-table
        show-overflow
        ref="xTreeRef"
        border="inner"
        :row-config="{isHover: true, useKey: true,isCurrent: true}"
        :toolbarConfig="toolbarConfig"
        :show-header="false"
        :data="tableData"
        :checkbox-config="{labelField: 'title'}"
        @current-change="(val) => $emit('change',val.row.value)"
        :tree-config="{transform: false,  line: true, iconOpen: 'vxe-icon-square-minus-fill', iconClose: 'vxe-icon-square-plus-fill',expandAll:true}">
      <vxe-column  tree-node>
        <template #default="{ row }">
          <span>
            <template v-if="row.children && row.children.length">
              <i class="tree-node-icon" :class="hasRowExpand(row) ? 'vxe-icon-folder-open' : 'vxe-icon-folder'"></i>
            </template>
            <template v-else>
              <i class="tree-node-icon vxe-icon-file-txt"></i>
            </template>
            <span :style="{color: row.selected ? 'red' : 'black'}">{{row.title}}</span>
          </span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script lang="ts">
import {defineComponent, ref, onMounted, watch} from 'vue';
import {VxeGridInstance, VXETable, VxeTableInstance} from 'vxe-table'
import {getLeftTree} from "/@/api/erupt/erupt";
import {useMessage} from "/@/hooks/web/useMessage";


export default defineComponent({
  name: 'leftTree',
  props: {
    className: {}
  },
  components: {VXETable},
  emits: [ 'change'],
  setup(props, {attrs, emit}) {

    interface RowVO {
      id: string
      pid: string | null
      title: string
    }

    const {className} = props
    const searchVal = ref('')
    const uploadModalRef = ref()
    const columns = ref([]);
    const xTreeRef = ref<VxeTableInstance<RowVO>>()
    const tableData = ref<RowVO[]>([])
    const toolbarConfig = ref({
      buttons: [],
    })

    function refresh() {
      search()

    }
    function onSearch() {
      let elements = []
      const $table = xTreeRef.value
      deepTree(tableData.value,searchVal.value)
      $table.setCurrentRow(elements)
    }

    watch(
        () => searchVal.value,
        (v) => {
          deepTree(tableData.value,v)
        })
    function deepTree(treeList, key) {
      for (let index = 0; index < treeList.length; index++) {
        const element = treeList[index];
        if (key && element.title.includes(key)) {
          element.selected = true
        } else {
          element.selected = false
        }
        if (element.children && element.children.length) {
          deepTree(element.children, key)
        }
      }
      console.log(treeList)
    }

    function handleCancel() {
      resetFields()
    }

    function search() {
      getLeftTree(className).then(res => {
        const $table = xTreeRef.value
        const tableDatas = [{title:'全部',value:''}]
        res.forEach(item => {
          tableDatas.push(item)
        })
        tableData.value = tableDatas
        setTimeout(() => {
          $table?.setAllTreeExpand(true)
        },30)

      })
    }

    const hasRowExpand = (row: RowVO) => {
      const $table = xTreeRef.value
      if ($table) {
        return $table.isTreeExpandByRow(row)
      }
      return false
    }


    onMounted(() => {
      search()
    })
    return {
      xTreeRef,
      tableData,
      columns,
      toolbarConfig,
      searchVal,
      onSearch,
      handleCancel,
      uploadModalRef,
      search,
      hasRowExpand,
    };
  },
})


</script>
