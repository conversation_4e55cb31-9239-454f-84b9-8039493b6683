<template>
  <div style="height: 900px">
    <VxeBasicTable ref="tableRef" v-bind="gridOptions">
      <template #action="{ row }">
        <TableAction outside :actions="createActions(row)" />
      </template>
    </VxeBasicTable>
    <PersonSelect
      @register="register4"
      :showFooter="true"
      title="请选择转办人"
      @ok="handleSubmit"
      @close="handleCancel1"
      v-model:value="personNo"
      :query-contions="queryContions"
      :pre-conditions="preCondition"
    >
    </PersonSelect>
    <BasicModal
      @register="register"
      :showFooter="true"
      defaultFullscreen
      title="请办理"
      @ok="handleSubmit"
      @close="handleCancel"
    >
      <a-collapse v-model:activeKey="activeKey">
        <a-collapse-panel key="1" header="详细信息">
          <Description
            class="mt-4"
            layout="vertical"
            :contentStyle="{
              width: '100%',
              wordBreak: 'break-all',
              whiteSpace: 'normal',
              display: 'inline-block',
            }"
            :column="4"
            :data="descData"
            :schema="lookSchema"
          />
        </a-collapse-panel>
        <a-collapse-panel key="2" header="审批历史">
          <a-steps status="finish" direction="vertical" size="small" v-for="step in steps">
            <a-step :title="step.name">
              <template #description>
                <div>办理人：{{ step.person }}</div>
                <div>处理方式：{{ step.action }}</div>
                <div>审批意见：{{ step.opinion }}</div>
                <p>办理时间：{{ step.createDateTime }}</p>
              </template>
            </a-step>
          </a-steps>
        </a-collapse-panel>
      </a-collapse>
      <a-card :head-style="headerStyle" :body-style="borderStyle" :bordered="true" title="整改记录">
        <a-table
          :pagination="false"
          :columns="[
            {
              name: '',
              dataIndex: '',
              key: '',
            },
            {
              title: '处理人',
              dataIndex: 'createUserName',
              key: 'createUserName',
              width: 600,
            },
            {
              title: '问题状态',
              dataIndex: 'opreation',
              key: 'opreation',
              width: 600,
            },
            {
              title: '整改意见',
              dataIndex: 'note',
              key: 'note',
              width: 600,
            },
            {
              title: '处理时间',
              dataIndex: 'createDateTime',
              key: 'createDateTime',
              width: 600,
            },
          ]"
          :data-source="descData.questionItems"
        >
        </a-table>
      </a-card>
      <a-card style="width: 100%; margin-top: 1%" title="审批处理">
        <BasicForm style="height: 100%" @register="registerForm"></BasicForm>
      </a-card>

      <template #footer>
        <VxeButton type="primary" @click="approve">确定</VxeButton>
        <VxeButton type="primary" @click="cancel">取消</VxeButton>
      </template>
    </BasicModal>

    <BasicModal
      defaultFullscreen
      @register="register1"
      :showFooter="true"
      title="编辑"
      width="50%"
      @ok="handleSubmit1"
      okText="保存"
    >
      <BasicForm @register="registerForm1"></BasicForm>
    </BasicModal>

    <BasicModal
      @register="register2"
      :showFooter="true"
      title="请编辑所属位置"
      width="40%"
      @ok="handleSubmit2"
      @cancel="handleCancel2"
    >
      <template #centerFooter>
        <a-button @click="clearLast"> 清除上一条</a-button>
      </template>
      <BasicForm @register="registerForm2" style="height: 350px"></BasicForm>
    </BasicModal>

    <BasicModal
      @register="register3"
      :showFooter="true"
      title="整改"
      width="50%"
      @ok="handleSubmit3"
      okText="保存"
    >
      <BasicForm @register="registerForm3"></BasicForm>
    </BasicModal>
  </div>
</template>

<script lang="tsx">
  import {
    defineComponent,
    PropType,
    ref,
    onMounted,
    watchEffect,
    computed,
    unref,
    watch,
    h,
  } from 'vue';
  import { VXETable, VxeTableInstance } from 'vxe-table';
  import BpmnViewer from '/@/views/process/bpmn/index1.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import {
    complete,
    deploy,
    deployedList,
    getApprovalRecords,
    getClassName,
    getFlowsTask,
    getProcessComponent,
    getProcessComponentOnChange,
    getProcessOne,
    getTasksListByUser,
    moduleListApi,
    read,
  } from '/@/api/process/process';
  import { ActionItem, TableAction } from '/@/components/Table';
  import { BasicTableProps, VxeBasicTable } from '/@/components/VxeTable';
  import { useRouter } from 'vue-router';
  import {
    Modal,
    Tag,
    Tooltip,
    Steps,
    Step,
    Tabs,
    TabPane,
    Table,
    Card,
    Collapse,
    CollapsePanel,
  } from 'ant-design-vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { processDef, task } from '/@/views/process/definition/processDefData';
  import { useForm } from '/@/components/Form';
  import { getOptionsApi, getDepartmentList } from '/@/views/demo/system/assembly/api/assembly';
  import { buildApi, getDetailOne, getOne, update } from '/@/api/erupt/erupt';

  import { useTaskStore } from '/@/store/modules/task';
  import { Description } from '/@/components/Description';
  import {
    getFormTaskList,
    TerminateTask,
    getType,
    SaveItems,
  } from '/src/views/task/question/api/question';
  import EntitySelector from '/@/components/Form/src/components/EntitySelector.vue';
  import { delegate, delegateQ } from '/@/views/task/assembly/api/assembly';
  import PersonSelect from '/@/views/task/components/personSelect.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Base64 } from 'js-base64';
  import {
    getComponent,
    getComponentProps,
    getSearchComponent,
  } from '/@/components/EruptTable/componets';
  import { GetDeptBySpecical, GetOptions } from '/@/views/demo/system/question/api';
  import Template from '/@/views/demo/system/quality/template.vue';

  export default defineComponent({
    components: {
      Template,
      EntitySelector,
      PersonSelect,
      Description,
      Tooltip,
      BpmnViewer,
      Tag,
      Tabs,
      TabPane,
      [Table.name]: Table,
      [Steps.name]: Steps,
      [Steps.Step.name]: Steps.Step,
      [Card.name]: Card,
      [Collapse.name]: Collapse,
      [CollapsePanel.name]: CollapsePanel,
      BasicModal,
      VxeBasicTable,
      TableAction,
      BasicForm,
      BasicDrawer,
      VXETable,
    },
    setup(props, { attrs, emit }) {
      const pid = ref();
      const eid = ref();
      const globSetting = useGlobSetting();
      const { createMessage } = useMessage();
      const taskStore = useTaskStore();
      const userStore = useUserStore();
      const lookSchema = ref([]);
      const taskSchema = ref([]);
      const editSchemas = ref([]);
      const personNo = ref();
      const isqj = ref(false);
      const base = ref({ id: '', version: '' });
      const [register, { openModal, closeModal }] = useModal();
      const [register1, { openModal: openModal1, closeModal: closeModal1 }] = useModal();
      const [register2, { openModal: openModal2, closeModal: closeModal2 }] = useModal();
      const [register3, { openModal: openModal3, closeModal: closeModal3 }] = useModal();
      const [register4, { openModal: openModal4, closeModal: closeModal4 }] = useModal();
      const activeKey = ref('1');
      const acts = ref([]);
      const cName = ref('');
      const extraSchemas = ref([]);
      const step = ref('');
      const steps = ref([]);
      const descData = ref([]);
      const current = ref(0);
      const bussinesKey = ref('');
      const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
        labelWidth: 100,
        schemas: taskSchema,
        showActionButtonGroup: false,
      });

      const [
        registerForm1,
        {
          resetFields: resetFields1,
          setFieldsValue: setFieldsValue1,
          updateSchema: updateSchema1,
          validate: validate1,
          getFieldsValue,
        },
      ] = useForm({
        labelWidth: 100,
        wrapperCol: { span: 24 },
        schemas: editSchemas,
        showActionButtonGroup: false,
      });

      const [
        registerForm2,
        {
          resetFields: resetFields2,
          setFieldsValue: setFieldsValue2,
          updateSchema: updateSchema2,
          validate: validate2,
        },
      ] = useForm({
        labelWidth: 100,
        schemas: extraSchemas,
        showActionButtonGroup: false,
      });

      const [
        registerForm3,
        {
          resetFields: resetFields3,
          setFieldsValue: setFieldsValue3,
          updateSchema: updateSchema3,
          validate: validate3,
        },
      ] = useForm({
        labelWidth: 100,
        schemas: [
          {
            field: 'questionStatus',
            label: '问题状态',
            required: true,
            component: 'Select',
            colProps: {
              span: 16,
            },
            componentProps: {
              options: [
                {
                  label: '已关闭',
                  value: '已关闭',
                },
                {
                  label: '已闭环',
                  value: '已闭环',
                },
                {
                  label: '协调中',
                  value: '协调中',
                },
              ],
            },
          },
          {
            field: 'modify',
            label: '整改',
            required: true,
            component: 'InputTextArea',
            colProps: {
              span: 24,
            },
          },
        ],
        showActionButtonGroup: false,
      });
      const lineOption = ref([]);
      const departmentList = ref([]);
      const getTypeList = ref([]);
      const gridOptions = ref<BasicTableProps>({
        id: 'VxeTable',
        keepSource: true,
        editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
        rowConfig: { isHover: true, useKey: true, isCurrent: false },
        columnConfig: { isHover: true, isCurrent: false },
        formConfig: {
          enabled: true,
          span: 24,
          items: [
            {
              field: 'lineIds',
              title: '所属线路',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: lineOption,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'code',
              title: '问题编码',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'typeId',
              title: '所属专业',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: getTypeList,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'reportDeptId',
              title: '提报部门',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: departmentList,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
            },
            {
              field: 'manageDeptId',
              title: '专业管理单位',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: departmentList,
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'center',
              folding: true,
            },
            {
              field: 'questionType',
              title: '问题类型',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: [
                    { label: '设计', value: 'A' },
                    { label: '施工', value: 'B' },
                    { label: '质保', value: 'C' },
                    { label: '功能缺陷', value: 'D' },
                    { label: '环境卫生', value: 'E' },
                    { label: '标识导向', value: 'F' },
                    { label: '其他', value: 'G' },
                  ],
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              field: 'approveStatus',
              title: '当前状态',
              itemRender: {
                name: 'ASelect',
                props: {
                  options: [
                    { label: '待提报', value: '待提报' },
                    { label: '审核中', value: '审核中' },
                    { label: '完成', value: '完成' },
                    { label: '终止', value: '终止' },
                  ],
                },
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              field: 'createUserId',
              title: '提报人',
              itemRender: {
                name: 'AInput',
              },
              span: 6,
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              field: 'createTime',
              title: '提报时间',
              itemRender: {
                name: 'ARangePicker',
                props: {
                  showTime: false, // 设置为 false 只显示年月日
                  valueFormat: 'YYYY-MM-DD', // 设置日期格式
                },
              },
              span: 6,
              align: 'left',
              titleWidth: '20%',
              titleAlign: 'right',
              folding: true,
            },
            {
              span: 24,
              align: 'center',
              className: '!pr-0',
              collapseNode: true,
              itemRender: {
                name: 'AButtonGroup',
                children: [
                  {
                    props: {
                      type: 'primary',
                      content: '查询',
                      htmlType: 'submit',
                    },
                    attrs: {
                      class: 'mr-2',
                    },
                  },
                  {
                    props: {
                      type: 'default',
                      htmlType: 'reset',
                      content: '重置',
                    },
                  },
                ],
              },
            },
          ],
        },
        columns: [
          {
            type: 'checkbox',
            width: '50',
            align: 'center',
          },
          {
            title: '序号',
            type: 'seq',
            width: '50',
            align: 'center',
          },
          {
            field: 'createUserId',
            showOverflow: 'title',
            title: '提报人',
            sortable: false,
            width: '150',
          },
          {
            field: 'lineIds',
            showOverflow: 'title',
            title: '所属线路',
            sortable: false,
            width: '150',
          },
          {
            field: 'reportDept',
            showOverflow: 'title',
            title: '提报部门',
            sortable: false,
            width: '150',
          },
          {
            field: 'createTime',
            showOverflow: 'title',
            title: '提报时间',
            sortable: false,
            width: '150',
          },
          {
            field: 'typeId',
            showOverflow: 'title',
            title: '所属专业',
            sortable: false,
            width: '150',
          },
          {
            field: 'code',
            showOverflow: 'title',
            title: '问题编码',
            sortable: false,
            width: '150',
          },
          {
            field: 'manageDept',
            showOverflow: 'title',
            title: '专业管理单位',
            sortable: false,
            width: '150',
          },
          {
            field: 'des',
            showOverflow: 'title',
            title: '问题简述',
            sortable: false,
            width: '150',
          },
          {
            field: 'questionType',
            showOverflow: 'title',
            title: '问题类型',
            sortable: false,
            width: '150',
          },
          {
            field: 'approveStatus',
            showOverflow: 'title',
            title: '当前状态',
            sortable: false,
            width: '150',
          },
          {
            title: '操作',
            align: 'center',
            width: 230,
            slots: {
              default: 'action',
            },
            fixed: 'right',
          },
        ],
        toolbarConfig: {
          refresh: true, // 显示刷新按钮
          import: false, // 显示导入按钮
          export: false, // 显示导出按钮
          print: false, // 显示打印按钮
          zoom: false, // 显示全屏按钮
          custom: true,
        },
        height: 'auto',
        proxyConfig: {
          ajax: {
            query: async ({ page, form }) => {
              debugger;
              const bpmns = await getFormTaskList(page, form);
              return bpmns;
            },
            queryAll: async ({ form }) => {
              const bpmns = await getFormTaskList();
              let rows = [];
              bpmns.forEach((bpmn) => {
                rows.push({ name: bpmn.split('.bpmn')[0] });
              });
              return rows;
            },
          },
        },
      });

      function actionChange(e) {
        console.log(e, step.value);
        getProcessComponentOnChange(pid.value, cName.value, { option: e ? e : '' }).then((res) => {
          if (res) {
            res.forEach((item) => {
              if (item.field == 'option') item.componentProps.onChange = (e) => actionChange(e);
            });
            taskSchema.value = res;
          }
        });
      }

      interface RowVO {
        name: string;
      }

      const tableData = ref<Array<RowVO>>([]);

      onMounted(async () => {
        const options = await getOptionsApi();
        lineOption.value = options;
        const departments = await getDepartmentList();
        departmentList.value = departments;
        const Type = await getType();
        getTypeList.value = Type;
        buildApi('Question').then((res) => {
          const {
            eruptModel: { eruptFieldModels },
          } = res;
          let edits = [];
          eruptFieldModels.forEach((item) => {
            const key = item.fieldName;
            const title = item.eruptFieldJson.edit.title;

            //  formState[key] = null
            if (item.eruptFieldJson.edit.show.edit_show && key !== 'id') {
              if (
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_REFER' &&
                item.eruptFieldJson.edit.type !== 'TAB_TABLE_ADD' &&
                item.eruptFieldJson.edit.type !== 'TAB_TREE'
              ) {
                const e = {
                  field: key,
                  label: title,
                  component: getComponent(item.eruptFieldJson.edit.type, item.eruptFieldJson.edit),
                  componentProps: (form) => {
                    return {
                      ...getComponentProps(item, 'Question'),
                      disabled: item.eruptFieldJson.edit.readOnly.edit,
                    };
                  },
                  required: item.eruptFieldJson.edit.notNull,
                  colProps: {
                    span: item.eruptFieldJson.edit.colSpan,
                  },
                };
                edits.push(e);
                console.log('edits', edits);
              } else {
              }
            }
          });
          edits.forEach((item) => {
            if (item.field == 'importantEnclosure') {
              debugger;
              const props = { ...item.componentProps() };
              item.ifShow = ({ values }) => {
                console.log(values);
                return values.level == 'A';
              };

              item.componentProps = ({ formModel }) => {
                return {
                  ...props,
                  required: true,
                  show: false,
                };
              };
              console.log('importantEnclosure', item);
            }
            if (item.field == 'typeId') {
              const props = { ...item.componentProps() };
              item.componentProps = ({ formModel }) => {
                return {
                  ...props,
                  onChange: async (e) => {
                    if (e !== 'qita') {
                      console.log(formModel);
                      updateSchema({
                        field: 'manageDeptId',
                        componentProps: {
                          disabled: true,
                        },
                      });
                    } else {
                      updateSchema({
                        field: 'manageDeptId',
                        componentProps: {
                          disabled: false,
                        },
                      });
                    }
                    const v = await GetDeptBySpecical(e);
                    setFieldsValue({
                      manageDeptId: v,
                    });
                  },
                };
              };
            }
            if (item.field == 'bidSection') {
              const props = { ...item.componentProps() };
              item.componentProps = ({ formModel }) => {
                return {
                  ...props,
                  readOnly: true,
                  onClick: async () => {
                    const arr = await GetOptions(formModel.lineIds);
                    const schemas = [
                      {
                        field: 'isqj',
                        label: '是否区间',
                        component: 'Select',
                        colProps: { span: 14 },
                        componentProps: {
                          onChange: (e) => {
                            isqj.value = e;
                          },
                          options: [
                            { label: '是', value: true },
                            { label: '否', value: false },
                          ],
                        },
                      },
                      {
                        field: 'isContain1',
                        label: '（包含本站）',
                        required: true,
                        component: 'RadioGroup',
                        ifShow: ({ values }) => {
                          return !!values.isqj;
                        },
                        componentProps: {
                          options: [
                            {
                              label: '是',
                              value: true,
                            },
                            {
                              label: '否',
                              value: false,
                            },
                          ],
                        },
                        colProps: { span: 12 },
                      },
                      {
                        field: 'siteName',
                        label: '车站',
                        required: true,
                        component: 'Select',
                        colProps: { span: 12 },
                        componentProps: {
                          options: arr,
                          // not request untill to select
                          placeholder: '请选择车站',
                        },
                      },
                      {
                        field: 'isContain2',
                        label: '（包含本站）',
                        required: true,
                        component: 'RadioGroup',
                        ifShow: ({ values }) => {
                          return !!values.isqj;
                        },
                        componentProps: {
                          options: [
                            {
                              label: '是',
                              value: true,
                            },
                            {
                              label: '否',
                              value: false,
                            },
                          ],
                        },
                        colProps: { span: 12 },
                      },
                      {
                        field: 'siteName1',
                        label: '车站',
                        required: true,
                        component: 'Select',
                        colProps: { span: 12 },
                        ifShow: ({ values }) => {
                          return !!values.isqj;
                        },
                        componentProps: {
                          options: arr,
                          // not request untill to select
                          placeholder: '请选择车站',
                        },
                      },
                      {
                        field: 'direction',
                        label: '上下行',
                        required: true,
                        component: 'Select',
                        colProps: { span: 12 },
                        ifShow: ({ values }) => {
                          return !!values.isqj;
                        },
                        componentProps: {
                          options: [
                            { label: '上行', value: '上行' },
                            { label: '下行', value: '下行' },
                          ],
                          // not request untill to select
                          placeholder: '请选择车站',
                        },
                      },
                    ];
                    extraSchemas.value = schemas;
                    openModal2();
                  },
                };
              };
            }
          });
          editSchemas.value = edits;

          //gridOptions.columns = columns
          //search()
        });
      });

      return {
        tableData,
        lineOption,
        gridOptions,
        personNo,
        register,
        register1,
        register2,
        register3,
        register4,
        openModal,
        openModal1,
        openModal2,
        openModal3,
        openModal4,
        closeModal,
        closeModal1,
        closeModal2,
        closeModal3,
        closeModal4,
        registerForm,
        registerForm1,
        registerForm2,
        registerForm3,
        descData,
        activeKey,
        acts,
        lookSchema,
        taskSchema,
        steps,
        setFieldsValue,
        setFieldsValue1,
        setFieldsValue2,
        updateSchema1,
        updateSchema2,
        current,
        departmentList,
        getTypeList,
        queryContions: [
          {
            field: 'no',
            title: '工号',
            itemRender: {
              name: 'AInput',
              defaultValue: '',
            },
            span: 6,
          },
          {
            field: 'name',
            title: '姓名',
            itemRender: {
              name: 'AInput',
              defaultValue: '',
            },
            span: 6,
          },
          {
            span: 4,
            align: 'right',
            className: '!pr-0',
            itemRender: {
              name: 'AButtonGroup',
              children: [
                {
                  props: {
                    type: 'primary',
                    content: '查询',
                    htmlType: 'submit',
                  },
                  attrs: {
                    class: 'mr-2',
                  },
                },
                {
                  props: {
                    type: 'default',
                    htmlType: 'reset',
                    content: '重置',
                  },
                },
              ],
            },
          },
        ],
        preCondition: new Map([
          ['deptStr', userStore.getUserInfo.dept],
          ['postStr', userStore.getUserInfo.post],
        ]),
        cancel: () => {
          resetFields();
          closeModal();
        },
        approve: async () => {
          let values = await validate();
          const params = {
            step: step.value,
            ...values,
            bussinesKey: bussinesKey.value,
          };
          complete(pid.value, params, cName.value).then((res) => {
            if (res == 200) {
              createMessage.success('办理完成');
              document.querySelector('button[title="刷新"]').click();
            }
            resetFields();
            taskStore.setTaskList();
            closeModal();
          });
        },
        handleSubmit: async () => {
          /* let values = await validate1();
         console.log(values)*/
          delegateQ([pid.value], personNo.value.no).then((res) => {
            if (res) {
              createMessage.success('转办成功');
              closeModal4();
              document.querySelector('button[title="刷新"]').click();
            }
          });
        },
        handleShow: (downloadUrl, index) => {
          console.log(downloadUrl);
          const userStore = useUserStore();
          const watermarkTxt = userStore.userInfo.username + ' ' + userStore.userInfo.realName;
          modalUrl.value = ''; // 清空旧缓存
          modalUrl.value =
            globSetting.previewUrl +
            '/onlinePreview?url=' +
            downloadUrl +
            '&watermarkTxt=' +
            watermarkTxt; // 设置预览 URL
          key.value = index; // 重新加载 iframe
          openModalPreview(true); // 打开 modal 窗口
        },
        handleCancel: () => {
          resetFields();
        },
        handleCancel1: () => {
          //resetFields();
        },
        createActions: (record) => {
          const actions: ActionItem[] = [
            {
              label: '整改',
              onClick: async () => {
                eid.value = record.id;
                console.log(record);
                openModal3();
              },
            },
            {
              label: '办理',
              onClick: async () => {
                console.log(record);
                step.value = record.taskStep;
                pid.value = record.taskId;
                let schema = [];

                getClassName(record.taskId).then(async (res) => {
                  if (res) {
                    bussinesKey.value = res;
                    const arr = res.split('.');
                    const className = arr[0];
                    cName.value = className;
                    getProcessComponent(record.taskId, className).then((res) => {
                      console.log('component', res);
                      if (res) {
                        res.forEach((item) => {
                          if (item.field == 'option')
                            item.componentProps.onChange = (e) => actionChange(e);
                        });
                        taskSchema.value = res;
                      }
                    });
                    const id = arr[1];
                    buildApi(className).then(async (res) => {
                      const {
                        eruptModel: { eruptFieldModels, eruptJson },
                        tabErupts,
                        power,
                      } = res;
                      let details = [];
                      eruptFieldModels.forEach((item) => {
                        const key = item.fieldName;
                        const title = item.eruptFieldJson.edit.title;
                        if (item.eruptFieldJson.views.length > 0) {
                          item.eruptFieldJson.views.forEach((v) => {
                            if (v.show) {
                              const k = v.column ? key + '_' + v.column : key;
                              if (item.eruptFieldJson.edit.show.detail_show) {
                                debugger;
                                const d = {
                                  field: k,
                                  label: v.title,
                                  span: item.eruptFieldJson.edit.detailSpan,
                                };
                                details.push(d);
                              }
                            } else {
                              if (item.eruptFieldJson.edit.show.detail_show) {
                                const d = {
                                  field: key,
                                  label: title,
                                  span: item.eruptFieldJson.edit.detailSpan,
                                };
                                details.push(d);
                              }
                            }
                          });
                        } else {
                          debugger;
                          if (item.eruptFieldJson.edit.show.detail_show && key !== 'id') {
                            if (item.eruptFieldJson.edit.type == 'ATTACHMENT') {
                              if (!item.eruptFieldJson.edit.attachmentType.isgBigFile) {
                                const d = {
                                  field: key,
                                  label: title,
                                  render: (val) => {
                                    return (
                                      <div>
                                        {val
                                          ? val.split('|').map((item, index) => {
                                              return (
                                                <div>
                                                  <a
                                                    onClick={() =>
                                                      handleShow(
                                                        encodeURIComponent(
                                                          Base64.encode(
                                                            globSetting.eruptAttachment + item,
                                                          ),
                                                        ),
                                                        index,
                                                      )
                                                    }
                                                  >
                                                    {item.substring(item.lastIndexOf('/') + 1)}
                                                  </a>{' '}
                                                  <a
                                                    style={{ marginLeft: '15px', color: '#ff6118' }}
                                                    href={globSetting.eruptAttachment + item}
                                                  >
                                                    下载
                                                  </a>
                                                </div>
                                              );
                                            })
                                          : ''}

                                        {/*<Tooltip placement={'bottom'}>
                          <a-button
                            onClick={() => {
                              const files = val ? val.split('|') : [];
                              uploadModalRef.value.setFiles(files);
                              openPreviewModal();
                            }}
                          >
                            <Icon icon="bi:eye" />
                          </a-button>
                        </Tooltip>*/}
                                      </div>
                                    );
                                  },
                                };
                                details.push(d);
                              } else {
                                const d = {
                                  field: key,
                                  label: title,
                                  render: (val) => {
                                    return (
                                      <div>
                                        {val
                                          ? val.split('|').map((item, index) => {
                                              return (
                                                <div>
                                                  <a
                                                    style={{ marginLeft: '15px', color: '#ff6118' }}
                                                    href={globSetting.eruptAttachment + item}
                                                  >
                                                    {item.substring(item.lastIndexOf('/') + 1)}
                                                  </a>
                                                </div>
                                              );
                                            })
                                          : ''}

                                        {/*<Tooltip placement={'bottom'}>
                          <a-button
                            onClick={() => {
                              const files = val ? val.split('|') : [];
                              uploadModalRef.value.setFiles(files);
                              openPreviewModal();
                            }}
                          >
                            <Icon icon="bi:eye" />
                          </a-button>
                        </Tooltip>*/}
                                      </div>
                                    );
                                  },
                                };
                                details.push(d);
                              }
                            } else {
                              const d = {
                                field: key,
                                label: title,
                                span: item.eruptFieldJson.edit.detailSpan,
                              };
                              details.push(d);
                            }
                          }
                        }

                        //  formState[key] = null
                      });
                      /*details.push({
                      field: 'message',
                      label: '备注',
                      required: true,
                      component: 'InputTextArea',
                      colProps: {span: 24},
                      })*/
                      lookSchema.value = details;
                      const entity = await getDetailOne(className, id);
                      descData.value = entity;

                      //gridOptions.columns = columns
                      //search()
                    });
                    const records = await getApprovalRecords(record.processInstanceId);
                    steps.value = records;
                    current.value = records.length;
                    openModal();
                  }
                });
              },
            },
          ];
          if (record.taskStep == '问题填报人发起') {
            actions.push({
              label: '编辑',
              onClick: async () => {
                const res = await getOne('Question', record.id);
                //base.value.id = res.id
                //base.value.version = res.version
                base.value = res;
                setTimeout(() => {
                  resetFields1();
                  openModal1();
                  setTimeout(() => {
                    setFieldsValue1(res);
                  }, 20);
                }, 200);
              },
            });
          }
          if (record.taskStep == '问题填报部门负责人' || record.taskStep == '对口部门负责人') {
            actions.push({
              label: '转办',
              onClick: () => {
                debugger;
                pid.value = record.taskId;
                openModal4();
              },
            });
          }
          if (record.taskStep == '问题填报人发起') {
            actions.push({
              label: '取消任务',
              popConfirm: {
                title: '是否确认取消任务',
                confirm: () => {
                  TerminateTask(record.taskId, record.id).then((res) => {
                    if (res) {
                      createMessage.success('取消成功');
                      document.querySelector('button[title="刷新"]').click();
                    } else {
                      createMessage.error('取消失败');
                    }
                  });
                },
              },
            });
          }
          return actions;
        },
        clearLast: async () => {
          const value = getFieldsValue();
          const arr = value.bidSection.split('\n\r');
          let nvalue = arr[0];

          for (var i = 0; i < arr.length; i++) {
            if (i != 0 && i != arr.length - 1) nvalue += '\n\r' + arr[i];
          }
          setFieldsValue1({ bidSection: nvalue });
          resetFields2();
          closeModal2();
        },
        handleSubmit1: async () => {
          const value = await validate1();
          const { status, message } = await update(
            'Question',
            { ...base.value, ...value, repeated: 1 },
            null,
          );
          if (status == 'SUCCESS') {
            createMessage.success('保存成功');
            resetFields1();
            closeModal1();
            document.querySelector('button[title="刷新"]').click();
          } else {
            createMessage.error('保存失败:' + message);
          }
        },
        handleSubmit2: async () => {
          if (isqj.value == false) {
            const value = await validate2();
            const v = getFieldsValue();
            if (v.bidSection) {
              setFieldsValue1({ bidSection: v.bidSection + '\n\r' + value.siteName });
            } else {
              setFieldsValue1({ bidSection: value.siteName });
            }

            resetFields2();
            closeModal2();
          }
          if (isqj.value == true) {
            const value = await validate2();
            debugger;
            const v = getFieldsValue();
            const left = value.siteName + (value.isContain1 ? '（含）' : '');
            const right = value.siteName1 + (value.isContain2 ? '（含）' : '');
            const result = left + '-' + right + '-' + value.direction;
            if (v.bidSection) {
              setFieldsValue1({ bidSection: v.bidSection + '\n\r' + result });
            } else {
              setFieldsValue1({ bidSection: result });
            }

            resetFields2();
            closeModal1();
          }
        },
        handleSubmit3: async () => {
          const value = await validate3();
          const r = await SaveItems(eid.value, { ...value });
          if (r) {
            createMessage.success('保存成功');
            closeModal3();
          }
        },
        handleCancel1: () => {},
        handleCancel2: () => {
          resetFields2();
        },
        eid,
        borderStyle: {
          border: '1px solid #e8e8e8',
        },
        headerStyle: {
          backgroundColor: '#fafafa',
          color: '#535151',
          fontWeight: 'bold',
          fontSize: '12',
          textAlign: 'left',
        },
      };
    },
  });
</script>
<style scoped>
  .ant-select-disabled {
    color: #303030;
    background: #fff;
    cursor: not-allowed;
  }
</style>
<style>
  .vxe-row > .vxe-col--6 {
    float: none !important;
  }
</style>
