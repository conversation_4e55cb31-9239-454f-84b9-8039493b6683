import type { Component } from 'vue';
import type { ComponentType } from './types/index';

/**
 * Component list, register here to setting it in the form
 */
import {
  Input,
  Select,
  Radio,
  Checkbox,
  AutoComplete,
  Cascader,
  DatePicker,
  InputNumber,
  Switch,
  TimePicker,
  TreeSelect,
  Slider,
  Rate,
} from 'ant-design-vue';

import ApiRadioGroup from './components/ApiRadioGroup.vue';
import Divider from './components/Divider.vue';
import RadioButtonGroup from './components/RadioButtonGroup.vue';
import ApiSelect from './components/ApiSelect.vue';
import ApiTree from './components/ApiTree.vue';
import ApiTreeSelect from './components/ApiTreeSelect.vue';
import ApiCascader from './components/ApiCascader.vue';
import ApiTransfer from './components/ApiTransfer.vue';
import EntitySelector from './components/EntitySelector.vue';
import BigFileUpload from '/@/components/Upload/src/BigFileUpload.vue';
import { BasicUpload } from '/@/components/Upload';
import { StrengthMeter } from '/@/components/StrengthMeter';
import { IconPicker } from '/@/components/Icon';
import { CountdownInput } from '/@/components/CountDown';
import { formatDate } from '@vueuse/shared';
import EruptUpload from '/@/components/Form/src/components/EruptUpload.vue';
import { Tinymce } from '/@/components/Tinymce/index';
import { CodeEditor } from '/@/components/CodeEditor';
import { MarkDown } from '/@/components/Markdown';
import CheckGroup from '/@/components/Form/src/components/CheckGroup.vue';
import TagSelect from '/@/components/Form/src/components/TagSelect.vue';
import ColorPicker from '/@/components/Form/src/components/ColorPicker.vue';
import EruptTreeSelect from '/@/components/Form/src/components/EruptTreeSelect.vue';
import TagEdit from '/@/components/Form/src/components/TagEdit.vue';
import FileList from '/@/components/Form/src/components/FileList.vue';
const componentMap = new Map<ComponentType, Component>();

componentMap.set('CheckGroup', CheckGroup);
componentMap.set('Input', Input);
componentMap.set('InputGroup', Input.Group);
componentMap.set('InputPassword', Input.Password);
componentMap.set('InputSearch', Input.Search);
componentMap.set('InputTextArea', Input.TextArea);
componentMap.set('InputNumber', InputNumber);
componentMap.set('Divide', Divider);
componentMap.set('AutoComplete', AutoComplete);
componentMap.set('EntitySelector', EntitySelector);
componentMap.set('BigFileUpload', BigFileUpload);
componentMap.set('Select', Select);
componentMap.set('ApiSelect', ApiSelect);
componentMap.set('ApiTree', ApiTree);
componentMap.set('TagSelect', TagSelect);
componentMap.set('TagEdit', TagEdit);
componentMap.set('TreeSelect', TreeSelect);
componentMap.set('ApiTreeSelect', ApiTreeSelect);
componentMap.set('ApiRadioGroup', ApiRadioGroup);
componentMap.set('Switch', Switch);
componentMap.set('RadioButtonGroup', RadioButtonGroup);
componentMap.set('RadioGroup', Radio.Group);
componentMap.set('Checkbox', Checkbox);
componentMap.set('CheckboxGroup', Checkbox.Group);
componentMap.set('ApiCascader', ApiCascader);
componentMap.set('Cascader', Cascader);
componentMap.set('Slider', Slider);
componentMap.set('Rate', Rate);
componentMap.set('ApiTransfer', ApiTransfer);
componentMap.set('CodeEditor', CodeEditor);
componentMap.set('HtmlEditor', Tinymce);
componentMap.set('DatePicker', DatePicker);
componentMap.set('YearPicker', DatePicker.YearPicker);
componentMap.set('MonthPicker', DatePicker.MonthPicker);
componentMap.set('RangePicker', DatePicker.RangePicker);
componentMap.set('WeekPicker', DatePicker.WeekPicker);
componentMap.set('TimePicker', TimePicker);
componentMap.set('StrengthMeter', StrengthMeter);
componentMap.set('IconPicker', IconPicker);
componentMap.set('InputCountDown', CountdownInput);
componentMap.set('EruptTreeSelect', EruptTreeSelect);
componentMap.set('ColorPicker', ColorPicker);
componentMap.set('Upload', BasicUpload);
componentMap.set('Attachment', EruptUpload);
componentMap.set('Divider', Divider);
componentMap.set('EruptFileList', FileList);

export function add(compName: ComponentType, component: Component) {
  componentMap.set(compName, component);
}

export function del(compName: ComponentType) {
  componentMap.delete(compName);
}

export function getColFomatterEn(key, view) {
  const k = view.column ? key + '_' + view.column : key;
  let col = { key: k, dataIndex: k, title: view.title };
  if (view.viewType == 'DATE') {
    col.customRender = ({ text }) => {
      return formatDate(new Date(text), 'YYYY-MM-DD');
    };
  }
  return col;
}

export { componentMap };
