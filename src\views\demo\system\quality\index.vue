<template>
  <erupt-table
    title="质保管理"
    seq="true"
    ref="eruptTableRef"
    class-name="Quality"
    :pre-conditions="preCondition"
    :query-sort="querySort"
  >
  <template #state="{ row }">
        <div v-if="row.state == '待启动'" style="align-self: center">
          <a-tag color="red" style="width: 60px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
        <div v-if="row.state == '未完成'" style="align-self: center">
          <a-tag color="green" style="width: 60px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
        <div v-if="row.state == '已完成'" style="align-self: center">
          <a-tag color="yellow" style="width: 60px; text-align: center">
            {{ row.state }}
          </a-tag>
        </div>
      </template>
      </erupt-table>
</template>
<script lang="ts">
  import eruptTable from '/@/components/EruptTable/eruptTable';
  import { useQualityStoreWithOut } from '/@/store/modules/quality';
  import { h, ref, defineComponent, onMounted, computed } from 'vue';
  import { Button, Popconfirm } from 'ant-design-vue';
  import { save, update } from '/@/api/erupt/erupt';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { useRoute, useRouter } from 'vue-router';
  export default defineComponent({
    name: 'Quality',
    components: { eruptTable },
    setup() {
      const eruptTableRef = ref();
      const { createMessage } = useMessage();
      const route = useRoute();
      const router = useRouter();
      const preCondition = new Map([]);
      const tplId = computed(() => route.query.tplId);

      const formExtraAction = () => {
        return h(
          Popconfirm,
          {
            title: '是否确认完成',
            onConfirm: async () => {
              const value = await eruptTableRef.value.getFormData();
              if (!value.version) delete value.version;
              if (!value.id) delete value.id;
              value.approveFlag = 1;
              if (value.id) {
                const { status, message } = await update('Quality', value, null);
                if (status == 'SUCCESS') {
                  createMessage.success('完成成功');
                  eruptTableRef.value.closeModal1();
                  eruptTableRef.value.refresh();
                } else {
                  createMessage.error('完成成功:' + message);
                }
              } else {
                const { status, message } = await save('Quality', value, null);
                if (status == 'SUCCESS') {
                  createMessage.success('完成成功');
                  eruptTableRef.value.closeModal1();
                  eruptTableRef.value.refresh();
                } else {
                  createMessage.error('完成成功:' + message);
                }
              }
            },
            onCancel: () => {},
          },
          h(Button, {}, '完成'),
        );
      };
      onMounted(() => {
        if (tplId) {
          preCondition.set('template', tplId);
        }
      });
      function goBack() {
        router.push('/newLine/quality/qualityTpl');
      }

      // async function getList() {
      //   const res = await getViewApi(id);
      //   console.log('12154151541',id);
      //   console.log('12121211212',res);
      //   // tableData.value = res.map((item) => {
      //   //   if (item.children && item.children.length > 0) {
      //   //     // 处理一级 children 数组，为每个子节点设置 req
      //   //     item.children = item.children.map((child) => {
      //   //       child.seq = ++globalIndex; // 为每个子节点设置 req
      //   //       return child; // 返回修改后的子节点
      //   //     });
      //   //   }
      //   //   return item;
      //   // });
      //   // console.log(tableData.value);
      // }

      return {
        formExtraAction,
        preCondition,
        querySort: (querys) => {
          for (let i = 0; i < querys.length; i++) {
            querys[i].span = 8;
            if (i >= 3) {
              querys[i].folding = true;
            }
          }
          console.log('afterSort', querys);
          return querys;
        },
      };
    },
  });
</script>
