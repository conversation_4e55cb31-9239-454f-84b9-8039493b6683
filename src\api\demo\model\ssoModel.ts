import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export type SysClientParams = {
  name?: string;
  url?: string;
};

export type SysClientVisitParams = {
  name?: string;
  url?: string;
};

export type SysClientPageParams = BasicPageParams & SysClientParams;

export type SysClientVisitPageParams = BasicPageParams & SysClientVisitParams;

export interface SysClientInfo {
  id: number;
  name: string;
  pos: string;
  logo: string;
  intro: string;
  allowUrl: string;
  status: number;
  isPublic: number;
  isFront: number;
  secretkey: string;
  ssoRedirect: string;
  type: string;
}

export interface SysClientListItem {
  id: number;
  name: string;
  pos: string;
  logo: string;
  intro: string;
  allowUrl: string;
  status: number;
  isPublic: number;
  isFront: number;
  secretkey: string;
  ssoRedirect: string;
  type: string;
}

export interface SysClientVisitInfo {
  id: number;
  clientId: number;
  userId: number;
  visit: number;
  sysClientName: string;
  sysClientLogo: string;
  sysUserAvatar: string;
}

export interface SysClientVisitListItem {
  id: number;
  clientId: number;
  userId: number;
  visit: number;
  sysClientName: string;
  sysClientLogo: string;
  sysUserAvatar: string;
}

export type SysClientListGetResultModel = BasicFetchResult<SysClientListItem>;

export type SysClientVisitListGetResultModel = BasicFetchResult<SysClientVisitListItem>;
