<script lang="tsx">
  import { defineComponent, onMounted, ref } from 'vue';
  import { getAllApi } from '/@/views/demo/event/api';
  import { formatDate } from '@vueuse/shared';

  export default defineComponent({
    methods: { formatDate },
    setup(props, { attrs, emit }) {
      const width = ref(1920);
      const cycle = ref(1);
      const all = ref([]);
      onMounted(() => {
        getAllApi().then((res) => {
          debugger;
          cycle.value = Math.ceil(res.length / 10);
          console.log(res.length, res.length / 10);
          width.value =
            res.length % 10 == 0 ? (res.length / 10) * 2080 : Math.ceil(res.length / 10) * 2080;

          all.value = res;
          console.log(all.value[0].year);
        });
      });

      return {
        cycle,
        width,
        all,
      };
    },
  });
</script>

<template>
  <div :style="{ width: '1620px', display: 'flex', flexDirection: 'row', overflowX: 'auto' }">
    <div style="width: 1620px" v-for="i in cycle">
      <div class="background" :style="{ width: 1620 + 'px' }">
        <div class="event" :style="{ width: 1620 + 'px' }">
          <div>
            <div v-if="all[(i - 1) * 10] !== undefined">
              <div class="year-05" :style="{ marginLeft: 88 + 'px', marginTop: '40px' }"
                >{{ all[(i - 1) * 10].year.substring(0, 4) }}年</div
              >
              <p class="year-05-note-line" :style="{ marginLeft: 88 + 'px', marginTop: '10px' }">{{
                all[(i - 1) * 10].description
              }}</p>
              <p class="year-05-note-km" :style="{ marginLeft: 88 + 'px', marginTop: '-20px' }"
                >开通 <strong>{{ all[(i - 1) * 10].mileage }}</strong> 公里</p
              >
              <div
                class="year-05-line"
                :style="{ marginLeft: 68 + 'px', marginTop: '-140px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 1] !== undefined">
              <div class="year-10" :style="{ marginLeft: 278 + 'px', marginTop: '-410px' }"
                >{{ all[(i - 1) * 10 + 1].year.substring(0, 4) }}年</div
              >
              <p class="year-10-note-line" :style="{ marginLeft: 278 + 'px', marginTop: '10px' }">{{
                all[(i - 1) * 10 + 1].description
              }}</p>
              <p class="year-10-note-km" :style="{ marginLeft: 278 + 'px', marginTop: '-20px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 1].mileage }}</strong> 公里</p
              >
              <div
                class="year-10-line"
                :style="{ marginLeft: 258 + 'px', marginTop: '-80px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 2] !== undefined">
              <div class="year-14" :style="{ marginLeft: 388 + 'px', marginTop: '-100px' }"
                >{{ all[(i - 1) * 10 + 2].year.substring(0, 4) }}年</div
              >
              <p class="year-14-note-line" :style="{ marginLeft: 388 + 'px', marginTop: '10px' }">{{
                all[(i - 1) * 10 + 2].description
              }}</p>
              <p class="year-14-note-km" :style="{ marginLeft: 388 + 'px', marginTop: '-20px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 2].mileage }}</strong> 公里</p
              >
              <div
                class="year-14-line"
                :style="{ marginLeft: 368 + 'px', marginTop: '-280px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 3] !== undefined">
              <div class="year-15" :style="{ marginLeft: 548 + 'px', marginTop: '-590px' }"
                >{{ all[(i - 1) * 10 + 3].year.substring(0, 4) }}年</div
              >
              <p class="year-15-note-line" :style="{ marginLeft: 548 + 'px', marginTop: '20px' }">{{
                all[(i - 1) * 10 + 3].description
              }}</p>
              <p class="year-15-note-km" :style="{ marginLeft: 548 + 'px', marginTop: '-20px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 3].mileage }}</strong> 公里</p
              >
              <div
                class="year-15-line"
                :style="{ marginLeft: 528 + 'px', marginTop: '-140px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 4] !== undefined">
              <div class="year-17" :style="{ marginLeft: 698 + 'px', marginTop: '-6px' }"
                >{{ all[(i - 1) * 10 + 4].year.substring(0, 4) }}年</div
              >
              <p class="year-17-note-line" :style="{ marginLeft: 698 + 'px', marginTop: '30px' }">{{
                all[(i - 1) * 10 + 4].description
              }}</p>
              <p class="year-17-note-km" :style="{ marginLeft: 698 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 4].mileage }}</strong> 公里</p
              >
              <div
                class="year-17-line"
                :style="{ marginLeft: 678 + 'px', marginTop: '-270px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 5] !== undefined">
              <div class="year-18" :style="{ marginLeft: 848 + 'px', marginTop: '-580px' }"
                >{{ all[(i - 1) * 10 + 5].year.substring(0, 4) }}年</div
              >
              <p class="year-18-note-line" :style="{ marginLeft: 848 + 'px', marginTop: '20px' }">{{
                all[(i - 1) * 10 + 5].description
              }}</p>
              <p class="year-18-note-km" :style="{ marginLeft: 848 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 5].mileage }}</strong> 公里</p
              >
              <div
                class="year-18-line"
                :style="{ marginLeft: 828 + 'px', marginTop: '-100px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 6] !== undefined">
              <div class="year-21" :style="{ marginLeft: 998 + 'px', marginTop: '-180px' }">{{
                all[(i - 1) * 10 + 6].year.substring(0, 4)
              }}</div>
              <p class="year-21-note-line" :style="{ marginLeft: 998 + 'px', marginTop: '20px' }">{{
                all[(i - 1) * 10 + 6].description
              }}</p>
              <p class="year-21-note-km" :style="{ marginLeft: 998 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 6].mileage }}</strong> 公里</p
              >
              <div
                class="year-21-line"
                :style="{ marginLeft: 978 + 'px', marginTop: '-275px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 7] !== undefined">
              <div class="year-22" :style="{ marginLeft: 1148 + 'px', marginTop: '-470px' }">{{
                all[(i - 1) * 10 + 7].year.substring(0, 4)
              }}</div>
              <p
                class="year-22-note-line"
                :style="{ marginLeft: 1148 + 'px', marginTop: '20px' }"
                >{{ all[(i - 1) * 10 + 7].description }}</p
              >
              <p class="year-22-note-km" :style="{ marginLeft: 1148 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 7].mileage }}</strong> 公里</p
              >
              <div
                class="year-22-line"
                :style="{ marginLeft: 1128 + 'px', marginTop: '-210px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 8] !== undefined">
              <div class="year-23" :style="{ marginLeft: 1298 + 'px', marginTop: '-120px' }">{{
                all[(i - 1) * 10 + 8].year.substring(0, 4)
              }}</div>
              <p
                class="year-23-note-line"
                :style="{ marginLeft: 1298 + 'px', marginTop: '20px' }"
                >{{ all[(i - 1) * 10 + 8].description }}</p
              >
              <p class="year-23-note-km" :style="{ marginLeft: 1298 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 8].mileage }}</strong> 公里</p
              >
              <div
                class="year-23-line"
                :style="{ marginLeft: 1278 + 'px', marginTop: '-280px' }"
              ></div>
            </div>
            <div v-if="all[(i - 1) * 10 + 9] !== undefined">
              <div class="year-24" :style="{ marginLeft: 1448 + 'px', marginTop: '-380px' }">{{
                all[(i - 1) * 10 + 9].year.substring(0, 4)
              }}</div>
              <p
                class="year-24-note-line"
                :style="{ marginLeft: 1448 + 'px', marginTop: '20px' }"
                >{{ all[(i - 1) * 10 + 9].description }}</p
              >
              <p class="year-24-note-km" :style="{ marginLeft: 1448 + 'px', marginTop: '-10px' }"
                >开通 <strong>{{ all[(i - 1) * 10 + 9].mileage }}</strong> 公里</p
              >
              <div
                class="year-24-line"
                :style="{ marginLeft: 1428 + 'px', marginTop: '-277px' }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .background {
    background-image: url('../../../assets/images/bg.png');
    background-repeat: repeat-x;
    height: 990px;
    border-radius: 0px 0px 0px 0px;
    display: flex;
    flex-direction: row;
  }

  .event {
    margin-top: 504.65px;
    background-repeat: repeat-x;
    height: 370.38px;
    width: 1625.24px;
    background-image: url('../../../assets/svg/event/img.svg');
  }

  .year-05 {
    position: relative;
    width: 230px;
    height: 39px;

    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-05-note-line {
    position: relative;
    width: 333px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-05-note-km {
    position: relative;

    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-10 {
    position: relative;
    width: 230px;
    height: 39px;
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-10-note-line {
    position: relative;

    width: 283px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-10-note-km {
    position: relative;
    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-14 {
    position: relative;
    width: 120px;
    height: 39px;
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-14-note-line {
    position: relative;
    width: 283px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-14-note-km {
    position: relative;

    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-15 {
    position: relative;
    margin-left: 700px;
    margin-top: -330px;
    width: 120px;
    height: 39px;
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-15-note-line {
    position: relative;
    margin-left: 640px;
    margin-top: -110px;
    width: 283px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-15-note-km {
    position: relative;
    margin-left: 640px;
    margin-top: -70px;

    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-17 {
    position: relative;
    margin-left: 640px;
    margin-top: 290px;
    width: 120px;
    height: 39px;
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-17-note-line {
    position: relative;
    margin-left: 640px;
    margin-top: 340px;
    width: 283px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-17-note-km {
    position: relative;
    margin-left: 640px;
    margin-top: 380px;

    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-18 {
    position: relative;
    margin-left: 740px;
    margin-top: -140px;
    width: 120px;
    height: 39px;
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-18-note-line {
    position: relative;
    margin-left: 740px;
    margin-top: -90px;
    width: 283px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-18-note-km {
    position: relative;
    margin-left: 740px;
    margin-top: -50px;

    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-21 {
    position: relative;
    margin-left: 940px;
    margin-top: 240px;
    width: 120px;
    height: 39px;
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-21-note-line {
    position: relative;
    margin-left: 940px;
    margin-top: 290px;
    width: 283px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-21-note-km {
    position: relative;
    margin-left: 940px;
    margin-top: 330px;

    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-22 {
    position: relative;
    margin-left: 1040px;
    margin-top: -190px;
    width: 120px;
    height: 39px;
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-22-note-line {
    position: relative;
    margin-left: 1040px;
    margin-top: -140px;
    width: 283px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-22-note-km {
    position: relative;
    margin-left: 1040px;
    margin-top: -100px;

    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-23 {
    position: relative;
    margin-left: 1260px;
    margin-top: 140px;
    width: 120px;
    height: 39px;
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-23-note-line {
    position: relative;
    margin-left: 1260px;
    margin-top: 190px;
    width: 283px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-23-note-km {
    position: relative;
    margin-left: 1260px;
    margin-top: 230px;

    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-24 {
    position: relative;
    margin-left: 1420px;
    margin-top: -190px;
    width: 120px;
    height: 39px;
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-24-note-line {
    position: relative;
    margin-left: 1420px;
    margin-top: -140px;
    width: 283px;
    height: 54px;
    font-size: 16px;
    color: #494949;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
  }

  .year-24-note-km {
    position: relative;
    margin-left: 1420px;
    margin-top: -100px;

    color: #494949;
    line-height: 12px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-05-line {
    position: relative;
    margin-left: 80px;
    margin-top: 9px;
    width: 120px;
    height: 300px;
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/line.png');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-10-line {
    position: relative;
    margin-left: 220px;
    margin-top: -70px;
    width: 120px;
    height: 300px;
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/line.png');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-14-line {
    position: relative;
    margin-left: 330px;
    margin-top: 10px;
    width: 120px;
    height: 300px;
    transform: rotateX(180deg);
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/line.png');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-15-line {
    position: relative;
    width: 120px;
    height: 500px;
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/<EMAIL>');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-17-line {
    position: relative;
    margin-left: 615px;
    margin-top: 125px;
    width: 120px;
    height: 300px;
    transform: rotateX(180deg);
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/line.png');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-18-line {
    position: relative;
    margin-left: 715px;
    margin-top: -100px;
    width: 120px;
    height: 500px;
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/<EMAIL>');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-21-line {
    position: relative;
    margin-left: 930px;
    margin-top: 55px;
    width: 120px;
    height: 300px;
    transform: rotateX(180deg);
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/line.png');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-22-line {
    position: relative;
    margin-left: 1015px;
    margin-top: -170px;
    width: 120px;
    height: 500px;
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/<EMAIL>');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-23-line {
    position: relative;
    margin-left: 1240px;
    margin-top: -65px;
    width: 120px;
    height: 300px;
    transform: rotateX(180deg);
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/line.png');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .year-24-line {
    position: relative;
    margin-left: 1400px;
    margin-top: -200px;
    width: 120px;
    height: 500px;
    background-repeat: no-repeat;
    background-image: url('../../../assets/images/<EMAIL>');
    font-size: 32px;
    color: #3b7ef0;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
</style>
