import {defineComponent} from 'vue';
import { ref} from 'vue';
import { VxeBasicTable, VxeGridInstance} from "/@/components/VxeTable";
import { TableAction} from "/@/components/Table";
const [ {openModal: openPreviewModal}] = useModal();
import {useMessage} from "/@/hooks/web/useMessage";
import { useForm} from '/@/components/Form/index';
import {useDrawer} from '/@/components/Drawer';
import {
    buildApi,
    tableQueryApi,
    batchRemove,
    save, update
} from "/@/api/erupt/erupt";
import { getColFomatter, getSearchComponent } from "/@/components/EruptTable/componets";
import {Tooltip,Tag} from "ant-design-vue";
import {Icon} from "/@/components/Icon";
import {BasicModal, useModal} from "/@/components/Modal";

export default defineComponent({
    name: 'eruptTable',
    props: {className: {}, preConditions: {default: []},  extraAction:{ default:{nums:0, actions: (row) => {return []}}}},
    setup(props, {emit, attrs}) {
        const {className,extraAction,preConditions} = props
        const [register, {openModal, closeModal}] = useModal();
        const [register1, {openModal:openModal1, closeModal:closeModal1}] = useModal();
        const tabs = ref([])
        const tableRef = ref<VxeGridInstance>();
        const powerRef = ref({})
        const tabValues = ref({})
        const uploadModalRef = ref()
        const columns = ref([]);
        const modalTitle = ref('')
        const showFooter = ref(true)
        const base = ref({id: '', version: ''})
        const [registerDrawer, {openDrawer, closeDrawer, setDrawerProps}] = useDrawer();
        const defaultSpaceRows = ref([])
        const addSchema = ref([])
        const editSchema = ref([])
        const lookSchema = ref([])
        const drawerWidth = ref('')

        const toolbarConfig = ref({
            buttons: [],
        })
        let querys = ref([]);
        const {createMessage} = useMessage();

        function refresh() {
            document.querySelector('button[content="查询"]').click()
        }

        function handleCancel() {
            resetFields()
        }
        async function handleBatchRemove() {
            const selectedRows = tableRef.value.getCheckboxRecords()
            let ids = []
            selectedRows.forEach(item => {
                ids.push(item.id)
            })
            const {status, message} = await batchRemove(className,ids)
            if (status == 'SUCCESS') {
                createMessage.success('删除成功')
                refresh()
            } else {
                createMessage.error('删除失败' + message)
            }
        }
        async function getFormData() {
            let values = await validate();
            return {...values, ...base.value,...tabValues.value}
        }
        async function handleSubmit() {
            try {
                let values = await validate();

                // setDrawerProps({confirmLoading: true});
                if (base.value.id) {
                    const data = {...values, ...base.value,...tabValues.value}
                    const {status, message} = await update(className, data, null)
                    if (status == 'SUCCESS') {
                        createMessage.success('保存成功')
                        resetFields
                        refresh()
                    } else {
                        createMessage.error('保存失败:' + message)
                    }
                } else {
                    const data= {...values,...tabValues.value}
                    const {status, message} = await save(className, data, null)
                    if (status == 'SUCCESS') {
                        createMessage.success('保存成功')
                        resetFields
                        refresh()
                    } else {
                        createMessage.error('保存失败:' + message)
                    }
                }
                // TODO custom api


                closeModal1();
                emit('success');
            } finally {
                // setDrawerProps({confirmLoading: false});
            }
        }

        function updateForm(schema) {
            updateSchema(schema)
        }
        function search(page, form) {
            debugger
            let condition = []
            preConditions.forEach(item => condition.push(item))
            for (let key in form) {
                if (form[key]) {
                    condition.push({key, value: form[key]})
                }
            }
            const query = {

                pageIndex: page.currentPage,
                pageSize: page.pageSize,
                condition
            }
            return tableQueryApi(className, query)
        }





        buildApi(className).then(res => {
            const {eruptModel:{eruptFieldModels,eruptJson},tabErupts,power } = res
            let cols = [{type: 'checkbox'}]
            let qs = []
            let edits = []
            let adds = []
            let details = []
            let tabItems = []
            let buttons = []
            powerRef.value = power
            drawerWidth.value = eruptJson.drawerWidth
            let tabV = {}
            eruptFieldModels.forEach(item => {
                const key = item.fieldName
                const title = item.eruptFieldJson.edit.title
                item.eruptFieldJson.views.forEach(v => {
                    if (v.show) {
                        cols.push(getColFomatter(key, v, item.eruptFieldJson.edit))
                    }
                })
                //  formState[key] = null
                if (item.eruptFieldJson.edit.search.value) {
                    qs.push(getSearchComponent(key,title,item,4,className))
                }

            })
            console.log('tabs',tabs.value,tabValues.value)
            qs.push({
                span: 4,
                align: 'right',
                className: '!pr-0',
                itemRender: {
                    name: 'AButtonGroup',
                    children: [
                        {
                            props: {type: 'primary', content: '查询', htmlType: 'submit'},
                            attrs: {class: 'mr-2'},
                        },
                        {props: {type: 'default', htmlType: 'reset', content: '重置'}},
                    ],
                },
            })

            let Awidth = 100
            Awidth += 60 * extraAction.nums
            debugger
            cols.push({
                title: '操作',
                align: 'center',
                width: Awidth,
                slots: {default: 'action'},
                fixed: 'right',
            },)
            querys.value = qs
            columns.value = cols
            toolbarConfig.value.buttons = buttons
            //gridOptions.columns = columns
            //search()
        })
        function getActions(row) {
            let actions = []
            extraAction.actions(row).forEach(action => {
                actions.push(action)
            })
            return <TableAction outside actions={actions}/>
        }
        return {
            tableRef,
            //createActions,
            columns,
            querys,
            register1,
            openModal1,
            closeModal1,
            toolbarConfig,
            search,
            registerDrawer,
            openDrawer,
            closeDrawer,
            modalTitle,
            showFooter,
            handleSubmit,
            setDrawerProps,
            handleCancel,
            defaultSpaceRows,
            uploadModalRef,
            tabs,
            tabValues,
            powerRef,
            getActions,
            drawerWidth,
            updateForm,
            getFormData,
            register,
            openModal,
            closeModal
        };
    },
    render() {


        return (
            <div>
                <VxeBasicTable ref="tableRef" columns={this.columns} formConfig={{
                    enabled: true,
                    items: this.querys,
                }} toolbarConfig={
                    this.toolbarConfig
                } columnConfig={{isCurrent: false, isHover: false}}
                               rowConfig={{isCurrent: false, isHover: false}} proxyConfig={{
                    ajax: {
                        query: async ({page, form}) => {
                            console.log(form)
                            return this.search(page, form);
                        },
                    },
                }}
                    // checked 为控制勾选的变量；
                    // defaultSpaceRows 为默认需要勾选的行；
                               v-slots={{
                                   action: (row) => {
                                       return this.getActions(row)
                                   },
                                   upload: (row,key) => {
                                       return (
                                           <div>
                                               <Tooltip placement={"bottom"}>
                                                   <a-button onClick={() => {
                                                       const files = row.row[key] ? row.row[key].split('|') : []
                                                       this.uploadModalRef.setFiles(files)
                                                       openPreviewModal()
                                                   }}>
                                                       <Icon icon="bi:eye"/>
                                                   </a-button>
                                               </Tooltip>

                                           </div>
                                       )

                                   },
                                   tags: (row,key) => {
                                       return (
                                           <div>
                                               {row.row[key] ? row.row[key].split(row.column.slots.joinSeparator).map(item => {
                                                   return <Tag>{item}</Tag>
                                               }) : ''}
                                           </div>
                                       )

                                   },
                               }
                               }>
                </VxeBasicTable>
            </div>
        );
    },
});
