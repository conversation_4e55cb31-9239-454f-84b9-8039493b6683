<template>
  <div>
    <vxe-toolbar ref="toolbarRef">
      <template #buttons>
        <vxe-button type="primary" @click="goBack">返回</vxe-button>
      </template>
    </vxe-toolbar>
    <vxe-grid v-bind="gridOptions"></vxe-grid>
<!--    <vxe-table
      border
      :column-config="{resizable: true}"
      :tree-config="{transform: true}"
      :data="tableData">
      <vxe-column field="parent" title="章号" tree-node></vxe-column>
      <vxe-column type="expand" width="80">
        <template #content="{ row }">
          <div class="expand-wrapper">
            <vxe-grid :editConfig="{
               trigger: 'dblclick',
               mode: 'cell'
            } " :columns="childCol" :data="row.childData">
              <template #active="{ row }">
                <vxe-button type="text" status="warning" @click="save(row)">保存</vxe-button>
                <vxe-button type="text" status="danger" @click="remove(row)">删除</vxe-button>
              </template>
            </vxe-grid>
          </div>
        </template>
      </vxe-column>
    </vxe-table>-->

    <BasicDrawer
      width="600px"
      @register="registerDrawer"
      :showFooter="true"
      title="请上传附件"
      @ok="handleSubmit"
    >
      <BasicForm style="height: 300px" @register="registerForm"></BasicForm>
    </BasicDrawer>
  </div>

</template>
<script lang="ts">
import {defineComponent, onMounted, ref} from 'vue';
import eruptTable from "../../../../components/EruptTable/eruptTable";
import {NColorPicker} from 'naive-ui'
import {useRoute, useRouter} from "vue-router";
import {VXETable} from "vxe-table";
import {
  getItemsApi,
  getOptionsApi,
  itemEdit,
  itemRemove
} from "/@/views/demo/system/assembly/api/assembly";
import {useMessage} from "/@/hooks/web/useMessage";
import {useDrawer} from "/@/components/Drawer";
import BasicModal from "/@/components/Modal/src/BasicModal.vue";
import BasicForm from "/@/components/Form/src/BasicForm.vue";
import {useForm} from "/@/components/Form";
import BasicDrawer from "/@/components/Drawer/src/BasicDrawer.vue";

export default defineComponent({
  name: 'AssemblyTemplate',
  components: {BasicDrawer, BasicForm, BasicModal, eruptTable, NColorPicker, VXETable},
  setup() {
    const route = useRoute();
    const router = useRouter();
    const [registerDrawer, {openDrawer, closeDrawer}] = useDrawer();

    const [registerForm, {resetFields, validate}] = useForm({
      labelWidth: 100,
      schemas: [{
        field: 'parent',
        label: '章号',
        required: true,
        component: 'Input',
        colProps: { span: 12 },
      },{
        field: 'chapter',
        label: '节号',
        required: true,
        component: 'Input',
        colProps: { span: 12 },
      },{
        field: 'provisions',
        label: '条文',
        required: true,
        component: 'InputTextArea',
        colProps: { span: 24 },
      },{
        field: 'stage',
        label: '落实阶段',
        required: true,
        component: 'Input',
        colProps: { span: 12 },
      },{
        field: 'responsibleUnit',
        label: '责任单位',
        required: true,
        component: 'Input',
        colProps: { span: 12 },
      },{
        field: 'cooperatingUnits',
        label: '配合单位',
        required: true,
        component: 'Input',
        colProps: { span: 12 },
      },],
      showActionButtonGroup: false,
    });
    const id = route.params?.id ?? -1;
    const row = route.query
    delete row._X_ROW_KEY
    delete row.key
    const loading = ref(false)
    const tableData = ref([])
    const {createMessage} = useMessage();
    const childCol = ref([
      {type: 'seq', title: '序号'},
      {field: 'parent', title: '编号', editRender: {name: 'AInput'}},
      {field: 'provisions', title: '条文', editRender: {name: 'ATextArea', props: {}} },
      {field: 'stage', title: '落实阶段', editRender: {name: 'AInput'}},
      {field: 'responsibleUnit', title: '责任单位', editRender: {name: 'AInput'}},
      {field: 'cooperatingUnits', title: '配合单位', editRender: {name: 'AInput'}},
      { title: '操作', slots: { default: 'active' } },
    ])

    const gridOptions = ref({
      border: true,
      treeConfig: {
        rowField: 'id',
        childrenField: 'children'
      },
      columns: [
        {field: 'chapter', title: '编号',treeNode: true, },
        {field: 'provisions', title: '条文'  },
        {field: 'stage', title: '落实阶段'},
        {field: 'responsibleUnit', title: '责任单位'},
        {field: 'cooperatingUnits', title: '配合单位'},
      ],
      data: tableData
    })

   /* const gridOptions = ref({
      border: true,
      treeConfig: {
        rowField: 'id',
        childrenField: 'childData'
      },
      columns: [
        {field: 'chapter', title: '编号',treeNode: true, editRender: {name: 'AInput'}},
        {field: 'provisions', title: '条文', editRender: {name: 'ATextArea', props: {}} },
        {field: 'stage', title: '落实阶段', editRender: {name: 'AInput'}},
        {field: 'responsibleUnit', title: '责任单位', editRender: {name: 'AInput'}},
        {field: 'cooperatingUnits', title: '配合单位', editRender: {name: 'AInput'}},
        /!*{ title: '操作', slots: { default: 'active' } },*!/
      ],
      data: tableData
    })*/
    onMounted(() => {
      getList()
      console.log(getItemsApi(id))
      //document.getElementsByClassName('vxe-form--wrapper')[0].style.display = 'none'

    })

    function goBack() {
      router.push('/newLine/assembly/assemblyTpl')
    }

    async function getList() {
      const res = await getItemsApi(id)
      console.log(res)
      tableData.value = res
    }

    return {
      add: () => {
        openDrawer()
      },
      save: (row) => {
        itemEdit(row).then(res => {
          if (res) {
            createMessage.success('保存成功')
          } else {
            createMessage.error('保存失败')
          }
        })
      },
      remove: (row) => {
        itemRemove(row.id).then(res => {
          if (res) {
            createMessage.success('删除成功')
          } else {
            createMessage.error('删除失败')
          }
        })
      },
      loading,
      tableData,
      childCol,
      getList,
      registerDrawer,
      openDrawer,
      closeDrawer,
      goBack,
      registerForm,
      gridOptions,
      handleSubmit: () => {

      }
    };
  },
});
</script>

