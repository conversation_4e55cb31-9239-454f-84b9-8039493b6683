<template>
  <div class="p-4">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-4 gap-4 mb-4">
      <Card>
        <div class="flex items-center">
          <div class="flex-1">
            <div class="text-secondary mb-2">总文档数</div>
            <div class="text-2xl font-bold">{{ fileCount }}</div>
          </div>
          <div class="stat-icon bg-indigo-50 p-3 rounded-lg">
            <FileTextOutlined class="text-xl text-indigo-600" />
          </div>
        </div>
      </Card>
      <Card>
        <div class="flex items-center">
          <div class="flex-1">
            <div class="text-secondary mb-2">总问题数</div>
            <div class="text-2xl font-bold">{{ questionCount }}</div>
          </div>
          <div class="stat-icon bg-rose-50 p-3 rounded-lg">
            <PlusCircleOutlined class="text-xl text-rose-600" />
          </div>
        </div>
      </Card>
      <Card>
        <div class="flex items-center">
          <div class="flex-1">
            <div class="text-secondary mb-2">下载次数</div>
            <div class="text-2xl font-bold">{{ downloadCount }}</div>
          </div>
          <div class="stat-icon bg-amber-50 p-3 rounded-lg">
            <EyeOutlined class="text-xl text-amber-600" />
          </div>
        </div>
      </Card>
      <Card>
        <div class="flex items-center">
          <div class="flex-1">
            <div class="text-secondary mb-2">知识分类</div>
            <div class="text-2xl font-bold">{{ directoryCount }}</div>
          </div>
          <div class="stat-icon bg-emerald-50 p-3 rounded-lg">
            <FolderOutlined class="text-xl text-emerald-600" />
          </div>
        </div>
      </Card>
    </div>

    <!-- 最近更新 -->
    <Card :bordered="false" class="mb-4">
      <template #title>
        <div class="flex items-center justify-between w-full">
          <span>搜索结果</span>
          <div class="flex items-center gap-4">
            <Input
              placeholder="搜索知识库..."
              class="!w-[300px]"
              v-model:value="searchText"
              @pressEnter="handleSearch"
              allowClear
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </Input>
            <Button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              搜索
            </Button>
            <Checkbox.Group v-model:value="searchTypes" class="flex items-center gap-4">
              <Checkbox value="question">问题</Checkbox>
              <Checkbox value="document">文档资料</Checkbox>
            </Checkbox.Group>
          </div>
        </div>
      </template>
      <div class="search-results">
        <div v-if="currentPageData.length === 0" class="text-center py-8">
          <Empty description="暂无搜索结果" />
        </div>
        <template v-else>
          <div
            v-for="item in currentPageData"
            :key="item.id"
            class="result-item p-4 hover:bg-gray-50"
          >
            <div class="flex justify-between items-start mb-2">
              <div class="flex items-center gap-2">
                <span
                  :class="[
                    'px-2 py-1 rounded text-sm',
                    item.module === 'question'
                      ? 'bg-indigo-50 text-indigo-700'
                      : 'bg-emerald-50 text-emerald-700',
                  ]"
                  >{{ item.module === 'question' ? '问题' : '文档' }}</span
                >
                <a
                  class="text-lg font-medium text-indigo-600 hover:text-indigo-800 cursor-pointer"
                  @click="item.module === 'document' ? handlePreview(item) : null"
                  >{{ item.title }}</a
                >
                <span :class="['px-2 py-1 rounded text-sm bg-amber-50 text-amber-700']">{{
                  item.category
                }}</span>
                <span class="px-2 py-1 rounded text-sm bg-cyan-50 text-cyan-700">{{
                  item.filetype
                }}</span>
              </div>
            </div>
            <div
              class="mt-2 text-gray-600 line-clamp-3"
              v-html="highlightText(item.content, searchText)"
            ></div>
            <div v-if="item.module === 'document'" class="mt-3">
              <Button type="primary" size="small" @click="handleDownload(item)">
                <template #icon><DownloadOutlined /></template>
                下载
              </Button>
            </div>
          </div>
          <div class="flex justify-center mt-4">
            <Pagination
              v-model:current="currentPage"
              :total="total"
              :pageSize="pageSize"
              show-quick-jumper
              show-size-changer
              :pageSizeOptions="['10', '20', '30', '40']"
              @change="handlePageChange"
              @showSizeChange="handleSizeChange"
            />
          </div>
        </template>
      </div>
    </Card>

    <BasicModal
      @register="registerModal"
      title="预览文件"
      :width="1000"
      :height="800"
      :fullscreen="true"
      :showFooter="false"
      :showCancelBtn="false"
      :showOkBtn="false"
      :closeFunc="async () => true"
    >
      <div style="height: 100vh">
        <iframe :src="previewUrl" width="100%" height="100%" frameborder="0"></iframe>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { Card, Input, Button, Checkbox, Pagination, message, Empty } from 'ant-design-vue';
  import {
    SearchOutlined,
    PlusOutlined,
    FileTextOutlined,
    PlusCircleOutlined,
    EyeOutlined,
    FolderOutlined,
    DownloadOutlined,
  } from '@ant-design/icons-vue';
  import {
    getFileDownload,
    getDirectoryCount,
    getFileCount,
    getQuestionCount,
    searchSolrQuery,
  } from './kbmanagement';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useUserStore } from '/@/store/modules/user';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Base64 } from 'js-base64';

  const searchText = ref('');
  const searchTypes = ref(['question', 'document']);
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(100);
  const downloadCount = ref(0);
  const directoryCount = ref(0);
  const fileCount = ref(0);
  const questionCount = ref(0);

  const [registerModal, { openModal }] = useModal();
  const previewUrl = ref<string>('');
  const userStore = useUserStore();
  const globSetting = useGlobSetting();
  const key = ref(0);

  // 获取下载次数
  const fetchDownloadCount = async () => {
    try {
      const count = await getFileDownload();
      downloadCount.value = count;
    } catch (error) {
      console.error('获取下载次数失败:', error);
    }
  };

  // 获取知识分类数量
  const fetchDirectoryCount = async () => {
    try {
      const count = await getDirectoryCount();
      directoryCount.value = count;
    } catch (error) {
      console.error('获取知识分类数量失败:', error);
    }
  };

  // 获取总文档数
  const fetchFileCount = async () => {
    try {
      const count = await getFileCount();
      fileCount.value = count;
    } catch (error) {
      console.error('获取总文档数失败:', error);
    }
  };

  // 获取总问题数
  const fetchQuestionCount = async () => {
    try {
      const count = await getQuestionCount();
      questionCount.value = count;
    } catch (error) {
      console.error('获取总问题数失败:', error);
    }
  };

  onMounted(() => {
    fetchDownloadCount();
    fetchDirectoryCount();
    fetchFileCount();
    fetchQuestionCount();
  });

  interface SearchResult {
    id: string;
    title: string;
    filetype: string;
    module: string;
    category: string;
    content: string;
  }

  // 搜索结果数据
  const searchResults = ref<SearchResult[]>([]);

  // 根据搜索词过滤结果
  const filteredResults = computed(() => {
    return searchResults.value;
  });

  // 高亮处理函数
  const highlightText = (text: string, keyword: string) => {
    if (!keyword || !keyword.trim()) return text;
    try {
      const words = keyword.trim().split(/\s+/);
      let highlightedText = text;

      words.forEach((word) => {
        const escapedWord = word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(${escapedWord})`, 'gi');
        highlightedText = highlightedText.replace(regex, '<mark class="highlight-text">$1</mark>');
      });

      return highlightedText;
    } catch (e) {
      return text;
    }
  };

  // 计算当前页显示的数据
  const currentPageData = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return filteredResults.value.slice(start, end);
  });

  // 搜索处理函数
  const handleSearch = async () => {
    try {
      const params = {
        search: searchText.value,
        module: searchTypes.value,
      };
      const results = await searchSolrQuery(params);
      searchResults.value = results;
      total.value = results.length;
      currentPage.value = 1;
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败，请稍后重试');
    }
  };

  // 监听搜索内容变化
  watch(searchText, (newVal) => {
    if (!newVal) {
      searchResults.value = [];
      total.value = 0;
    }
  });

  // 监听类型选择变化
  watch(searchTypes, () => {
    if (searchText.value) {
      handleSearch();
    }
  });

  const handleEdit = (record) => {
    console.log('编辑:', record);
  };

  const handleDelete = (record) => {
    console.log('删除:', record);
  };

  // 分页相关处理函数
  const handlePageChange = (page: number) => {
    currentPage.value = page;
  };

  const handleSizeChange = (current: number, size: number) => {
    pageSize.value = size;
    currentPage.value = 1; // 切换每页条数时重置为第一页
  };

  // 预览处理函数
  const handlePreview = (record) => {
    if (record.module === 'document') {
      //alert(record.id);
      const id = record.id.replace('#', '');
      const downloadUrl =
        globSetting.apiUrl + '/documentation/download/' + id + '?fullfilename=' + record.title;
      const watermarkTxt = userStore.userInfo?.username + ' ' + userStore.userInfo?.realName || '';
      const encodedUrl = encodeURIComponent(Base64.encode(downloadUrl));

      previewUrl.value = '';
      previewUrl.value =
        globSetting.previewUrl +
        '/onlinePreview?url=' +
        encodedUrl +
        '&watermarkTxt=' +
        watermarkTxt;
      key.value += 1;
      openModal(true);
    }
  };

  // 下载处理函数
  const handleDownload = (record) => {
    if (record.module === 'document') {
      const id = record.id.replace('#', '');
      const downloadUrl = globSetting.apiUrl + '/documentation/download/' + id;
      window.open(downloadUrl, '_blank');
      message.success('开始下载文档');
    }
  };
</script>

<style scoped>
  .result-item {
    transition: all 0.3s;
    position: relative;
  }

  .result-item:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 1px;
    background: rgba(0, 0, 0, 0.06);
  }

  .result-item + .result-item {
    margin-top: 0.5rem;
  }

  :deep(.highlight-text) {
    background-color: rgba(239, 68, 68, 0.2);
    padding: 0 2px;
    border-radius: 2px;
    font-style: normal;
    color: rgb(239, 68, 68);
    border-bottom: 1px solid rgba(239, 68, 68, 0.5);
  }

  .stat-icon {
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .stat-icon:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
</style>
