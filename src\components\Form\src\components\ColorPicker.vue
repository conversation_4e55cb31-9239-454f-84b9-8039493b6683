<template>
  <n-color-picker :swatches="[
      null,
      '#18A058',
      '#2080F0',
      '#F0A020',
      'rgba(208, 48, 80, 1)'
    ]" style="height:50px" v-model:value="selectValue"></n-color-picker>
</template>

<script lang="ts">
import {defineComponent, watch, ref } from 'vue';
import {NColorPicker} from 'naive-ui'
export default defineComponent({
  name: 'ColorSelect',
  components: { NColorPicker },
  props: {
    value:{type:String},
    options: {},
  },
  emits: ['change', 'update:value'],
  setup(props, { emit }) {
    const selectValue = ref('')
    watch(
      () => props.value,
      (val) => {
        if (val) {
          selectValue.value = val
        } else {
          selectValue.value = ''
        }
      },
      { deep: true },
    );

    watch(
      () => selectValue.value,
      (val) => {
        if (val) {
          emit('update:value', val);
          emit('change', val);
        }
      },
      { deep: true },
    );


    return { selectValue };
  },
});
</script>

<style scoped lang="less">

</style>
